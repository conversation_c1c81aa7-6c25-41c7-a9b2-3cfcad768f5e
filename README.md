# qiplus-firebase

## Instalação

```bash
# install firebase-cli
npm install -g firebase-tools

# Login firebase
firebase login

# Deploy firebase
firebase deploy --only functions

# Deploy
firebase.sh (para deploy no ambiente de dev)
firebase.sh env=prod (para deploy no ambiente de produção)
```

## Variáveis de Ambiente

O projeto utiliza variáveis de ambiente para configuração. Crie um arquivo `.env` na pasta `functions/` com base no arquivo `.env.example`.

### Configuração do Redis

```
REDIS_HOST=localhost       # Host do Redis
REDIS_PORT=6379            # Porta do Redis
REDIS_PASSWORD=senha       # Senha do Redis
REDIS_DATABASE=0           # Banco de dados do Redis
REDIS_TIMEOUT=10000        # Timeout de conexão em ms
```

### Configuração do Chat API

```
CHAT_API_URL=https://shotxv2dev.qi.plus  # URL base da API de chat
```

### Ambiente

```
NODE_ENV=development       # Ambiente (development, production)
```
