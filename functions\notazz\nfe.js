const CONSTANTS = require('../constants');

const { NOTAZZ_NFE_POSTBACK_URL } = CONSTANTS;

const nfe = {
    "DESTINATION_NAME": "",
    "DESTINATION_TAXID": "",
    "DESTINATION_IE": "",
    "DESTINATION_IM": "",
    "DESTINATION_TAXTYPE": "",
    "DESTINATION_STREET": "",
    "DESTINATION_NUMBER": "",
    "DESTINATION_COMPLEMENT": "",
    "DESTINATION_DISTRICT": "",
    "DESTINATION_CITY": "",
    "DESTINATION_UF": "",
    "DESTINATION_ZIPCODE": "",
    "DESTINATION_PHONE": "",
    "DESTINATION_EMAIL": "",
    "DESTINATION_EMAIL_SEND": null /* {
        "1": { "EMAIL": "" },
    } */,
    "DOCUMENT_BASEVALUE": "",
    "DOCUMENT_DESCRIPTION": "",
    "DOCUMENT_ISSUE_DATE": "",
    "DOCUMENT_CNAE": "",
    
    "DOCUMENT_GOAL": "1",                   // Finalidade da Nota Fiscal. 1 = Normal, 2 = Complementar, 3 = Ajuste, 4 = Devolução/Retorno
    "DOCUMENT_OPERATION_TYPE": "1",         // Tipo de Operação. 0 = Entrada, 1 = Saída
    "DOCUMENT_REFERENCED": "",              // Chave da nota fiscal referenciada. Utilizar quando DOCUMENT_GOAL for diferente de 1
    "DOCUMENT_NATURE_OPERATION": "VENDA",   // Natureza da operação da nota fiscal | ex: "VENDA"

    "DOCUMENT_PRODUCT": {
        1: {
            "DOCUMENT_PRODUCT_COD": "",
            "DOCUMENT_PRODUCT_TAX_COD": "",
            "DOCUMENT_PRODUCT_EAN": "",
            "DOCUMENT_PRODUCT_NAME": "",
            "DOCUMENT_PRODUCT_QTD": "",
            "DOCUMENT_PRODUCT_UNITARY_VALUE": "",
            "DOCUMENT_PRODUCT_NCM": "",
            "DOCUMENT_PRODUCT_CEST": "",
            "DOCUMENT_PRODUCT_CFOP": "",
            "DOCUMENT_PRODUCT_DISCOUNT": "0.00",
            "DOCUMENT_PRODUCT_ICMS_CST": "",
            "DOCUMENT_PRODUCT_IPI_CST": "",
            "DOCUMENT_PRODUCT_PIS_CST": "",
            "DOCUMENT_PRODUCT_COFINS_CST": "",
            "DOCUMENT_PRODUCT_ICMS_ALIQUOTA": "0.00",
            "DOCUMENT_PRODUCT_IPI_ALIQUOTA": "0.00",
            "DOCUMENT_PRODUCT_PIS_ALIQUOTA": "0.00",
            "DOCUMENT_PRODUCT_COFINS_ALIQUOTA": "0.00",
            "DOCUMENT_PRODUCT_OTHER_EXPENSES": "0.00",
        }
    },
    "DOCUMENT_FRETE": {
        "DOCUMENT_FRETE_MOD": "9",          // 0 = Por conta do emitente, 1 = Por conta do destinatário / remetente, 2 = Por conta de terceiros, 9 = Sem frete
        "DOCUMENT_FRETE_VALUE": "0",
        "DOCUMENT_FRETE_TRANSPORTADORA": {
            "DOCUMENT_FRETE_TRANSPORTADORA_NAME": "",
            "DOCUMENT_FRETE_TRANSPORTADORA_TAXID": "",
            "DOCUMENT_FRETE_TRANSPORTADORA_IE": "",
            "DOCUMENT_FRETE_TRANSPORTADORA_STREET": "",
            "DOCUMENT_FRETE_TRANSPORTADORA_NUMBER": "",
            "DOCUMENT_FRETE_TRANSPORTADORA_DISTRICT": "",
            "DOCUMENT_FRETE_TRANSPORTADORA_CITY": "",
            "DOCUMENT_FRETE_TRANSPORTADORA_UF": "",
        },
        "DOCUMENT_FRETE_VEICULO": {
            "DOCUMENT_FRETE_VEICULO_PLACA": "",
            "DOCUMENT_FRETE_VEICULO_UF": "",
        },
        "DOCUMENT_FRETE_VOLUMES": {
            "DOCUMENT_FRETE_VOLUMES_QTD": "",
            "DOCUMENT_FRETE_VOLUMES_SPECIES": "",
            "DOCUMENT_FRETE_VOLUMES_NET_WEIGHT": "",
            "DOCUMENT_FRETE_VOLUMES_GROSS_WEIGHT": "",
        },
    },

    "LOGISTICS": "",                        // Chave de integração da logística já configurada pelo sistema web. Se encontra no menu Configurações > Logística > Editar
    "FREIGHT_MODE": "",                     // Modalidade do frete (ver valores disponíveis no anexo 1)
    "FREIGHT_QUOTE": "0",                    // Realzar cotação de frete. Para ativar informe a APIKEY da sua plataforma de cotação dentro de Configurações > Logística > Editar. Possíveis valores 1 = realizar cotação, 0 = não realizar cotação
    "DOCUMENT_ATTACHMENT": "",

    "EXTERNAL_ID": "",    
    "SALE_ID": "",    
    "REQUEST_ORIGIN": "QIPlus Sistemas",
    "REQUEST_RETURN": NOTAZZ_NFE_POSTBACK_URL,

}

module.exports = nfe;
