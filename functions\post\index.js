const {
  ROLES,
  CONSTANTS,
  COLLEC<PERSON>ONS,
  FirestoreRef,
  admin,
  helpers,
  moment,
  momentNow,
  langMessages,
} = require("../init");
const {
  isLoopableObject,
  generateLongTextKeywords,
  generateKeywords,
  replaceUndefined,
  matchIfNumber,
  generateKeywordsDocs,
  nowToISO,
} = helpers;

const {
  KEYWORDS_SUBCOLLECTIONS_MAP,
  KEYWORDS_SUBCOLLECTION_NAME,
  KEYWORDS_FIELDS_MAP,
} = COLLECTIONS;

const { MOMENT_ISO } = CONSTANTS;

const GLOBAL_STATE_CACHE_LIMIT = 50000;
const $GLOBALS = {};

$GLOBALS.state = {
  leadsIds: {},
  posts: {},
  add: (collection, post) => {
    const { state } = $GLOBALS;
    if (!state.posts[collection]) state.posts[collection] = [];
    state.posts[collection] = state.posts[collection].filter(
      (p) => p.ID !== post.ID
    );
    state.posts[collection].push(post);
    console.log(
      `added post to state > fetchListenedPosts > ${collection} > ${post.ID} | collection length: ${state.posts[collection].length}`
    );
  },
  set: (collection, post) => {
    const { state } = $GLOBALS;
    state.add(collection, post);
  },
  update: (collection, data, ID) => {
    const { state } = $GLOBALS;
    if (!state.posts[collection]) state.posts[collection] = [];
    let post = state.posts[collection].find((p) => p.ID === ID || data.ID);
    if (post) {
      console.log(
        `updated post in state > fetchListenedPosts > ${collection} > ${post.ID} | collection length: ${state.posts[collection].length}`
      );
      state.add(collection, { ...post, ...data });
    }
  },
  get: (collection, postId) => {
    const { state } = $GLOBALS;
    if (!state.posts[collection]) return null;
    const post = state.posts[collection].find((p) => p.ID === postId);
    post &&
      console.log(
        `got post from state > ${collection} > ${postId} | collection length: ${state.posts[collection].length}`
      );
    return post;
  },
  find: (collection, fn) => {
    const { state } = $GLOBALS;
    if (!state.posts[collection]) return null;
    const post = state.posts[collection].find(fn);
    post &&
      console.log(
        `found post in state > ${collection} | collection length: ${state.posts[collection].length}`
      );
    return post;
  },
  remove: (collection, postId) => {
    const { state } = $GLOBALS;
    if (state.posts[collection]) {
      postId = isLoopableObject(postId) ? postId.ID : postId;
      state.posts[collection] = state.posts[collection].filter(
        (p) => p.ID !== postId
      );
    }
  },
};

let funcCounter = 0;
// grabHereToCopyPaste
const updateCache = async (change, context) => {
  funcCounter++;
  if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
    console.error(
      "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
        funcCounter
    );
    return null;
  }

  const { before, after } = change;
  const { params } = context;

  const collection = params.collection;
  const docId = params.docId;
  const oldData = (before.exists && before.data()) || {};
  const newData = (after.exists && after.data()) || {};

  if (oldData.systemUpdate || newData.systemUpdate) {
    console.log(
      `systemUpdate on > ${
        oldData.systemUpdate ? "oldData" : "newData"
      } > funcCounter` + funcCounter
    );
    return null;
  }

  if (after.exists && COLLECTIONS.coreCollections.includes(collection)) {
    $GLOBALS.state.add(collection, { ...newData, ID: docId });
    console.log(
      `updateCache ${context.params.collection} funcCounter:`,
      funcCounter
    );
  } else if (!after.exists) {
    $GLOBALS.state.remove(collection, docId);
  }

  return null;
};

// grabHereToCopyPaste
const QueryBuilder = (query, callbackFn, onError) => {
  // console.log('query',query);

  const results = {
    query,
    startTime: new Date().getTime(),
  };

  let {
    db,
    order,
    orderKey,
    idKey,
    startKey,
    lastKey,
    endKey,
    equalKey,
    equalVal,
    filterKeys,
    filterMode,
    searchTerm,
    where,
    limit,
  } = query;

  order = query.order === "desc" ? "desc" : "asc";
  idKey = query.idKey || "ID";
  where =
    Array.isArray(query.where) && query.where.length ? query.where : false;

  const dbRef = FirestoreRef.collection(db);

  let dbRefQuery = dbRef;

  Array.isArray(where) &&
    where.forEach(
      (queryArgs) =>
        (dbRefQuery = dbRef.where(queryArgs[0], queryArgs[1], queryArgs[2]))
    );

  if (equalKey && equalVal) {
    dbRefQuery = dbRefQuery.where(equalKey, "==", equalVal);
  }

  if (searchTerm) {
    dbRefQuery = dbRefQuery.where("keywords", "array-contains", searchTerm);
  }

  if (orderKey) {
    dbRefQuery = dbRefQuery.orderBy(orderKey, order);

    if (lastKey) {
      dbRefQuery = dbRefQuery.startAfter(lastKey);
    } else if (startKey) {
      dbRefQuery = dbRefQuery.startAt(startKey);
    }

    if (endKey) {
      dbRefQuery = dbRefQuery.endAt(endKey);
    }
  } else {
    if (idKey && startKey) {
      dbRefQuery = dbRefQuery.where(idKey, ">", startKey);
    }

    if (idKey && endKey) {
      dbRefQuery = dbRefQuery.where(idKey, "<=", endKey);
    }
  }

  if (limit) {
    dbRefQuery = dbRefQuery.limit(
      Math.min(limit, CONSTANTS.FIRESTORE_MAX_LIMIT)
    );
  }

  if (typeof callbackFn === "function") {
    return dbRefQuery
      .get()
      .then((snapshot) => {
        let data = snapshot.docs
          .map((doc) => doc.data())
          .filter((doc) => {
            let isMatch = true;

            if (filterKeys) {
              const matches = {};
              Object.keys(filterKeys).forEach((filterKey) => {
                const filterVal = filterKeys[filterKey];
                const keyValue = filterKey in doc ? doc[filterKey] : "skipKey";
                if (keyValue !== "skipKey") {
                  const keyMatch =
                    keyValue &&
                    typeof keyValue === "object" &&
                    Object.keys(keyValue).length
                      ? Object.keys(keyValue).find((k) =>
                          matchIfNumber(keyValue[k], filterVal)
                        )
                      : Array.isArray(keyValue)
                        ? keyValue.find((v) => matchIfNumber(v, filterVal))
                        : matchIfNumber(keyValue, filterVal);
                  matches[filterKey] = keyMatch;
                } else {
                  matches[filterKey] = false;
                }
              });
              isMatch =
                filterMode === "or"
                  ? Object.keys(filterKeys).find((f) => matches[f])
                  : Object.keys(filterKeys).filter((f) => matches[f]).length ===
                    Object.keys(filterKeys).length;
            }

            return isMatch;
          });

        const result = {
          ...results,
          data,
          numChildren: snapshot.size,
          lastKey: jsonClone(data).pop()[orderKey || idKey],
          endTime: new Date().getTime(),
        };
        callbackFn(result);

        return result;
      })
      .catch((error) => {
        console.error();
        onError && onError();
      });
  }

  return dbRefQuery;
};

// grabHereToCopyPaste
const addScore = async (collection, ID, scoreDoc) => {
  try {
    return await FirestoreRef.collection(
      `${collection}/${ID}/${COLLECTIONS.SCORES_SUBCOLLECTION_NAME}`
    ).add(scoreDoc);
  } catch (error) {
    console.error("addScore > error", { error });
    return null;
  }
};

// grabHereToCopyPaste
const addNewLog = (data) => {
  const { collection, id } = data;

  return new Promise((res, rej) => {
    data = replaceUndefined(data);

    FirestoreRef.collection(collection)
      .doc(id)
      .collection(COLLECTIONS.LOGS_COLLECTION_NAME)
      .add(data)
      .then((result) => {
        if (result.id) {
          const dataId = result.id;
          const log = {
            ...data,
            id: dataId,
          };
          return res(log);
        }
        const err = "ERROR_ADDING_NEW_LOG";
        return rej(err);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const fetchSubCollection = (
  collection,
  docId,
  subcollection,
  where,
  limit,
  orderBy,
  order
) => {
  console.log("fetchSubCollection", {
    collection,
    docId,
    subcollection,
    where,
    limit,
    orderBy,
    order,
  });
  return new Promise((res, rej) => {
    let query = FirestoreRef.collection(collection)
      .doc(docId)
      .collection(subcollection);
    if (where) {
      where.forEach((w) => {
        query = query.where(w[0], w[1], w[2]);
      });
    }
    if (orderBy) {
      query = query.orderBy(orderBy, order || "desc");
    }
    if (limit && !isNaN(limit)) query = query.limit(limit);
    query
      .get()
      .then((snapshot) => {
        const posts = [];
        snapshot.forEach((doc) => posts.push(doc.data()));
        return res(posts);
      })
      .catch((error) => {
        console.error(error);
        return res([]);
      });
  });
};

// grabHereToCopyPaste
const fetchCollection = (collection, where, limit, orderBy, order) => {
  return new Promise((res, rej) => {
    let query = FirestoreRef.collection(collection);
    if (where) {
      console.log(
        "SHOTXCRON > FETCHCOLLECION >  FETCHLISTENEDPOSTS > where",
        where
      );
      where.forEach((w) => {
        query = query.where(w[0], w[1], w[2]);
      });
    }
    if (orderBy) {
      query = query.orderBy(orderBy, order || "asc");
    }
    if (limit && !isNaN(limit)) query = query.limit(limit);
    query
      .get()
      .then((snapshot) => {
        const posts = [];
        snapshot.forEach((doc) => {
          console.log("SHOTXCRON > FETCHCOLLECION > DOC", posts);
          const post = doc.data();
          console.log("SHOTXCRON > FETCHCOLLECION > DOC > DATA", posts);
          snapshot.size < GLOBAL_STATE_CACHE_LIMIT &&
            $GLOBALS.state.add(collection, post);
          posts.push(post);
        });
        console.log("SHOTXCRON > FETCHCOLLECION > POSTS", posts);
        return res(posts);
      })
      .catch((error) => {
        console.log("SHOTXCRON >FETCHCOLLECION > POSTS > ERR");
        console.error(error);
        return res([]);
      });
  });
};

// grabHereToCopyPaste
const fetchDoc = (path, createUnexistant) => {
  return new Promise(async (res, rej) => {
    let pathArr = Array.isArray(path) ? path : path.split("/");
    path = pathArr.join("/");

    if (pathArr.length % 2 !== 0) {
      if (createUnexistant) {
        return res(await FirestoreRef.collection(`${path}`).doc().get());
      }
      return res(null);
    }

    return FirestoreRef.doc(`${path}`)
      .get()
      .then(async (doc) => {
        if (doc.exists) {
          return res(doc);
        } else if (createUnexistant && pathArr.length % 2 === 0) {
          let colPath = pathArr.filter((p, i) => i < pathArr.length - 1);
          return res(
            await FirestoreRef.collection(`${colPath.join("/")}`)
              .doc()
              .get()
          );
        }
        return res(null);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const resolvePost = (collection, postId, fromCache) => {
  return new Promise((res) =>
    fetchPost(collection, postId, fromCache)
      .then(res)
      .catch((err) => console.log("resolvePost > err", { err }) || res(null))
  );
};

// grabHereToCopyPaste
const fetchPost = (collection, postId, fromCache) => {
  return new Promise((res, rej) => {
    console.log(`EVALUATE_LISTENERS > FETCH_POST > START:`, {
      fromCache,
      collection,
      postId,
    });
    if (fromCache === true && $GLOBALS.state.get(collection, postId)) {
      return res($GLOBALS.state.get(collection, postId));
    }
    return FirestoreRef.collection(collection)
      .doc(postId)
      .get()
      .then((doc) => {
        if (doc.exists) {
          const post = doc.data();
          $GLOBALS.state.add(collection, post);
          console.log(`EVALUATE_LISTENERS > FETCH_POST > RESULT:`, {
            post,
            collection,
            postId,
            fromCache,
          });
          return res(post || {});
        }
        const err = `ERROR_FETCHING_${collection}_${postId}`;
        return rej(err);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const addNewPost = (collection, newPost) => {
  return new Promise((res, rej) => {
    newPost = replaceUndefined(newPost);
    FirestoreRef.collection(collection)
      .add(newPost)
      .then((result) => {
        if (result.id) {
          const newPostId = result.id;
          const post = {
            ...newPost,
            ID: newPostId,
          };
          $GLOBALS.state.add(collection, post);
          return res(post);
        }
        const err = "ERROR_ADDING_NEW_POST";
        return rej(err);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const updateRef = (docRef, data) =>
  new Promise(async (res) => {
    let updatedRef = docRef && docRef.get ? await docRef.get() : null;
    if (updatedRef && updatedRef.exists) {
      return docRef.update(data).then((r) =>
        res({
          ...updatedRef.data(),
          ...data,
        })
      );
    }
    return res(null);
  });

// grabHereToCopyPaste
const updatePost = (collection, updatedPost, ID) => {
  return new Promise((res, rej) => {
    updatedPost = replaceUndefined(updatedPost);
    const postId = ID || updatedPost.ID;
    FirestoreRef.collection(collection)
      .doc(`${postId}`)
      .update(updatedPost)
      .then((result) => {
        $GLOBALS.state.update(collection, updatedPost, postId);
        return res(updatedPost);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const savePostFields = (collection, postId, updatedPostData) => {
  return new Promise((res, rej) => {
    updatedPostData = replaceUndefined(updatedPostData);
    FirestoreRef.collection(collection)
      .doc(`${postId}`)
      .update(updatedPostData)
      .then((result) => {
        $GLOBALS.state.update(collection, updatedPostData, postId);
        return res(updatedPostData);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const replacePost = (collection, updatedPost, ID) => {
  return new Promise((res, rej) => {
    updatedPost = replaceUndefined(updatedPost);
    const postId = ID || updatedPost.ID;
    FirestoreRef.collection(collection)
      .doc(`${postId}`)
      .set(updatedPost)
      .then((result) => {
        $GLOBALS.state.set(collection, updatedPost);
        return res(updatedPost);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const deletePost = (collection, ID) => {
  return new Promise((res, rej) => {
    FirestoreRef.collection(collection)
      .doc(`${ID}`)
      .delete()
      .then((result) => {
        $GLOBALS.state.remove(collection, ID);
        return res(ID);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const deleteCollectionPosts = (collection, where) => {
  return new Promise((res, rej) => {
    let query = FirestoreRef.collection(collection);
    where.forEach((w) => {
      query = query.where(w[0], w[1], w[2]);
    });
    query
      .get()
      .then((snapshot) => {
        const promises = [];
        snapshot.forEach((doc) => {
          const { ID } = doc.data();
          promises.push(doc.ref.delete());
          $GLOBALS.state.remove(collection, ID);
        });
        return Promise.all(promises);
      })
      .then((r) => {
        return res(r.length);
      })
      .catch((error) => {
        console.error(error);
        return res(0);
      });
  });
};

// grabHereToCopyPaste
const trashPost = (collection, post, oldPost) => {
  const oldStatus = (oldPost || {}).status;
  const newStatus = post.status;
  const postId = post.ID;

  if (
    postId &&
    newStatus === CONSTANTS.TRASH_STATUS &&
    COLLECTIONS.coreCollections.find((c) => c === collection)
  ) {
    const trashSubCollection = FirestoreRef.collection(
      COLLECTIONS.TRASH_COLLECTION_NAME
    )
      .doc("posts")
      .collection(collection);

    post.trashed = nowToISO();

    return trashSubCollection
      .doc(`${postId}`)
      .set(post)
      .then(() => {
        $GLOBALS.state.remove(collection, post);
        return FirestoreRef.collection(collection).doc(`${postId}`).delete();
      });
  }

  return null;
};

// grabHereToCopyPaste
const getAccountByOwner = (ownerId, fromCache) => {
  return new Promise((res, rej) => {
    const collection = COLLECTIONS.ACCOUNTS_COLLECTION_NAME;
    if (ownerId === CONSTANTS.ORPHANS_OWNER) {
      return res(null);
    }
    if (fromCache === true) {
      let account = $GLOBALS.state.find(collection, (p) => p.owner === ownerId);
      if (account) return res(account);
    }
    return FirestoreRef.collection(collection)
      .where(CONSTANTS.OWNER_FIELD, "==", `${ownerId}`)
      .limit(1)
      .get()
      .then(
        (snapshot) => {
          let account = {};
          if (snapshot.size) {
            snapshot.forEach((doc) => {
              account = doc.data();
            });
            $GLOBALS.state.add(collection, account);
            return res(account);
          }
          return res(null);
        },
        (error) => {
          console.error(error);
          return res(null);
        }
      );
  });
};

// grabHereToCopyPaste
const getStores = (callbackFn) => {
  if ($GLOBALS.stores && Object.keys($GLOBALS.stores).length) {
    callbackFn($GLOBALS.stores);
    return;
  }

  var storesRef = FirestoreRef.collection("stores");

  storesRef
    .get()
    .then((snapshot) => {
      const stores = {};
      console.log("numChildren", snapshot.size);
      snapshot.forEach((doc) => {
        const store = doc.data();
        if (!store.accountId) {
          console.log("------------- START ERRRR ------------------");
          console.log("store", { id: store.ID, title: store.title });
          console.log("------------- END ERRRR ------------------");
        } else {
          stores[store.ID] = store;
          // console.log('store', { id: store.ID, title: store.title, accountId: store.accountId });
          // console.log('fin ------------------');
        }
      });

      $GLOBALS.stores = stores;
      callbackFn(stores);

      return;
    })
    .catch(console.log);
};

// grabHereToCopyPaste
const fetchTaxonomy = (taxName, collection, taxId) => {
  return new Promise((res, rej) => {
    FirestoreRef.collection(COLLECTIONS.TAXONOMIES_COLLECTION_NAME)
      .doc(collection)
      .collection(taxName)
      .doc(taxId)
      .get()
      .then((doc) => {
        if (doc.exists) {
          const taxonomy = doc.data();
          return res(taxonomy || {});
        }
        const err = `ERROR_FETCHING_${taxName}`;
        return rej(err);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const migrateSubCollections = (docPath, newDocPath, exclusions, exceptions) =>
  new Promise(async (res) => {
    const promises = [];
    const subcollections = await admin
      .firestore()
      .doc(docPath)
      .listCollections();

    if (Array.isArray(subcollections)) {
      const subcollectionList = subcollections.map((col) => col.id);
      subcollectionList.forEach((subColName) => {
        if (Array.isArray(exclusions) && exclusions.includes(subColName)) {
          promises.push(deleteSubCollection(docPath, subColName));
        } else if (
          !Array.isArray(exceptions) ||
          !exceptions.includes(subColName)
        ) {
          promises.push(migrateSubCollection(docPath, subColName, newDocPath));
        }
      });
      console.log("onDeleteDocs > subcollectionList", subcollectionList);
    }

    return Promise.all(promises)
      .then((r) => res(true))
      .catch((r) => res(false));
  });

// grabHereToCopyPaste
const migrateSubCollection = async (docPath, subColName, newDocPath) =>
  new Promise((res) => {
    let i = 0,
      sBatch = FirestoreRef.batch();
    const docRef = FirestoreRef.doc(docPath);
    const newDocRef = FirestoreRef.doc(newDocPath);
    const newColRef = newDocRef.collection(subColName);

    return docRef
      .collection(subColName)
      .get()
      .then((subColSnap) => {
        subColSnap.forEach((sDoc) => {
          i++;
          let data = sDoc.data();
          if (i === 500) {
            sBatch.commit();
            sBatch = FirestoreRef.batch();
            i = 0;
          }
          newColRef.doc(sDoc.id).set(data);
          sBatch.delete(sDoc.ref);
        });
        try {
          return sBatch.commit();
        } catch (error) {
          console.error(error);
          return null;
        }
      })
      .then((r) => res(true))
      .catch((r) => res(false));
  });

// grabHereToCopyPaste
const deleteSubCollections = (docPath, exceptions) =>
  new Promise(async (res) => {
    const promises = [];
    const subcollections = await admin
      .firestore()
      .doc(docPath)
      .listCollections();

    if (Array.isArray(subcollections)) {
      const subcollectionList = subcollections.map((col) => col.id);
      subcollectionList.forEach((subColName) => {
        if (!Array.isArray(exceptions) || !exceptions.includes(subColName)) {
          promises.push(deleteSubCollection(docPath, subColName));
        }
      });
      console.log("onDeleteDocs > subcollectionList", subcollectionList);
    }

    return Promise.all(promises)
      .then((r) => res(true))
      .catch((r) => res(false));
  });

// grabHereToCopyPaste
const deleteSubCollection = async (docPath, subColName) =>
  new Promise((res) => {
    let i = 0,
      sBatch = FirestoreRef.batch();
    const docRef = FirestoreRef.doc(docPath);

    return docRef
      .collection(subColName)
      .get()
      .then((subColSnap) => {
        subColSnap.forEach((sDoc) => {
          i++;
          if (i === 500) {
            sBatch.commit();
            sBatch = FirestoreRef.batch();
            i = 0;
          }
          sBatch.delete(sDoc.ref);
        });
        try {
          return sBatch.commit();
        } catch (error) {
          console.error(error);
          return null;
        }
      })
      .then((r) => res(true))
      .catch((r) => res(false));
  });

// grabHereToCopyPaste
const updateKeywordsSubcollection = async (doc) => {
  // --------------------------------
  // KEYWORDS SUBCOLLECTION
  // keywords fields to subcollection
  // --------------------------------

  if (!doc.exists) {
    return null;
  }

  const docRef = doc.ref;
  const post = doc.data();
  const docId = doc.id;

  let { collection, owner, accountId } = post;

  // keywords
  let keywordsSource;

  const inheritedData = {
    docRef,
    docId,
    owner,
    accountId,
  };

  switch (collection) {
    case COLLECTIONS.DEALS_COLLECTION_NAME:
      var contactId = post.contactId || "";

      var lead = {};
      if (contactId) {
        try {
          lead = await fetchPost(COLLECTIONS.LEADS_COLLECTION_NAME, contactId);
          inheritedData.contactRef = FirestoreRef.collection(
            COLLECTIONS.LEADS_COLLECTION_NAME
          ).doc(contactId);
        } catch (error) {
          console.log("updateKeywordsSubcollection > err", { error });
        }
      }

      var mergedContact = {
        ...(lead || {}),
        ...(post.contact || {}),
      };

      Object.keys(mergedContact).forEach((field, i) => {
        const val = mergedContact[field];
        if (lead[field] && val === "") {
          mergedContact[field] = lead[field];
        }
      });

      keywordsSource = {
        ...mergedContact,
        ...post,
      };

      break;
    default:
      keywordsSource = { ...post };
      break;
  }

  const keywordsCollection =
    KEYWORDS_SUBCOLLECTIONS_MAP[collection] || KEYWORDS_SUBCOLLECTION_NAME;

  const keywordDocs = generateKeywordsDocs(keywordsSource);

  let keywordsBatch = FirestoreRef.batch();

  keywordDocs.forEach((keywordDoc) => {
    keywordDoc = {
      ...keywordDoc,
      ...inheritedData,
    };
    let { index } = keywordDoc;
    if (index) {
      let newDoc = replaceUndefined(keywordDoc);
      let kDocRef = docRef.collection(keywordsCollection).doc(index);
      keywordsBatch.set(kDocRef, newDoc);
    }
  });

  return keywordsBatch.commit().catch((err) => {
    console.error("updateKeywordsSubcollection > err", { err });
    console.log("updateKeywordsSubcollection > err > data", {
      err,
      keywordDocs,
    });
  });
};

// grabHereToCopyPaste
const updateStoreUsers = (userData, oldData) => {
  if (
    !userData.ID ||
    !Array.isArray(userData.roles) ||
    !Array.isArray(userData.stores)
  )
    return;

  const userId = userData.ID;

  const addedStores =
    !oldData || !Array.isArray(oldData.stores)
      ? userData.stores
      : [...new Set(userData.stores)].filter(
          (sId) => !oldData.stores.includes(sId)
        );
  const excludedStores =
    Array.isArray(oldData.stores) &&
    [...new Set(oldData.stores)].filter(
      (sId) => !userData.stores.includes(sId)
    );

  Array.isArray(addedStores) &&
    addedStores.forEach((storeId) => {
      const storeRef = FirestoreRef.collection("stores")
        .doc(`${storeId}`)
        .get()
        .then((doc) => {
          let store = null;
          if (doc.exists) {
            store = doc.data();
            let changes = 0;

            userData.roles.forEach((role) => {
              const roleKey = ROLES.QIPLUS_ROLES_PLURAL[role];

              const usersInRole = store[roleKey] || [];
              const indexOf = usersInRole.indexOf(userId);

              if (indexOf === -1) {
                usersInRole.push(userId);
                store[roleKey] = usersInRole;
                changes++;
              }
            });

            changes && doc.ref.update(store);
            // console.log('updateStoreUsers > store', store);
          }
          return store;
        })
        .catch((error) => {
          console.error("error", error);
        });
    });

  Array.isArray(excludedStores) &&
    excludedStores.forEach((storeId) => {
      const storeRef = FirestoreRef.collection("stores")
        .doc(`${storeId}`)
        .get()
        .then((doc) => {
          let store = null;
          if (doc.exists) {
            store = doc.data();
            let changes = 0;

            userData.roles.forEach((role) => {
              const roleKey = ROLES.QIPLUS_ROLES_PLURAL[role];

              const usersInRole = store[roleKey] || [];
              const indexOf = usersInRole.indexOf(userId);

              if (indexOf >= 0) {
                usersInRole.splice(indexOf, 1);
                store[roleKey] = usersInRole;
                changes++;
              }
            });

            changes && doc.ref.update(store);
            // console.log('updateStoreUsers > store', store);
          }
          return store;
        })
        .catch((error) => {
          console.error("error", error);
        });
    });
};

let getLeadsTimeout;
let getLeadsStack = [];
// grabHereToCopyPaste
const getLeads = (callbackFn) => {
  getLeadsStack.push(callbackFn);
  clearTimeout(getLeadsTimeout);
  getLeadsTimeout = setTimeout(() => {
    FirestoreRef.collection("leads")
      .get()
      .then((snapshot) => {
        const leads = {};
        snapshot.forEach((doc) => {
          leads[doc.id] = doc.data();
        });
        if (Object.keys(leads).length) {
          getLeadsStack.forEach((fn) => {
            typeof fn === "function" && fn(leads);
          });
          getLeadsStack = [];
        }
        return leads;
      })
      .catch((error) => {
        console.error("error", error);
      });
  }, CONSTANTS.timer_1_Second);
};

const getSegmentationsByIds = async (segmentationIds) => {
  console.log("SHOTXCRON > getSegmentationsByIds", segmentationIds);

  return await FirestoreRef.collection("segmentations")
    .where("id", "in", segmentationIds)
    .get()
    .then((snapshot) => {
      const segmentations = {};
      snapshot.forEach((doc) => {
        segmentations[doc.id] = doc.data();
      });
      return segmentations;
    })
    .catch((error) => {
      console.error("error", error);
    });
};

const getLeadById = async (leadId) => {
  return await FirestoreRef.collection(COLLECTIONS.LEADS_COLLECTION_NAME)
    .doc(leadId)
    .get()
    .then((doc) => {
      if (doc.exists) {
        return doc.data();
      }
      return null;
    });
};

// grabHereToCopyPaste
const getAccountLeadsIds = (accountId, fromCache) => {
  return new Promise(async (res) => {
    if (fromCache !== false && $GLOBALS.state.leadsIds[accountId]) {
      return res($GLOBALS.state.leadsIds[accountId]);
    }

    let accountLeadsRef = await FirestoreRef.collectionGroup(
      COLLECTIONS.KEYWORDS_SUBCOLLECTIONS_MAP[COLLECTIONS.LEADS_COLLECTION_NAME]
    )
      .where("field", "==", "accountId")
      .where("accountId", "==", accountId)
      .where("keywords", "array-contains", accountId)
      .orderBy("updatedAt", "desc")
      .get();

    const accountLeadsDocs = accountLeadsRef.docs.map((d) => d.data());
    const accountLeadsIds = accountLeadsDocs.map((kDoc) => kDoc.docId);

    $GLOBALS.state.leadsIds[accountId] = accountLeadsIds;
    return res(accountLeadsIds);
  });
};

// grabHereToCopyPaste
const buildPages = (query, callbackFn) => {
  const results = {
    query,
    fetchedPages: {},
    startTime: new Date().getTime(),
  };
  const pages = [];
  let children = [];
  let lastKey = "";

  const db = query.db;
  const page = query.page;
  const order = query.order === "desc" ? "desc" : "asc";
  const orderKey = query.orderKey;
  const orderBy = query.orderBy;
  const idKey = query.idKey || "id";
  const startKey = query.startKey;
  const endKey = query.endKey;
  const equalKey = query.equalKey;
  const equalVal = query.equalVal;
  const filterKeys =
    query.filterKeys &&
    typeof query.filterKeys === "object" &&
    Object.keys(query.filterKeys).length
      ? query.filterKeys
      : false;
  const filterMode = query.filterMode || "and";
  const perpage = (query.perpage && parseInt(query.perpage, 10)) || 50;
  const where =
    Array.isArray(query.where) && query.where.length ? query.where : false;
  const limit = query.limit;
  const fetchPages = Array.isArray(query.fetchPages)
    ? query.fetchPages
    : !isNaN(query.fetchPages)
      ? [query.fetchPages]
      : false;

  const dbRef = FirestoreRef.collection(db);

  let dbRefQuery = dbRef;

  Array.isArray(where) &&
    where.forEach(
      (queryArgs) =>
        (dbRefQuery = dbRef.where(queryArgs[0], queryArgs[1], queryArgs[2]))
    );

  if (equalKey && equalVal) {
    dbRefQuery = dbRefQuery.where(equalKey, "==", equalVal);
  }

  if (idKey && startKey) {
    dbRefQuery = dbRefQuery.where(idKey, ">", startKey);
  }

  if (idKey && endKey) {
    dbRefQuery = dbRefQuery.where(idKey, "<=", endKey);
  }

  if (orderKey) {
    dbRefQuery = dbRefQuery.orderBy(orderKey, order);
  }

  if (limit) {
    dbRefQuery = dbRefQuery.limit(
      Math.min(limit, CONSTANTS.FIRESTORE_MAX_LIMIT)
    );
  }

  dbRefQuery
    .get()
    .then((snapshot) => {
      results.numChildren = snapshot.size;

      const dbChildren = [];
      let childrenKeys = [];

      snapshot.forEach((doc) => {
        const dbElem = doc.data();
        const dbKey = doc.id;

        lastKey = dbKey;

        let isMatch = true;
        if (filterKeys) {
          const matches = {};
          Object.keys(filterKeys).forEach((filterKey) => {
            const filterVal = filterKeys[filterKey];
            const keyValue =
              filterKey in dbElem ? dbElem[filterKey] : "skipKey";
            if (keyValue !== "skipKey") {
              const keyMatch =
                keyValue &&
                typeof keyValue === "object" &&
                Object.keys(keyValue).length
                  ? Object.keys(keyValue).find((k) =>
                      matchIfNumber(keyValue[k], filterVal)
                    )
                  : Array.isArray(keyValue)
                    ? keyValue.find((v) => matchIfNumber(v, filterVal))
                    : matchIfNumber(keyValue, filterVal);
              matches[filterKey] = keyMatch;
            } else {
              matches[filterKey] = false;
            }
          });
          isMatch =
            filterMode === "or"
              ? Object.keys(filterKeys).find((f) => matches[f])
              : Object.keys(filterKeys).filter((f) => matches[f]).length ===
                Object.keys(filterKeys).length;
        }
        if (isMatch) {
          children.push(dbElem);
        }

        dbChildren.push(dbElem);
      });

      if (orderKey && children.length) {
        children.sort((a, b) => {
          return a[orderKey] && b[orderKey]
            ? a[orderKey] > b[orderKey]
              ? order !== "asc"
                ? -1
                : 1
              : order !== "asc"
                ? 1
                : -1
            : 0;
        });
      }

      if (limit && filterKeys && children.length) {
        children = children.filter((dbKey, count) => count < limit);
      }

      childrenKeys = children.map((c) => c.ID || c.id);

      // pagination
      let page = [];

      childrenKeys.forEach((dbKey, count, arr) => {
        page.push(dbKey);
        if (page.length === perpage || count === arr.length - 1) {
          pages.push(page);
          page = [];
        }
      });

      fetchPages &&
        fetchPages
          .filter((p) => (p < 0 ? pages[pages.length + p] : pages[p - 1]))
          .forEach((p) => {
            const arrayIndex = p < 0 ? pages.length + p : p - 1;
            const pageNum = arrayIndex + 1;
            results.fetchedPages[pageNum] = pages[arrayIndex].map((dbKey) =>
              children.find((c) => (c.ID || c.id) === dbKey)
            );
          });

      results.lastKey = lastKey;
      results.numResults = children.length;
      results.numKeys = childrenKeys.length;
      results.numPages = pages.length;
      results.pages = pages;
      results.endTime = new Date().getTime();

      // console.log('results', results);

      typeof callbackFn === "function" && callbackFn(results);

      return dbChildren;
    })
    .catch((error) => {
      console.error("error", error);
    });
};

// grabHereToCopyPaste
const internalLog = (fnName, error, docData) => {
  console.error(`${fnName} > error`, { error });
  console.error(`${fnName} > type of error ${typeof error}`);

  let logData = {};

  if (error && error.toString) {
    logData.toStr = error.toString();
  }

  let props = ["name", "stack", "message", "error"];

  props.forEach((p) => {
    try {
      if (error && error[p] && typeof error[p] === "string")
        logData[p] = error[p];
    } catch (err) {
      () => {};
    }
  });

  try {
    let s = JSON.stringify(error);
    if (s !== "{}") logData.s = s;
  } catch (err) {
    () => {};
  }

  console.log(`${fnName} > log > logData`, { logData });

  const errorLog = {
    logData,
    docData: docData || "",
    fnName,
    createdAt: momentNow().valueOf(),
    date: nowToISO(),
  };

  return FirestoreRef.collection(COLLECTIONS.INTERNAL_LOGS_COLLECTION_NAME)
    .doc("functions")
    .collection(fnName)
    .add(errorLog);
};

module.exports = {
  $GLOBALS,
  updateCache,
  QueryBuilder,
  addScore,
  addNewLog,
  fetchCollection,
  fetchSubCollection,
  migrateSubCollections,
  migrateSubCollection,
  deleteSubCollections,
  deleteSubCollection,
  fetchDoc,
  resolvePost,
  fetchPost,
  addNewPost,
  updateRef,
  updatePost,
  replacePost,
  savePostFields,
  deletePost,
  deleteCollectionPosts,
  trashPost,
  getStores,
  getAccountByOwner,
  fetchTaxonomy,
  updateKeywordsSubcollection,
  updateStoreUsers,
  getLeads,
  getSegmentationsByIds,
  getLeadById,
  getAccountLeadsIds,
  buildPages,
  internalLog,
};
