const {
  ROLES,
  CONSTANTS,
  COLLECTIONS,
  FirestoreRef,
  helpers,
  models,
  cors,
  moment,
  momentNow,
  langMessages,
  ERROR_TYPES,
} = require("../init");
const {
  addScore,
  addNewLog,
  resolvePost,
  fetchPost,
  addNewPost,
  updatePost,
  savePostFields,
  fetchCollection,
  fetchSubCollection,
  getAccountLeadsIds,
  fetchTaxonomy,
} = require("../post");
const { createNotazzNF } = require("../notazz");
const {
  isLoopableObject,
  convertTonumOnly,
  replaceUndefined,
  filterRecursively,
  areEqualObjects,
  diffObjects,
  jsonClone,
  nowToISO,
} = helpers;
const { getScheduledMoment } = require("../notifications");
const { replaceShortCodes } = require("../shortcodes");

const axios = require("axios");
const qs = require("qs");
const nfeModel = require("../notazz/nfe");
const nfseModel = require("../notazz/nfse");
const leadWebhook = require("../models/leadWebhook");
const dealWebhook = require("../models/dealWebhook");
const {
  LEADS_COLLECTION_NAME,
  SHOTX_COLLECTION_NAME,
} = require("../constants/collections");
const { checkPhoneOrMobile } = require("../utils/phoneUtils");
const { sendMessage } = require("../helpers/shotxHelper");

const { COLLECTION_TRIGGERS } = COLLECTIONS;

const {
  CRONJOB_TYPES,
  MOMENT_ISO,
  MOMENT_LOCAL,
  MOMENT_SHORT,
  APP_TRIGGERS,
  APP_ACTIONS,
} = CONSTANTS;

// grabHereToCopyPaste
const checkActionsLoopDanger = (actions, triggers) => {
  let loopDanger = false;

  if (!loopDanger && Array.isArray(triggers)) {
    triggers.forEach((t) => {
      if (
        (t.entrances || []).find((e) => {
          const conflictActions = filterRecursively(
            actions,
            (a) =>
              APP_ACTIONS[a.name] &&
              a.id &&
              a.id === e.id &&
              a.collection === e.collection,
            ["no", "yes"]
          );
          return Boolean(conflictActions.length);
        })
      ) {
        loopDanger = true;
        console.log("checkActionsLoopDanger > loopAlert");
      }
    });
  }

  const goToActions = filterRecursively(
    actions,
    (a) => a.name === APP_ACTIONS.APP_ACTION_GO_TO_ACTION
  );

  if (!loopDanger && goToActions.length) {
    console.log("checkActionsLoopDanger > goToActions", goToActions);

    goToActions.forEach((goToAction, i) => {
      // console.log('checkActionsLoopDanger > goToActions > forEach', i);

      const checkLoop = (actionObj) => {
        const { target, name } = actionObj;

        if (
          loopDanger ||
          !target ||
          name !== APP_ACTIONS.APP_ACTION_GO_TO_ACTION
        )
          return;

        try {
          const actionKeyPath = target.split("-")[1].split(":");
          const dstIndex = actionKeyPath.pop();

          let dstParent = actions;

          for (const k of actionKeyPath) {
            if (dstParent[k]) {
              dstParent = dstParent[k];
            } else {
              loopDanger = true;
              console.log("checkActionsLoopDanger > !!no parent!!", {
                actionKeyPath,
                dstIndex,
                dstParent,
                goToAction,
                actionObj,
                i,
                json: JSON.stringify({ goToAction, actionObj }),
              });
            }
          }

          const dstAction = dstParent[dstIndex];
          const nextActions = jsonClone(dstParent).splice(dstIndex);
          const nextGoActions = Array.isArray(nextActions)
            ? filterRecursively(
              nextActions,
              (a) => a.name === APP_ACTIONS.APP_ACTION_GO_TO_ACTION
            )
            : [];

          if (
            dstParent === actions ||
            (dstAction &&
              dstAction.name === APP_ACTIONS.APP_ACTION_GO_TO_ACTION) ||
            nextGoActions.find(
              (a) =>
                areEqualObjects(a, goToAction) || areEqualObjects(a, actionObj)
            )
          ) {
            loopDanger = true;
            console.log("checkActionsLoopDanger > conflict!!", {
              actionKeyPath,
              dstIndex,
              dstParent,
              goToAction,
              actionObj,
              i,
              json: JSON.stringify({ goToAction, actionObj }),
            });
          }

          if (!loopDanger && nextGoActions.length) {
            // console.log('checkActionsLoopDanger > next',{nextGoActions, nextActions, actionObj, goToAction});
            nextGoActions.forEach(checkLoop);
          }
        } catch (error) {
          console.error("checkActionsLoopDanger > error!!", error);
        }
      };

      !loopDanger && checkLoop(goToAction);
    });
  }

  return loopDanger;
};

// grabHereToCopyPaste
const doActions = async ({ contact, deal, automation, actions }) => {
  contact = contact || (deal && deal.contact) || {};

  if (!actions) actions = automation.actions;

  if (
    !(actions || []).length ||
    checkActionsLoopDanger(automation.actions || actions)
  ) {
    return null;
  }

  /*
  // ----------------------------------------------------------------------------------
    ** NAO RETIRAR DE DENTRO DA FUNCAO PARA NAO FAZER REFERENCIA CRUZADA DE "REQUIRE()"
  // ---------------------------------------------------------------------------------- */
  const { createBatchMailing, createMailing } = require("../mailing");
  const { setTeamMembers } = require("../qiusers");
  // ----------------------------------------------------------------------------------
  // ----------------------------------------------------------------------------------

  const isDeal = Boolean(deal);
  const target = isDeal ? deal : contact;
  const contactId = isDeal ? deal.contactId : contact.ID;
  const dealId = isDeal ? deal.ID : "";
  const automationId = automation.ID;

  let { owner, accountId } = target;

  if (!owner || owner === CONSTANTS.ORPHANS_OWNER)
    owner = automation.owner || CONSTANTS.ORPHANS_OWNER;

  if (!accountId || accountId === CONSTANTS.ORPHANS_ACCOUNT)
    accountId = automation.accountId || CONSTANTS.ORPHANS_ACCOUNT;

  const automationAccountId = accountId;

  let updatedTarget = jsonClone(target);
  let updatedContact = jsonClone(contact);
  let queries = [];
  let promises = [];

  /*
  | Prevent Automation from running twice for
  | the same user if setting is active
  */
  const momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
  const isExpired =
    automation.end &&
    moment(automation.end).isValid() &&
    new Date(automation.end).getTime() < new Date(momentNowISO).getTime();
  const isPublished =
    !automation.status || automation.status === CONSTANTS.PUBLISH_STATUS;

  if (isExpired || !isPublished) {
    console.log("doActions > (isExpired || !isPublished)", {
      momentNowISO,
      isExpired,
      isPublished,
      automation,
    });
    return null;
  }

  /*
  | Prevent Automation from running twice for
  | the same user if setting is active
  */
  if ((automation.config || {}).prevent_dup_user) {
    let where = [];
    where.push(["trigger", "==", APP_TRIGGERS.APP_TRIGGER_FIRED]);
    where.push(["contactId", "==", contactId]);
    let limit = 1;
    let firedLogs = await fetchSubCollection(
      COLLECTIONS.AUTOMATIONS_COLLECTION_NAME,
      automationId,
      COLLECTIONS.LOGS_COLLECTION_NAME,
      where,
      limit
    );

    if (firedLogs.length) {
      console.log("DOACTIONS > prevent_dup_user > firedLogs", { firedLogs });
      return null;
    }
  }

  let actionIndex = 0;
  let breakActions = false;

  for (const action of actions) {
    if (breakActions) break;

    const { name, id, data, triggers } = action;
    console.log("DOACTIONS > INSIDE FOR", { name, id, data, triggers });
    switch (name) {
      case APP_ACTIONS.APP_ACTION_LOG:
        if (isLoopableObject(data) && data.trigger) {
          // LOGS
          const logKeys = [
            CONSTANTS.DATE_FIELD,
            CONSTANTS.MODIFIED_FIELD,
            ...CONSTANTS.RELATIONAL_FIELDS,
          ];
          const logData = {};

          logKeys.forEach((key) => {
            if (key in automation) {
              logData[key] = automation[key];
            } else {
              logData[key] = "";
            }
          });

          let log = {
            contactId,
            user_id: contactId,
            operator_id: 0,
            id: automation.ID,
            collection: automation.collection,
            trigger: data.trigger,
            date: momentNow().format(MOMENT_ISO),
            owner,
            accountId,
            data: logData,
            context: {
              collection: automation.collection,
              id: automation.ID,
              operator_id: "",
            },
          };

          // console.log('doActions > log',log);
          promises.push(addNewLog(log));
        }
        break;
      case APP_ACTIONS.APP_ACTION_EMAIL:
        queries.push({
          collection: COLLECTIONS.MAILING_COLLECTION_NAME,
          id,
          action: (post) => {
            // console.log(`doActions > ${name} > email > contactId`, contactId, { id, post });
            if (!post || !post.ID || !post.settings) return null;

            let {
              owner,
              content,
              settings,
              settings: { context },
            } = post;

            let html = content;
            if (settings.footer) {
              html += "<br>\n" + settings.footer;
            }
            if (settings.address) {
              html += `${"\n"}<br><p style="text-align:center;font-size:11px;">${settings.address
                }</p>`;
            }

            let accountId = post.accountId || automationAccountId;
            const email = {
              ...settings,
              scheduled_date: "",
              owner,
              accountId,
              html,
              segmentations: [],
              contacts: [contactId],
              confirmed_broadcast: false,
              context: {
                ...context,
                accountId,
                collection: automation.collection,
                id: automation.ID,
                operator_id: "",
                relatedId: post.ID,
                relatedCollection: post.collection,
              },
            };

            console.log(`doActions > action_email > ${name} > email`, email);
            return createBatchMailing(email);
          },
        });
        break;
      case APP_ACTIONS.APP_ACTION_NOTIFICATE_SELLER:
      case APP_ACTIONS.APP_ACTION_NOTIFICATE_MANAGER:
        queries.push({
          collection: COLLECTIONS.NOTIFICATIONS_COLLECTION_NAME,
          id,
          action: async (post) => {
            if (!post || !post.ID || !post.settings) return null;

            const afterPromisesTarget = await resolvePost(
              updatedTarget.collection,
              updatedTarget.ID,
              false
            );

            const targetUserId =
              action.name === CONSTANTS.APP_ACTION_NOTIFICATE_SELLER
                ? afterPromisesTarget[CONSTANTS.SELLER_FIELD]
                : afterPromisesTarget[CONSTANTS.MANAGER_FIELD];

            console.log(
              `doActions > ${name} > notification > targetUserId`,
              targetUserId
            );

            if (targetUserId && post.content) {
              let {
                owner,
                settings,
                settings: { context },
              } = post;

              try {
                const qiuser = await fetchPost(
                  COLLECTIONS.QIUSERS_COLLECTION_NAME,
                  targetUserId
                );
                const { content } = await replaceShortCodes(
                  post.content,
                  post,
                  COLLECTIONS.QIUSERS_COLLECTION_NAME,
                  [qiuser]
                );
                if (qiuser.email && content) {
                  const to = qiuser.email;
                  let accountId = post.accountId || automationAccountId;
                  const email = {
                    ...settings,
                    to,
                    scheduled_date: "",
                    owner,
                    accountId,
                    html: content,
                    segmentations: [],
                    contacts: [targetUserId],
                    context: {
                      ...context,
                      accountId,
                      collection: automation.collection,
                      id: automation.ID,
                      operator_id: "",
                      relatedId: post.ID,
                      relatedCollection: post.collection,
                    },
                  };
                  return createMailing(email);
                }
                return null;
              } catch (error) {
                console.error(error);
                return null;
              }
            }
            return null;
          },
        });
        break;
      case APP_ACTIONS.APP_ACTION_CONTRACT:
        queries.push({
          collection: COLLECTIONS.CONTRACTS_COLLECTION_NAME,
          id,
          action: (post) => {
            if (!post || !post.ID || !post.settings) return null;

            let {
              owner,
              content,
              settings,
              settings: { context },
            } = post;

            let html = content;
            if (settings.footer) {
              html += "<br>\n" + settings.footer;
            }
            if (settings.address) {
              html += `${"\n"}<br><p style="text-align:center;font-size:11px;">${settings.address
                }</p>`;
            }

            let accountId = post.accountId || automationAccountId;
            const email = {
              ...settings,
              scheduled_date: "",
              owner,
              accountId,
              html,
              segmentations: [],
              contacts: [contactId],
              confirmed_broadcast: false,
              context: {
                ...context,
                accountId,
                collection: automation.collection,
                id: automation.ID,
                operator_id: "",
                relatedId: post.ID,
                relatedCollection: post.collection,
              },
            };

            console.log(`doActions > action_contract > ${name} > email`, email);
            return createBatchMailing(email);
          },
        });
        break;
      case APP_ACTIONS.APP_ACTION_DEAL_UPDATE:
      case APP_ACTIONS.APP_ACTION_DEAL_UPDATE_IN_FUNNEL:
        if (data) {
          const funnelId =
            name === CONSTANTS.APP_ACTION_DEAL_UPDATE
              ? data.funnel
              : automation.ID;

          if (!funnelId) break;

          queries.push({
            collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
            id: funnelId,
            action: async (post) => {
              if (!post || !post.ID) return null;

              const where = [];
              where.push([CONSTANTS.CONTACT_ID_FIELD, "==", contactId]);
              where.push([CONSTANTS.FUNNEL_ID_FIELD, "==", funnelId]);

              const deals = await fetchCollection(
                COLLECTIONS.DEALS_COLLECTION_NAME,
                where
              );
              const subpromises = [];

              deals.forEach((foundDeal) => {
                if (foundDeal.ID) {
                  const title =
                    data.title ||
                    foundDeal.title ||
                    updatedContact.displayName ||
                    updatedContact.email ||
                    "";
                  const value = data.value || (foundDeal.data || {}).value || 0;

                  const pipeline = (post || {}).pipeline || {};
                  const stage =
                    ("stage" in data &&
                      pipeline[data.stage] &&
                      `${data.stage}`) ||
                    foundDeal.stage ||
                    CONSTANTS.FIRST_STAGE;

                  const { tags } = foundDeal;
                  const addTags = data.addTags || [];
                  const removeTags = data.removeTags || [];

                  const updatedTags = [
                    ...new Set([...tags, ...addTags]),
                  ].filter((tagId) => !removeTags.includes(tagId));

                  let updatedDeal = {
                    title,
                    value,
                    stage,
                    tags: updatedTags,
                  };

                  subpromises.push(
                    updatePost(
                      COLLECTIONS.DEALS_COLLECTION_NAME,
                      updatedDeal,
                      foundDeal.ID
                    )
                  );
                }
              });

              return Promise.all(subpromises);
            },
          });
        }
        break;
      case APP_ACTIONS.APP_ACTION_FUNNEL_SWITCHER:
        if (
          deal &&
          dealId &&
          data &&
          data.funnel &&
          data.funnel !== automation.ID
        ) {
          const origFunnelId = automation.ID;
          const destFunnelId = data.funnel;

          queries.push({
            collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
            id: destFunnelId,
            action: async (post) => {
              if (!post || !post.ID) return null;

              const pipeline = (post || {}).pipeline || {};
              const stage =
                ("stage" in data && pipeline[data.stage] && `${data.stage}`) ||
                CONSTANTS.FIRST_STAGE;

              if (pipeline && pipeline[stage]) {
                const title =
                  data.title ||
                  deal.title ||
                  updatedContact.displayName ||
                  updatedContact.email ||
                  "";
                const value = data.value || (deal.data || {}).value || 0;

                const { tags } = deal;
                const addTags = data.addTags || [];
                const removeTags = data.removeTags || [];

                const updatedTags = [...new Set([...tags, ...addTags])].filter(
                  (tagId) => !removeTags.includes(tagId)
                );

                let updatedDeal = {
                  title,
                  value,
                  stage,
                  tags: updatedTags,
                  [CONSTANTS.FUNNEL_ID_FIELD]: destFunnelId,
                };

                return updatePost(
                  COLLECTIONS.DEALS_COLLECTION_NAME,
                  updatedDeal,
                  dealId
                );
              }

              return null;
            },
          });
        }
        break;
      case APP_ACTIONS.APP_ACTION_DEAL:
        if (data && data.funnel) {
          const funnelId = data.funnel;

          queries.push({
            collection: COLLECTIONS.FUNNELS_COLLECTION_NAME,
            id: funnelId,
            action: async (post) => {
              if (!post || !post.ID) return null;

              const config = post.config || {};
              const pipeline = post.pipeline || {};
              const funnelData = post.data || {};

              const hasFirstName = Boolean(
                `${updatedContact.firstName || ""}`.replace(/ /g, "")
              );
              const title =
                data.title ||
                updatedContact.displayName ||
                (hasFirstName
                  ? `${updatedContact.firstName} ${updatedContact.lastName}`
                  : "") ||
                updatedContact.email ||
                "";
              const value = data.value || funnelData.value || 0;
              const stage =
                ("stage" in data && pipeline[data.stage] && `${data.stage}`) ||
                CONSTANTS.FIRST_STAGE;
              const tags = data.tags || [];

              const sales_mode =
                (config && config.sales_mode) || CONSTANTS.DEFAULT_SALES_MODE;
              const products = data.products || funnelData.products || [];

              let task_id = "";
              if (pipeline[stage] && pipeline[stage].task_id) {
                task_id = pipeline[stage].task_id;
              }

              let accountId = post.accountId || automationAccountId;

              const contactModel = models.childnodes.contact;

              const dealContact = {};

              Object.keys(contactModel).forEach((k, i) => {
                dealContact[k] = updatedContact[k] || contactModel[k];
              });

              const newDeal = {
                ID: "",
                id: "",
                title,
                contact: dealContact,
                contactId,
                accountId,
                owner,
                funnel: funnelId,
                stage,
                date: momentNow().format(MOMENT_ISO),
                modified: momentNow().format(MOMENT_ISO),
                collection: COLLECTIONS.DEALS_COLLECTION_NAME,
                config: {
                  notification: false,
                  sales_mode,
                },
                data: {
                  value,
                  total: 0,
                  products,
                },
                description: "",
                comments: [],
                seller: "",
                manager: "",
                stages: {},
                checklist: {},
                notifications: [],
                tasks: [],
                tags,
                stores: [],
                pos: 0,
                logs: {},
              };

              return addNewPost(COLLECTIONS.DEALS_COLLECTION_NAME, newDeal);
            },
          });
        }
        break;
      case APP_ACTIONS.APP_ACTION_EVENT:
        queries.push({
          collection: COLLECTIONS.EVENTS_COLLECTION_NAME,
          id,
          subcollection: COLLECTIONS.PARTICIPANTS_COLLECTION_NAME,
          where: [["ID", "==", contactId]],
          action: (posts) => {
            if (
              !Array.isArray(posts) ||
              !posts.find((p) => p.ID === contactId)
            ) {
              let participant = {
                ID: contactId,
                collection: COLLECTIONS.LEADS_COLLECTION_NAME,
                confirmed: false,
                checkin: false,
                checkinTime: "",
                added: momentNow().format(MOMENT_ISO),
                modified: momentNow().format(MOMENT_ISO),
                context: {
                  collection: automation.collection,
                  id: automation.ID,
                  operator_id: "",
                },
              };

              return FirestoreRef.collection(COLLECTIONS.EVENTS_COLLECTION_NAME)
                .doc(id)
                .collection(COLLECTIONS.PARTICIPANTS_COLLECTION_NAME)
                .doc(contactId)
                .set(participant);
            }

            return null;
          },
        });
        break;
      case APP_ACTIONS.APP_ACTION_CREATE_TICKET:
        queries.push({
          collection: COLLECTIONS.TICKETS_COLLECTION_NAME,
          id,
          action: async (post) => {
            if (!post || !post.ID) return null;

            const contactModel = models.childnodes.contact;

            const ticketContact = {};

            Object.keys(contactModel).forEach((k, i) => {
              ticketContact[k] = updatedContact[k] || contactModel[k];
            });

            let postTitle = "";
            let titleFields = [];

            if (post.title) titleFields.push(`${post.title || ""}`);

            if (updatedContact.displayName) {
              titleFields.push(updatedContact.displayName);
            } else if (updatedContact.firstName) {
              titleFields.push(
                `${updatedContact.firstName} ${updatedContact.lastName}`
              );
            }
            if (updatedContact.companyName)
              titleFields.push(updatedContact.companyName);

            titleFields.push(momentNow().format(MOMENT_SHORT));

            if (titleFields.reduce((a, b) => a + b, "").replace(/ /g, "")) {
              postTitle = titleFields.join("-");
            }

            let newPost = {
              ...post,
              ID: "",
              id: "",
              title: postTitle,
              status: CONSTANTS.PUBLISH_STATUS,
              config: {
                ...post.config,
                use_as_template: false,
              },
              payment: {
                ...post.payment,
                nf_check: Boolean(data.nf_check),
              },
              data: {
                ...post.data,
                contractId: "",
              },
              notazz: {},
              contactId,
              contact: ticketContact,
              author: "",
              accountId,
              date: momentNow().format(MOMENT_ISO),
              modified: momentNow().format(MOMENT_ISO),
              context: {
                collection: automation.collection,
                id: automation.ID,
                operator_id: "",
              },
            };

            if (automation.collection === COLLECTIONS.FUNNELS_COLLECTION_NAME) {
              newPost.context[CONSTANTS.FUNNEL_ID_FIELD] = automation.ID;
            }

            const newTicket = await addNewPost(
              COLLECTIONS.TICKETS_COLLECTION_NAME,
              newPost
            );
            const ticketID = newTicket.ID;

            if (
              data.nf_check &&
              data.nf_integration &&
              Array.isArray(post.items)
            ) {
              const notazzIntegration = await resolvePost(
                COLLECTIONS.INTEGRATIONS_COLLECTION_NAME,
                data.nf_integration,
                true
              );
              const apiKey =
                notazzIntegration &&
                (notazzIntegration.data || {}).notazz_api_key;

              console.log(`doActions > ${name} > Tickets > apiKey`, {
                notazzIntegration,
                apiKey,
              });

              if (apiKey) {
                const lead = { ...(isDeal ? updatedContact : updatedTarget) };
                const prodModel = { ...nfeModel.DOCUMENT_PRODUCT[1] };

                let subpromises = post.items
                  .filter((i) => i.product)
                  .map((i) =>
                    resolvePost(COLLECTIONS.PRODUCTS_COLLECTION_NAME, i.product)
                  );
                let products = await Promise.all(subpromises);

                console.log(`doActions > ${name} > Tickets > products`, {
                  products,
                });

                /* nf type */
                let nfType = products.find(
                  (p) => (p.notazz || {}).nf_type === "product"
                )
                  ? "product"
                  : products.find((p) => (p.notazz || {}).nf_type === "service")
                    ? "service"
                    : "";

                if (!nfType) return null;

                let nfModel = nfType === "product" ? nfeModel : nfseModel;
                let notazz = { ...nfModel };

                notazz.DOCUMENT_BASEVALUE = 0;

                products.forEach((product, i) => {
                  let item = post.items.find((i) => i.product === product.ID);
                  if (product && (product.notazz || {}).nf_type) {
                    let { nf_type, DOCUMENT_PRODUCT } = product.notazz;
                    let qty = item.qty;

                    if (nf_type === "product") {
                      if (
                        (DOCUMENT_PRODUCT || {}).DOCUMENT_PRODUCT_UNITARY_VALUE
                      ) {
                        let nfProd = { ...prodModel };
                        Object.keys(prodModel).forEach((k) => {
                          const v =
                            k in DOCUMENT_PRODUCT
                              ? DOCUMENT_PRODUCT[k]
                              : prodModel[k];
                          nfProd[k] = v;
                        });
                        nfProd.DOCUMENT_PRODUCT_QTD = qty;
                        notazz.DOCUMENT_PRODUCT[i + 1] = nfProd;
                        notazz.DOCUMENT_BASEVALUE +=
                          parseInt(i.qty || 1, 10) *
                          parseFloat(
                            DOCUMENT_PRODUCT.DOCUMENT_PRODUCT_UNITARY_VALUE
                          );
                      }
                      const nfeFieldList = ["DOCUMENT_CNAE"];

                      nfeFieldList.forEach((fName) => {
                        if (product.notazz[fName]) {
                          notazz[fName] = product.notazz[fName];
                        }
                      });
                    } else if (nf_type === "service") {
                      if (product.data.price) {
                        notazz.DOCUMENT_BASEVALUE += parseFloat(
                          product.data.price
                        );
                      }
                      const nfseFieldList = [
                        "DOCUMENT_CNAE",
                        "SERVICE_LIST_LC116",
                        "CITY_SERVICE_CODE",
                        "CITY_SERVICE_DESCRIPTION",
                        "WITHHELD_ISS",
                      ];

                      nfseFieldList.forEach((fName) => {
                        if (product.notazz[fName]) {
                          notazz[fName] = product.notazz[fName];
                        }
                      });

                      if (product.notazz.ALIQUOTAS) {
                        notazz.ALIQUOTAS = product.notazz.ALIQUOTAS;
                      }
                    }
                  }
                });

                notazz.DOCUMENT_BASEVALUE = Number(
                  notazz.DOCUMENT_BASEVALUE
                ).toFixed(2);

                /* customer */
                let {
                  address,
                  email,
                  firstName,
                  lastName,
                  type,
                  cpf,
                  cnpj,
                  phone,
                } = lead;

                notazz.DESTINATION_TAXTYPE = type === "corporation" ? "J" : "F";
                notazz.DESTINATION_NAME = `${firstName} ${lastName}`;

                if (type === "corporation" && cnpj) {
                  notazz.DESTINATION_TAXID = `${convertTonumOnly(cnpj)}`;
                } else if (cpf) {
                  notazz.DESTINATION_TAXID = `${convertTonumOnly(cpf)}`;
                }
                if (lead.IE) {
                  notazz.DESTINATION_IE = `${convertTonumOnly(lead.IE)}`;
                }
                if (lead.IM) {
                  notazz.DESTINATION_IM = `${convertTonumOnly(lead.IM)}`;
                }
                if (email) {
                  notazz.DESTINATION_EMAIL = `${email}`;
                }
                if (phone) {
                  notazz.DESTINATION_PHONE = `${convertTonumOnly(phone)}`;
                }

                /* address */
                if (address.street) {
                  notazz.DESTINATION_STREET = `${address.street}`;
                }
                if (address.num) {
                  notazz.DESTINATION_NUMBER = `${address.num}`;
                }
                if (address.comp) {
                  notazz.DESTINATION_COMPLEMENT = `${address.comp}`;
                }
                if (address.neighborhood) {
                  notazz.DESTINATION_DISTRICT = `${address.neighborhood}`;
                }
                if (address.city) {
                  notazz.DESTINATION_CITY = `${address.city}`;
                }
                if (address.state) {
                  notazz.DESTINATION_UF = `${address.state}`;
                }
                if (address.postalCode) {
                  notazz.DESTINATION_ZIPCODE = `${convertTonumOnly(
                    address.postalCode
                  )}`;
                }

                /* EXTERNAL_ID */
                notazz.EXTERNAL_ID = ticketID;
                notazz.SALE_ID = ticketID;

                Object.keys(nfModel).forEach((k) => {
                  const v = k in notazz ? notazz[k] : nfModel[k];
                  notazz[k] = v;
                });

                console.log(`doActions > ${name} > Tickets > notazz`, {
                  notazz,
                });

                return createNotazzNF(apiKey, nfType, notazz)
                  .then((result) => {
                    console.log("result >>> ");
                    console.log(result);
                    return savePostFields(
                      COLLECTIONS.TICKETS_COLLECTION_NAME,
                      ticketID,
                      { notazz: { ...notazz, nf_ID: result.id, nf_error: "" } }
                    );
                  })
                  .catch(async (error) => {
                    console.error(error);
                    let msg = "Erro ao gerar a Nota Fiscal";
                    if (typeof error === "string") msg = error;
                    const notification = {
                      accountId,
                      owner,
                      qiuser: owner,
                      title: "Erro ao gerar a Nota Fiscal",
                      message: msg,
                      url: `/${COLLECTIONS.TICKETS_COLLECTION_NAME}/${ticketID}`,
                      context: newTicket.context,
                      viewed: false,
                      scheduled_date: momentNow().format(MOMENT_ISO),
                      date: momentNow().format(MOMENT_ISO),
                      modified: momentNow().format(MOMENT_ISO),
                    };
                    let notificationResult = await addNewPost(
                      COLLECTIONS.DESKTOP_NOTIFICATIONS_COLLECTION_NAME,
                      notification
                    );
                    console.log(
                      `doActions > ${name} > Tickets > notificationResult`,
                      { notificationResult }
                    );
                    return savePostFields(
                      COLLECTIONS.TICKETS_COLLECTION_NAME,
                      ticketID,
                      { notazz: { ...notazz, nf_error: msg } }
                    );
                  });
              }
            }

            return null;
          },
        });

        break;
      case APP_ACTIONS.APP_ACTION_PROFILE:
        if (data && data.profile) {
          updatedTarget.profile = data.profile;
        }
        break;
      case APP_ACTIONS.APP_ACTION_SEGMENTATIONS:
        queries.push({
          collection: COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME,
          id,
          action: (post) => {
            if (!post || !post.ID) return null;
            if (!Array.isArray(post.leads)) post.leads = [];
            if (!post.leads.includes(contactId)) {
              post.leads.push(contactId);
              return updatePost(
                COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME,
                { leads: post.leads },
                post.ID
              );
            }
            return null;
          },
        });
        break;
      case APP_ACTIONS.APP_ACTION_SCORE:
        if (data && data.add && !isNaN(parseFloat(data.add))) {
          if (updatedTarget.ID && updatedTarget.collection) {
            let scoreDoc = {
              score: data.add,
              date: nowToISO(),
              context: {
                action,
                collection: automation.collection,
                id: automation.ID,
                operator_id: "",
              },
            };
            promises.push(
              addScore(updatedTarget.collection, updatedTarget.ID, scoreDoc)
            );
          }
        }
        break;
      case APP_ACTIONS.APP_ACTION_STORE:
        if (!Array.isArray(updatedTarget.stores)) updatedTarget.stores = [];
        updatedTarget.stores = [...new Set([...updatedTarget.stores, id])];
        break;
      // case APP_ACTIONS.APP_ACTION_TAGS:
      //   if (data && Array.isArray(data.tags)) {
      //     if (isDeal) {
      //       if (!Array.isArray(updatedContact.tags)) updatedContact.tags = [];
      //       updatedContact.tags = [
      //         ...new Set([...updatedContact.tags, ...data.tags]),
      //       ];
      //     } else {
      //       if (!Array.isArray(updatedTarget.tags)) updatedTarget.tags = [];
      //       updatedTarget.tags = [
      //         ...new Set([...updatedTarget.tags, ...data.tags]),
      //       ];
      //     }
      //   }
      //   break;
      case APP_ACTIONS.APP_ACTION_REMOVE_TAGS:
        if (data && Array.isArray(data.tags)) {
          if (isDeal) {
            if (!Array.isArray(updatedContact.tags)) updatedContact.tags = [];
            updatedContact.tags = [...new Set([...updatedContact.tags])].filter(
              (tId) => !data.tags.includes(tId)
            );
          } else {
            if (!Array.isArray(updatedTarget.tags)) updatedTarget.tags = [];
            updatedTarget.tags = [...new Set([...updatedTarget.tags])].filter(
              (tId) => !data.tags.includes(tId)
            );
          }
        }
        break;
      case APP_ACTIONS.APP_ACTION_DEALS_TAGS:
        if (isDeal && data && Array.isArray(data.tags)) {
          if (!Array.isArray(updatedTarget.tags)) updatedTarget.tags = [];
          updatedTarget.tags = [
            ...new Set([...updatedTarget.tags, ...data.tags]),
          ];
        }
        break;
      case APP_ACTIONS.APP_ACTION_REMOVE_DEALS_TAGS:
        if (isDeal && data && Array.isArray(data.tags)) {
          if (!Array.isArray(updatedTarget.tags)) updatedTarget.tags = [];
          updatedTarget.tags = [...new Set([...updatedTarget.tags])].filter(
            (tId) => !data.tags.includes(tId)
          );
        }
        break;
      case APP_ACTIONS.APP_ACTION_TEAMS:
        if (updatedTarget.ID && updatedTarget.collection && id) {
          try {
            const distMode = (data || {}).sellers_mode;
            const team = await resolvePost(
              COLLECTIONS.TEAMS_COLLECTION_NAME,
              id,
              true
            );
            if (team && team.ID) {
              if (isDeal) {
                let funnel, funnelId;
                if (
                  automation.collection === COLLECTIONS.FUNNELS_COLLECTION_NAME
                ) {
                  funnel = automation;
                  funnelId = automation.ID;
                } else if (updatedTarget[CONSTANTS.FUNNEL_ID_FIELD]) {
                  funnelId = updatedTarget[CONSTANTS.FUNNEL_ID_FIELD];
                  funnel = await resolvePost(
                    COLLECTIONS.FUNNELS_COLLECTION_NAME,
                    funnelId,
                    true
                  );
                }
                promises.push(
                  setTeamMembers({
                    deal: updatedTarget,
                    team,
                    funnel,
                    mode: distMode,
                    overwrite: true,
                  })
                );
              } else {
                promises.push(
                  setTeamMembers({
                    lead: updatedTarget,
                    team,
                    accountId,
                    mode: distMode,
                    overwrite: true,
                  })
                );
              }
            }
          } catch (error) {
            console.error("doActions > team > error", error);
          }
        }
        break;
      case APP_ACTIONS.APP_ACTION_SET_SELLER:
        if (updatedTarget.ID && updatedTarget.collection && data.seller) {
          try {
            const seller = await resolvePost(
              COLLECTIONS.QIUSERS_COLLECTION_NAME,
              data.seller,
              true
            );
            console.log("doActions > seller > data", seller);
            if (seller && seller.ID) {
              updatedTarget.seller = seller.ID;
              promises.push(
                updatePost(
                  updatedTarget.collection,
                  { [CONSTANTS.SELLER_FIELD]: seller.ID },
                  updatedTarget.ID
                )
              );
            }
          } catch (error) {
            console.error("doActions > seller > error", error);
          }
        }
        break;
      case APP_ACTIONS.APP_ACTION_SET_MANAGER:
        if (updatedTarget.ID && updatedTarget.collection && data.manager) {
          try {
            const manager = await resolvePost(
              COLLECTIONS.QIUSERS_COLLECTION_NAME,
              data.manager,
              true
            );
            console.log("doActions > manager > data", manager);
            if (manager && manager.ID) {
              updatedTarget.manager = manager.ID;
              promises.push(
                updatePost(
                  updatedTarget.collection,
                  { [CONSTANTS.MANAGER_FIELD]: manager.ID },
                  updatedTarget.ID
                )
              );
            }
          } catch (error) {
            console.error("doActions > manager > error", error);
          }
        }
        break;
      case APP_ACTIONS.APP_ACTION_TEAM_TASKLIST:
      case APP_ACTIONS.APP_ACTION_USER_TASKLIST:
        if (
          data &&
          ((name === APP_ACTIONS.APP_ACTION_USER_TASKLIST && data.qiuser) ||
            (name === APP_ACTIONS.APP_ACTION_TEAM_TASKLIST && data.team))
        ) {
          queries.push({
            collection: COLLECTIONS.TASKLISTS_COLLECTION_NAME,
            id,
            action: (post) => {
              if (!post || !post.ID) return null;

              let qiuserId =
                name === APP_ACTIONS.APP_ACTION_USER_TASKLIST
                  ? data.qiuser
                  : post.qiuser;
              let teamId =
                name === APP_ACTIONS.APP_ACTION_TEAM_TASKLIST
                  ? data.team
                  : post.team;
              let tasks = (post.tasks || []).map((t) => ({
                ...t,
                completed: false,
              }));
              let accountId = post.accountId || automationAccountId;

              let newPost = {
                ...post,
                ID: "",
                id: "",
                contactId,
                accountId,
                qiuser: qiuserId,
                team: teamId,
                date: momentNow().format(MOMENT_ISO),
                modified: momentNow().format(MOMENT_ISO),
                tasks,
                context: {
                  collection: automation.collection,
                  id: automation.ID,
                  operator_id: "",
                },
              };
              return addNewPost(COLLECTIONS.TASKLISTS_COLLECTION_NAME, newPost);
            },
          });
        }
        break;
      case APP_ACTIONS.APP_ACTION_DEAL_TASKLIST:
        if (isDeal && updatedTarget.ID) {
          queries.push({
            collection: COLLECTIONS.TASKLISTS_COLLECTION_NAME,
            id,
            action: (post) => {
              if (!post || !post.ID) return null;

              let postTasks = (post.tasks || []).map((t) => ({
                ...t,
                completed: false,
              }));
              let tasks = {
                ...(updatedTarget.tasks || {}),
                [id]: postTasks,
              };

              return updatePost(
                COLLECTIONS.DEALS_COLLECTION_NAME,
                { tasks },
                updatedTarget.ID
              );
            },
          });
        }
        break;
      case APP_ACTIONS.APP_ACTION_DEAL_WEBHOOK:
        if (isDeal && data.url) {
          let dealData = {};
          Object.keys(dealWebhook).forEach((k, i) => {
            dealData[k] = updatedTarget[k] || dealWebhook[k];
          });

          if (data.tags && Array.isArray(updatedTarget.tags)) {
            dealData.tags = [];
            for (const tagId of updatedTarget.tags) {
              try {
                let { title, ID } = await fetchTaxonomy(
                  COLLECTIONS.TAGS_TAXONOMY_NAME,
                  COLLECTIONS.LEADS_COLLECTION_NAME,
                  tagId
                );
                dealData.tags.push({ title, ID });
              } catch (error) {
                console.log(error);
              }
            }
          }
          if (data.comments && Array.isArray(updatedTarget.comments)) {
            dealData.comments = [];
            try {
              for (const { content } of updatedTarget.comments) {
                dealData.comments.push(content);
              }
            } catch (error) {
              console.log(error);
            }
          }

          dealData.value = (updatedTarget.data || {}).value || 0;

          if (updatedTarget.deadline && updatedTarget.deadline.cronId) {
            try {
              let scheduledMoment = getScheduledMoment(
                updatedTarget.deadline,
                updatedTarget
              );
              if (scheduledMoment)
                dealData.deadline = scheduledMoment.format(
                  CONSTANTS.MOMENT_ISO
                );
            } catch (error) {
              console.log(error);
            }
          }

          if (
            Array.isArray((updatedTarget.data || {}).products) &&
            updatedTarget.data.products.length
          ) {
            dealData.products = [];
            for (const productId of updatedTarget.data.products) {
              try {
                let product = await fetchPost(
                  COLLECTIONS.PRODUCTS_COLLECTION_NAME,
                  productId
                );
                if (product.data.price)
                  dealData.products.push({
                    title: product.title,
                    ID: product.ID,
                    price: product.data.price,
                  });
              } catch (error) {
                console.log(error);
              }
            }
          }

          try {
            if (updatedTarget[CONSTANTS.FUNNEL_ID_FIELD] === automationId) {
              dealData.funnel = { title: automation.title, ID: automationId };
            } else if (updatedTarget[CONSTANTS.FUNNEL_ID_FIELD]) {
              let { title, ID } = await fetchPost(
                COLLECTIONS.FUNNELS_COLLECTION_NAME,
                updatedTarget[CONSTANTS.FUNNEL_ID_FIELD]
              );
              dealData.funnel = { title, ID };
            }
          } catch (error) {
            console.log(error);
          }

          promises.push(
            new Promise((res) => {
              const params = qs.stringify(dealData);
              return axios
                .post(data.url, params)
                .then((r) => {
                  console.log(`doActions > ${name} > webhook > response`, r);
                  return res(true);
                })
                .catch((err) => {
                  console.log(`doActions > ${name} > webhook > err`, err);
                  return res(false);
                });
            })
          );
        }

        break;
      case APP_ACTIONS.APP_ACTION_WEBHOOK:
        if (data.url) {
          let srcData = isDeal ? updatedContact : updatedTarget;
          let leadData = {};
          Object.keys(leadWebhook).forEach((k, i) => {
            leadData[k] = srcData[k] || leadWebhook[k];
          });
          if (srcData.mobile) {
            leadData.phone_numbers.push(
              `+${srcData.mobileCC || "55"}${srcData.mobile.replace(/\D/g, "")}`
            );
          }
          if (srcData.phone) {
            leadData.phone_numbers.push(
              `+${srcData.phoneCC || "55"}${srcData.phone.replace(/\D/g, "")}`
            );
          }
          if (data.tags && Array.isArray(srcData.tags)) {
            leadData.tags = [];
            for (const tagId of srcData.tags) {
              try {
                let { title, ID } = await fetchTaxonomy(
                  COLLECTIONS.TAGS_TAXONOMY_NAME,
                  COLLECTIONS.LEADS_COLLECTION_NAME,
                  tagId
                );
                leadData.tags.push({ title, ID });
              } catch (error) {
                console.log(error);
              }
            }
          }
          if (data.comments && Array.isArray(srcData.comments)) {
            leadData.comments = [];
            try {
              for (const { content } of srcData.comments) {
                leadData.comments.push(content);
              }
            } catch (error) {
              console.log(error);
            }
          }
          if (data.customFields && isLoopableObject(srcData.customFields)) {
            leadData.custom_fields = [];
            for (const field in srcData.customFields) {
              try {
                if (field.indexOf(`${CONSTANTS.CUSTOM_FIELDS_PATH}:`) > -1) {
                  let { title, ID } = await fetchPost(
                    COLLECTIONS.FIELDS_COLLECTION_NAME,
                    field.replace(`${CONSTANTS.CUSTOM_FIELDS_PATH}:`, "")
                  );
                  leadData.custom_fields.push({ title, ID });
                }
              } catch (error) {
                console.log(error);
              }
            }
          }
          promises.push(
            new Promise((res) => {
              const params = qs.stringify(leadData);
              return axios
                .post(data.url, params)
                .then((r) => {
                  console.log(`doActions > ${name} > webhook > response`, r);
                  return res(true);
                })
                .catch((err) => {
                  console.log(`doActions > ${name} > webhook > err`, err);
                  return res(false);
                });
            })
          );
        }

        break;
      case APP_ACTIONS.APP_ACTION_EVALUATE:
        if (Array.isArray(triggers)) {
          // TODO evaluateAutomationTringgers chama o evaluate
          const result = await evaluateAutomationTriggers(
            {
              ...automation,
              ...action,
              config: { ...automation.config, ...action.config },
            },
            contactId,
            deal
          );
          const conditionalActions = result ? action.yes : action.no;

          console.log(`doActions > ${name} > conditionalActions`, {
            conditionalActions,
            result: Boolean(result),
          });

          if (Array.isArray(conditionalActions) && conditionalActions.length) {
            promises.push(
              doActions({
                contact: isDeal ? updatedContact : updatedTarget,
                deal: isDeal ? updatedTarget : deal,
                automation,
                actions: conditionalActions,
              })
            );
          }
        }

        break;
      case APP_ACTIONS.APP_ACTION_GO_TO_AUTOMATION:
        if (id) {
          queries.push({
            collection: COLLECTIONS.AUTOMATIONS_COLLECTION_NAME,
            id,
            action: async (post) => {
              const postActions = post.actions;
              const loopActions = Array.isArray(postActions)
                ? filterRecursively(
                  postActions,
                  (a) => a.name === name && a.id === automationId
                )
                : [false];

              if (!loopActions.length && postActions.length) {
                console.log(`doActions > ${name} > postActions`, postActions);

                try {
                  let result = await doActions({
                    contact: isDeal ? updatedContact : updatedTarget,
                    deal: isDeal ? updatedTarget : deal,
                    automation: post,
                  });

                  return addNewLog({
                    contactId,
                    user_id: contactId,
                    operator_id: 0,
                    id: post.ID,
                    collection: COLLECTIONS.AUTOMATIONS_COLLECTION_NAME,
                    trigger: APP_TRIGGERS.APP_TRIGGER_FIRED,
                    date: momentNow().format(CONSTANTS.MOMENT_ISO),
                    owner,
                    accountId,
                    context: {
                      operator_id: 0,
                      id: automationId,
                      collection: COLLECTIONS.AUTOMATIONS_COLLECTION_NAME,
                    },
                    data: {
                      action,
                      results: Array.isArray(result) && result.length,
                      err: result === null,
                    },
                  });
                } catch (error) {
                  console.error(error);
                }
              }

              return null;
            },
          });
        }

        break;
      case APP_ACTIONS.APP_ACTION_GO_TO_ACTION:
        if (action.target && automation.actions) {
          const automationActions = automation.actions;

          try {
            const actionKeyPath = action.target.split("-")[1].split(":");
            const dstIndex = actionKeyPath.pop();

            let dstParent = automationActions;
            for (const k of actionKeyPath) {
              dstParent = dstParent[k];
            }

            if (dstParent !== automationActions) {
              const nextActions = jsonClone(dstParent).splice(dstIndex);
              console.log(`doActions > ${name} > nextActions`, { nextActions });

              if (Array.isArray(nextActions) && nextActions.length) {
                promises.push(
                  doActions({
                    contact: isDeal ? updatedContact : updatedTarget,
                    deal: isDeal ? updatedTarget : deal,
                    automation,
                    actions: nextActions,
                  })
                );
              }
            }
          } catch (error) {
            console.error(`doActions > ${name} > error`, error);
          }
        }

        break;
      case APP_ACTIONS.APP_ACTION_TIMER:
      case APP_ACTIONS.APP_ACTION_SCHEDULE:
        if (isLoopableObject(data) && actionIndex + 1 < actions.length) {
          const pendingActions = Array.from(actions).splice(actionIndex + 1);
          let scheduled_date = "";

          if (name === APP_ACTIONS.APP_ACTION_SCHEDULE) {
            scheduled_date =
              data.datetime && moment(data.datetime).isValid()
                ? moment(data.datetime)
                : "";
          } else {
            scheduled_date = momentNow();

            if (!isNaN(parseInt(data.dd))) {
              let dd = parseInt(data.dd);
              scheduled_date.add(dd, "days");
            }
            if (!isNaN(parseInt(data.hh))) {
              let hh = parseInt(data.hh);
              scheduled_date.add(hh, "hours");
            }
            if (!isNaN(parseInt(data.mm))) {
              let mm = parseInt(data.mm);
              scheduled_date.add(mm, "minutes");
            }
          }

          if (scheduled_date && scheduled_date.isAfter(momentNow())) {
            breakActions = true;

            const newJob = {
              actions: pendingActions,
              accountId,
              contactId,
              dealId,
              automation: automation.ID,
              executed: false,
              execution_date: "",
              scheduled_date: scheduled_date.format(MOMENT_ISO),
              date: momentNow().format(MOMENT_ISO),
              modified: momentNow().format(MOMENT_ISO),
              type: CRONJOB_TYPES.AUTOMATION,
              context: {
                collection: automation.collection,
                id: automation.ID,
                operator_id: "",
              },
            };

            promises.push(
              addNewPost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, newJob)
            );
          }
        }
        break;
      //TODO: APP_ACTION_SHOTX_SEND_TEXT_MESSAGE
      case APP_ACTIONS.APP_ACTION_SHOTX_SEND_TEXT_MESSAGE:
        {
          const { quick_message, instance } = data;

          if (!quick_message || !instance) return null;

          const instanceCollection = `${COLLECTIONS.SHOTX_COLLECTION_NAME}/${accountId}/${COLLECTIONS.SHOTX_INSTANCES_COLLECTION_NAME}`;
          const messageCollection = `${COLLECTIONS.SHOTX_COLLECTION_NAME}/${accountId}/${COLLECTIONS.SHOTX_QUICK_MESSAGES_COLLECTION_NAME}`;
          const instanceFind = await fetchCollection(instanceCollection, [
            ["ID", "==", instance],
          ]).then((res) => res[0]);

          if (!instanceFind || instanceFind.status.state !== "open") {
            console.log(
              "APP_ACTION_SHOTX_SEND_TEXT_MESSAGE > INSTANCE NOT FOUND OR CLOSED"
            );
            return null;
          }

          const messageFind = await fetchCollection(messageCollection, [
            ["id", "==", quick_message],
          ]).then((res) => res[0]);

          if (!messageFind) {
            console.log(
              "APP_ACTION_SHOTX_SEND_TEXT_MESSAGE > MESSAGE NOT FOUND"
            );
            return null;
          }

          let contactNumber = null;
          const lead = await fetchCollection(
            COLLECTIONS.LEADS_COLLECTION_NAME,
            [['accountId', "==", accountId], ['ID', "==", contactId]],)
            .then(res => res[0]);

          contactNumber = checkPhoneOrMobile(lead);
          if (!contactNumber) {
            console.log(
              "APP_ACTION_SHOTX_SEND_TEXT_MESSAGE > CONTACT NUMBER NOT FOUND"
            );
            return null;
          }

          replaceShortCodes(messageFind.message, [], COLLECTIONS.LEADS_COLLECTION_NAME, [lead]).then(message => {
            console.log('APP_ACTION_SHOTX_SEND_TEXT_MESSAGE > SHORTCODES REPLACED MESSAGE', message)
            sendMessage(message.content, contactNumber, accountId, instanceFind.id)
            return message
          }).catch(err => {
            console.log('APP_ACTION_SHOTX_SEND_TEXT_MESSAGE > SHORTCODES REPLACED MESSAGE ERROR', err)
          })
        }
        break;
      default:
        break;
    }

    console.log("doActions > actionIndex " + actionIndex);
    actionIndex++;
  }

  const diffTarget = diffObjects(updatedTarget, target);
  const diffContact = diffObjects(updatedContact, contact);

  console.log("doActions > finish", {
    deal,
    target,
    actions,
    diffTarget,
    diffContact,
    updatedTarget,
    updatedContact,
  });

  if (isLoopableObject(diffTarget) && target.ID && target.collection) {
    promises.splice(0, 0, updatePost(target.collection, diffTarget, target.ID));
  }

  if (isLoopableObject(diffContact) && contact.ID && contact.collection) {
    promises.splice(
      0,
      0,
      updatePost(contact.collection, diffContact, contact.ID)
    );
  }

  const actionCb = [];
  for (const q of queries) {
    if (q.action && q.subcollection) {
      let queryRef = FirestoreRef.collection(q.collection)
        .doc(q.id)
        .collection(q.subcollection);
      if (q.where) {
        q.where.forEach((w) => {
          queryRef = queryRef.where(w[0], w[1], w[2]);
        });
      }
      // eslint-disable-next-line promise/no-nesting
      try {
        const snapshot = await queryRef.get();
        const res = snapshot.docs.map((doc) => ({ ...doc.data(), query: q }));
        actionCb.push(q.action(res));
      } catch (error) {
        console.error(error);
      }
    } else if (q.action && q.collection) {
      const res = await resolvePost(q.collection, q.id);
      actionCb.push(q.action(res));
    }
  }

  return Promise.all(promises).then((r) => Promise.all(actionCb));
};

// grabHereToCopyPaste
const fetchSegmentationsContactIds = (segmentationIds) =>
  new Promise((res, rej) => {
    if (typeof segmentationIds === "string") {
      segmentationIds = [segmentationIds];
    }

    // handle segmentations
    segmentationIds = [...new Set(segmentationIds || [])];

    let contactIds = [];

    console.log(
      "fetchSegmentationsContactIds > segmentationIds",
      segmentationIds
    );

    const promises = segmentationIds
      .map((sId) => ({
        sId,
        evaluate: async (s) => {
          let segmentationContactIds = [];

          if (s.status === CONSTANTS.PUBLISH_STATUS) {
            if ((s.config || {}).dinamic) {
              if (Array.isArray(s.triggers)) {
                let triggersContactIds = await fetchContactIdsFromTriggers(s);
                segmentationContactIds = [
                  ...new Set([
                    ...segmentationContactIds,
                    ...triggersContactIds,
                  ]),
                ];
              }
            } else {
              if (Array.isArray(s.leads)) {
                segmentationContactIds = [
                  ...new Set([...segmentationContactIds, ...s.leads]),
                ];
              }
            }
          }

          return segmentationContactIds;
        },
      }))
      .map(({ sId, evaluate }) => {
        return new Promise((res) => {
          return fetchPost(COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME, sId)
            .then(evaluate)
            .then(
              (sCIds) =>
                console.log(
                  "fetchSegmentationsContactIds > evaluate > sCIds",
                  sCIds.length
                ) || res(sCIds)
            )
            .catch(
              (err) =>
                console.error("fetchSegmentationsContactIds > err", { err }) ||
                res([])
            );
        });
      });

    return Promise.all(promises).then((results) => {
      contactIds = results.reduce(
        (all, sCIds) => [...new Set([...all, ...sCIds])],
        []
      );
      console.log(
        "fetchSegmentationsContactIds > Promise.all > contactIds",
        contactIds.length
      );
      return res(contactIds);
    });
  });

// grabHereToCopyPaste
const fetchContactsFromTriggers = async (request, response) => {
  cors(request, response, async () => {
    const postData = request.body.data;
    const accountId = postData.accountId;
    console.log("fetchContactsFromTriggers > postData", postData);
    let leads = [];
    if (!postData) {
      console.log("failed-precondition No data sent", request.body);
      return null;
    }
    console.log(
      "SEG > FETCH_CONTACTIDS_FROM_TRIGGERS > FORA > COLLECTION",
      postData.collection
    );
    console.log(
      "SEG > FETCH_CONTACTIDS_FROM_TRIGGERS > FORA > TRIGGER",
      postData.triggers
    );

    //TODO aqui
    if (postData.collection === COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME) {
      const entrances = postData.triggers.map((trigger) => {
        const entrances = [];
        console.log("SEG > TRINGER > ENTRNANCES > MAP", trigger);
        trigger.entrances.map((entrance) => {
          if (
            entrance.collection === COLLECTIONS.SHOTX_COLLECTION_NAME ||
            entrance.collection === COLLECTIONS.OTHERS_COLLECTION_NAME ||
            entrance.collection === COLLECTIONS.LOCALES_COLLECTION_NAME
          ) {
            entrances.push(entrance);
          }
          return entrance;
        });
        return entrances;
      });

      return getOthersLeads(entrances, accountId)
        .then((leadsIds) => {
          leads.push(leadsIds);
          console.log("dentro do teste", leadsIds);
          return fetchContactIdsFromTriggers(postData);
        })
        .then((contactIds) => {
          contactIds = [...leads[0], ...contactIds];
          console.log(
            "dentro do then fetchContactIdsFromTriggers",
            contactIds,
            leads[0]
          );
          return response.send({ data: { contactIds } });
        })
        .catch((error) => {
          console.log("EERROR", error);
        });
    } else {
      console.log("INSIDE FETCH_CONTACTIDS_FROM_TRIGGERS > ELSE");
      return fetchContactIdsFromTriggers(postData)
        .then((contactIds) => {
          return response.send({ data: { contactIds } });
        })
        .catch((error) => {
          const errorType = ERROR_TYPES.FETCH_CONTACTIDS_FROM_TRIGGERS;
          const errorMessage =
            typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "";
          return response.send({
            data: { error: errorType, errorMessage, errorType, debug: error },
          });
        });
    }
  });
  return null;
};

const addZeroInFront = (num) => {
  return num < 10 ? "0" + num : num.toString();
};

const checkGender = (gender) => {
  if (gender === "male") {
    return "masc";
  } else if (gender === "female") {
    return "fem";
  } else {
    return "";
  }
};
//TODO GET OTHERS LEADS
const getOthersLeads = async (entrances, accountId) => {
  let leads = [];
  const collection = COLLECTIONS.LEADS_COLLECTION_NAME;
  const bithdayMonthTrigger = entrances[0].filter(
    (entrance) => entrance.name === "to_birthday_month"
  )[0];
  const shotxTrigger = entrances[0].filter(
    (entrance) => entrance.name === "leads_added"
  )[0];
  const genderTrigger = entrances[0].filter(
    (entrance) => entrance.name === "to_gender"
  )[0];
  const ageRangeTrigger = entrances[0].filter(
    (entrance) => entrance.name === "to_age_range"
  )[0];
  const cepRangeTrigger = entrances[0].filter(
    (entrance) => entrance.name === "to_cep_range"
  )[0];
  const localeTrigger = entrances[0].filter(
    (entrance) =>
      entrance.name === "to_city" ||
      entrance.name === "to_state" ||
      entrance.name === "to_neighborhood" ||
      entrance.name === "to_country"
  )[0];

  if (bithdayMonthTrigger) {
    let bithdayMonth = bithdayMonthTrigger.birthMonth;
    bithdayMonth = addZeroInFront(bithdayMonth);
    console.log(
      "SEG > ENTRANCES > INSIDE >  to_birthday_mounth > bithdayMonth > next",
      bithdayMonth
    );

    let where = [];

    where.push(["accountId", "==", accountId]);
    where.push(["birth.month", "==", bithdayMonth]);
    let leadsFiltred = await fetchCollection(collection, where);
    leadsFiltred.map((lead) => leads.push(lead.ID));
  }
  if (shotxTrigger) {
    const instanceId = shotxTrigger.id;
    let where = [];

    where.push(["accountId", "==", accountId]);
    where.push(["instanceId", "==", instanceId]);

    const shotxLeads = await fetchCollection(collection, where);
    shotxLeads.map((lead) => leads.push(lead.ID));
  }
  if (genderTrigger) {
    let gender = genderTrigger.gender;
    gender = checkGender(gender);
    let where = [];
    console.log;
    where.push(["accountId", "==", accountId]);
    where.push(["gender", "==", gender]);

    const genderLeads = await fetchCollection(collection, where);
    genderLeads.map((lead) => leads.push(lead.ID));
  }
  if (ageRangeTrigger) {
    let rangeDateStart = ageRangeTrigger.ageRangeStart;
    let rangeDateEnd = ageRangeTrigger.ageRangeEnd;

    const date = momentNow();

    const dateStart = date.subtract({ y: rangeDateStart }).format("YYYY-MM-DD");
    const dateEnd = date.subtract({ y: rangeDateEnd }).format("YYYY-MM-DD");

    let where = [];

    where.push(["accountId", "==", accountId]);
    where.push(["birthday", "!=", ""]);

    const AllLeads = await fetchCollection(collection, where);

    if (AllLeads) {
      AllLeads.filter((lead) => {
        const birthday = moment(lead.birthday).format("YYYY-MM-DD");

        console.log(
          "FETCH_CONTACTIDS_FROM_TRIGGERS > OUTROS > birthday > ok",
          birthday,
          dateStart,
          dateEnd
        );

        if (birthday <= dateStart && birthday >= dateEnd) {
          leads.push(lead.ID);
        }
        return lead;
      });
    }
  }
  //TODO CEP RANGE
  if (cepRangeTrigger) {
    console.log("SEG > ENTRANCES > INSIDE >  CEP");
    let rangeStart = cepRangeTrigger.rangeStart.replace("-", "");
    let rangeEnd = cepRangeTrigger.rangeEnd.replace("-", "");

    let where = [];

    where.push(["accountId", "==", accountId]);
    where.push(["address.postalCode", ">=", rangeStart]);
    where.push(["address.postalCode", "<=", rangeEnd]);

    const leadsInCepWithoutFilter = await fetchCollection(collection, where);

    console.log(
      "SEG > ENTRANCES > INSIDE >  CEP > GET1 > LEADS",
      leadsInCepWithoutFilter,
      "WHERE",
      where,
      "RANGE",
      rangeStart,
      rangeEnd
    );
    where = [];

    rangeStart =
      cepRangeTrigger.rangeStart.slice(0, 5) +
      "-" +
      cepRangeTrigger.rangeStart.slice(5);
    rangeEnd =
      cepRangeTrigger.rangeEnd.slice(0, 5) +
      "-" +
      cepRangeTrigger.rangeEnd.slice(5);

    where.push(["accountId", "==", accountId]);
    where.push(["address.postalCode", ">=", rangeStart]);
    where.push(["address.postalCode", "<=", rangeEnd]);
    // console.log("SEG > ENTRANCES > INSIDE >  CEP", leadsInCep);
    const leadsInCepWithFilter = await fetchCollection(collection, where);
    console.log(
      "SEG > ENTRANCES > INSIDE >  CEP > GET2 > LEADS",
      leadsInCepWithFilter,
      "WHERE",
      where,
      "RANGE",
      rangeStart,
      rangeEnd
    );

    leadsInCepWithoutFilter.map((lead) => leads.push(lead.ID));
    leadsInCepWithFilter.map((lead) => leads.push(lead.ID));

    console.log("SEG > ENTRANCES > INSIDE > LEADS", leads);
  }
  if (localeTrigger) {
    let where = [];

    where.push(["accountId", "==", accountId]);
    switch (localeTrigger.name) {
      case "to_city":
        where.push(["address.city", "==", localeTrigger[localeTrigger.name]]);
        break;
      case "to_state":
        where.push(["address.state", "==", localeTrigger[localeTrigger.name]]);
        break;
      case "to_country":
        where.push([
          "address.country",
          "==",
          localeTrigger[localeTrigger.name],
        ]);
        break;
      case "to_neighborhood":
        where.push([
          "address.neighborhood",
          "==",
          localeTrigger[localeTrigger.name],
        ]);
        break;
    }
    // where.push([localeTrigger.name, "==", localeTrigger[localeTrigger.name]]);
    console.log("SEG > ENTRANCES > INSIDE >  where", where);
    let leadsFiltred = await fetchCollection(collection, where);
    console.log("SEG > ENTRANCES > INSIDE >  FILTREDS", leadsFiltred);
    leadsFiltred.map((lead) => leads.push(lead.ID));
  }

  leads = leads.filter((lead, index) => leads.indexOf(lead) === index);
  return leads;
};

const fetchContactIdsFromTriggers = (automation) =>
  new Promise(async (res, rej) => {
    const { accountId, triggers, config } = automation;

    let contactIds = [];
    let listenedPosts = await fetchListenedPosts({ ...automation });
    console.log("fetchContactIdsFromTriggers > listenedPosts", {
      listenedPosts:
        listenedPosts.length <= 20
          ? listenedPosts.map(({ collection, ID, id }) => ({
            collection,
            ID,
            id,
          }))
          : listenedPosts.length,
    });

    if (listenedPosts.length) {
      let listenedPostsLeadsIds = [
        ...new Set(
          listenedPosts
            .filter(
              (post) =>
                post.collection === COLLECTIONS.LEADS_COLLECTION_NAME &&
                Boolean(post.ID)
            )
            .map((post) => post.ID)
        ),
      ];
      console.log("fetchContactIdsFromTriggers > listenedPostsLeadsIds", {
        listenedPostsLeadsIds:
          listenedPostsLeadsIds.length <= 50
            ? listenedPostsLeadsIds
            : listenedPostsLeadsIds.length,
      });

      let listenedPostsContactIds = [
        ...new Set(
          listenedPosts
            .filter((post) => Boolean(post.contactId))
            .map((post) => post.contactId)
        ),
      ];
      console.log("fetchContactIdsFromTriggers > listenedPostsContactIds", {
        listenedPostsContactIds:
          listenedPostsContactIds.length <= 50
            ? listenedPostsContactIds
            : listenedPostsContactIds.length,
      });

      let entrancesPosts = [...listenedPosts];
      console.log(
        "fetchContactIdsFromTriggers > entrancesPosts > count:",
        entrancesPosts.length
      );

      contactIds = [
        ...new Set([...listenedPostsLeadsIds, ...listenedPostsContactIds]),
      ].filter((contactId) => {
        let evaluatedTriggers = evaluateTriggers({
          triggers,
          config,
          contactId,
          entrancesPosts,
        });
        // console.log(`fetchContactIdsFromTriggers > logContactIds > ${contactId} > evaluatedTriggers`,{ evaluatedTriggers });
        return Boolean(evaluatedTriggers);
      });

      console.log("fetchContactIdsFromTriggers > contactIds", {
        contactIds: contactIds.length <= 200 ? contactIds : contactIds.length,
      });
    }

    return res(contactIds);
  });

// grabHereToCopyPaste
const fetchListenedPosts = async ({
  accountId,
  triggers,
  contactId,
  automationId,
  logUpdated,
}) => {
  console.log(`FETCH_LISTENED_POSTS > START:`, {
    accountId,
    triggers,
    contactId,
    automationId,
    logUpdated,
  });

  /*
  | if contactId is not undefined we are evaluating a trigger,
  | otherwise we are evaluating a set of entrances as conditions,
  */

  const promises = [];
  const queries = [];

  const logCollections = [
    COLLECTIONS.MAILING_COLLECTION_NAME,
    COLLECTIONS.CAMPAIGNS_COLLECTION_NAME,
    COLLECTIONS.FORMS_COLLECTION_NAME,
    COLLECTIONS.QUESTIONNAIRES_COLLECTION_NAME,
    COLLECTIONS.STORES_COLLECTION_NAME,
    COLLECTIONS.INTEGRATIONS_COLLECTION_NAME,
    COLLECTIONS.LEADS_COLLECTION_NAME,
  ];

  let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
  let isValid = (date) => Boolean(date) && moment(date).isValid();

  const reduceTriggers = (a) =>
    !Array.isArray(a)
      ? []
      : a
        .filter((t) => Array.isArray(t.entrances))
        .reduce((all, t) => {
          return [
            ...all,
            ...t.entrances
              .filter((e) => e.name)
              .filter((e) => !all.find((a) => areEqualObjects(a, e))),
          ];
        }, []);

  const allEntrances = reduceTriggers(triggers).filter(
    (e) =>
      !isValid(e.start) ||
      new Date(e.start).getTime() <= new Date(momentNowISO).getTime()
  );

  const getPostDocs = async (where) => {
    let postDocQuery = FirestoreRef.collection(
      COLLECTIONS.AUTOMATIONS_COLLECTION_NAME
    )
      .doc(automationId)
      .collection(COLLECTIONS.LOGS_COLLECTION_NAME);

    where.forEach((w) => {
      postDocQuery = postDocQuery.where(w[0], w[1], w[2]);
    });

    let postResult = await postDocQuery.get();
    return postResult.docs
      .map((d) => {
        try {
          if (d.data()) {
            return d.data();
          } else {
            return null;
          }
        } catch (error) {
          console.log("getPostDocs > error", { error });
          return null;
        }
      })
      .filter((d) => d);
  };

  for (const entrance of allEntrances) {
    console.log("🚀 - LOGLISTENERS FOR ENTRANCE: ", entrance);

    let entranceQueries = [];

    let { start, end } = entrance;

    if (
      COLLECTIONS.taxonomies.includes(entrance.collection) &&
      entrance.taxName &&
      entrance.taxCollection
    ) {
      let taxId = entrance.id;
      let { taxName, taxCollection } = entrance;
      let where,
        trigger = entrance.name;

      switch (entrance.collection) {
        case COLLECTIONS.TAGS_TAXONOMY_NAME:
          if (contactId) {
            entranceQueries.push({
              collection: entrance.taxCollection,
              id: contactId,
            });
          } else if (entrance.name === APP_TRIGGERS.APP_TRIGGER_TAGHASTAG) {
            // trigger = APP_TRIGGERS.APP_TRIGGER_TAG_ADDED
            if (taxId) {
              where = [];
              where.push(["tags", "array-contains", taxId]);
              entranceQueries.push({
                collection: entrance.taxCollection,
                where,
              });
            }
          } else if (entrance.name === APP_TRIGGERS.APP_TRIGGER_TAGHASNOT) {
            /*
            | Tag has not is disabled due to the fetch payload which
            | is pttentially the entire collection of leads
            */
            // where = [];
            // where.push(["tags","",taxId]); --> this is not correct
            // entranceQueries.push({ collection: entrance.taxCollection, where })
          }
          break;
        default:
          break;
      }

      if (contactId) {
        where = [];
        where.push(["trigger", "==", trigger]);
        entranceQueries.push({
          collection: entrance.taxCollection,
          id: contactId,
          subcollection: COLLECTIONS.LOGS_COLLECTION_NAME,
          where,
          limit: 1,
        });
      } else {
        where = [];
        where.push(["trigger", "==", trigger]);
        where.push([
          CONSTANTS.KEYWORDS_FIELD,
          "array-contains",
          `[taxName-taxId]:[${taxName}-${taxId}]`,
        ]);
        entranceQueries.push({
          collectionGroup: COLLECTIONS.LOGS_COLLECTION_NAME,
          where,
          orderBy: CONSTANTS.CREATED_FIELD,
          order: "desc",
        });
      }

      where = [];
      where.push(["ID", "==", taxId]);
      entranceQueries.push({
        collection: COLLECTIONS.TAXONOMIES_COLLECTION_NAME,
        id: entrance.taxCollection,
        subcollection: entrance.taxName,
        where,
      });
    } else if (
      entrance.collection === COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME
    ) {
      entranceQueries.push({
        collection: entrance.collection,
        id: entrance.id,
        evaluate: async (segmentation) => {
          let sId = segmentation.ID,
            config = segmentation.config || {};
          let result = segmentation.status === CONSTANTS.PUBLISH_STATUS;

          if (segmentation.status === CONSTANTS.PUBLISH_STATUS) {
            let sContactIds = config.dinamic
              ? await fetchSegmentationsContactIds(sId)
              : segmentation.leads || [];
            if (contactId) {
              result = sContactIds.includes(contactId);
            }
            return { post: { ...segmentation, leads: sContactIds }, result };
          }
          return { post: segmentation, result };
        },
      });

      let where = [["trigger", "==", entrance.name]];
      let query = {
        collection: entrance.collection,
        id: entrance.id,
        subcollection: COLLECTIONS.LOGS_COLLECTION_NAME,
        where,
      };

      if (contactId) {
        query.where.push(["contactId", "==", contactId]);
        query.limit = 1;
      }

      entranceQueries.push(query);
      // teste
    } else if (entrance.collection === COLLECTIONS.EVENTS_COLLECTION_NAME) {
      entranceQueries.push({
        collection: entrance.collection,
        id: entrance.id,
      });

      let where = [];
      if (contactId) where.push(["ID", "==", contactId]);
      entranceQueries.push({
        collection: entrance.collection,
        id: entrance.id,
        subcollection: COLLECTIONS.PARTICIPANTS_COLLECTION_NAME,
        where,
      });
    } else if (
      entrance.collection === COLLECTIONS.MAILING_COLLECTION_NAME ||
      entrance.collection === COLLECTIONS.CAMPAIGNS_COLLECTION_NAME
    ) {
      // entranceQueries.push({ collection: entrance.collection, id: entrance.id });

      // let triggerName;
      // switch (entrance.name) {
      // 	case APP_TRIGGERS.APP_TRIGGER_DIDNT_OPEN:
      // 		triggerName = APP_TRIGGERS.APP_TRIGGER_OPENED;
      // 	break;
      // 	case APP_TRIGGERS.APP_TRIGGER_DIDNT_CLICK:
      // 		triggerName = APP_TRIGGERS.APP_TRIGGER_CLICKED;
      // 	break;
      // 	default:
      // 		triggerName = entrance.name;
      // 	break;
      // }

      // Resultado Consulta.
      let postDocs = [];

      // TODO mailDocQuery
      const TRIGGER_OF_SENT_EMAIL = "sent";
      let where = [
        ["context.relatedId", "==", entrance.id],
        ["trigger", "==", TRIGGER_OF_SENT_EMAIL],
        ["accountId", "==", accountId],
        ["contactId", "==", contactId],
      ];

      let mailDocs = await getPostDocs(where);

      if (mailDocs.length) {
        if (entrance.name === APP_TRIGGERS.APP_TRIGGER_CLICKED) {
          // || entrance.name === APP_TRIGGERS.APP_TRIGGER_DIDNT_CLICK) {
          let where = [
            ["trigger", "==", APP_TRIGGERS.APP_TRIGGER_CLICKED],
            ["data.mailId", "==", mailDocs[0].data.mailId],
          ];

          postDocs = await getPostDocs(where);
        } else if (entrance.name === APP_TRIGGERS.APP_TRIGGER_DELIVERED) {
          let where = [
            ["trigger", "==", "delivered"],
            ["data.mailId", "==", mailDocs[0].data.mailId],
          ];

          postDocs = await getPostDocs(where);
        } else if (entrance.name === APP_TRIGGERS.APP_TRIGGER_OPENED) {
          //|| entrance.name === APP_TRIGGERS.APP_TRIGGER_DIDNT_OPEN) {
          // fazer 2 consultas
          postDocs = [];

          let where = [
            ["trigger", "in", ["opened", "viewed"]],
            ["data.mailId", "==", mailDocs[0].data.mailId],
          ];
          postDocs = await getPostDocs(where);

          if (postDocs.length === 0) {
            let where = [
              ["trigger", "in", ["opened", "viewed"]],
              ["context.id", "==", mailDocs[0].data.mailId],
            ];

            postDocs = await getPostDocs(where);
          }
        }
      }

      return postDocs;
    } else if (entrance.collection === COLLECTIONS.PRODUCTS_COLLECTION_NAME) {
      entranceQueries.push({
        collection: entrance.collection,
        id: entrance.id,
      });

      let triggerName;
      switch (entrance.name) {
        case APP_TRIGGERS.APP_TRIGGER_NEVER_BOUGHT:
          triggerName = APP_TRIGGERS.APP_TRIGGER_BOUGHT;
          break;
        default:
          triggerName = entrance.name;
          break;
      }

      if (entrance.name === APP_TRIGGERS.APP_TRIGGER_NEVER_BOUGHT) {
        if (contactId) {
          let where = [];
          where.push(["contactId", "==", contactId]);
          where.push(["trigger", "==", triggerName]);
          let query = {
            collection: entrance.collection,
            id: entrance.id,
            subcollection: COLLECTIONS.LOGS_COLLECTION_NAME,
            where,
            orderBy: CONSTANTS.CREATED_FIELD,
            order: "desc",
            limit: 1,
          };

          entranceQueries.push(query);
        } else {
          const accountLeadsIds = await getAccountLeadsIds(accountId);

          const boughtLogs = await FirestoreRef.collectionGroup(
            COLLECTIONS.LOGS_COLLECTION_NAME
          )
            .where("id", "==", entrance.id)
            .where("collection", "==", entrance.collection)
            .where("trigger", "==", triggerName)
            .orderBy("createdAt", "desc")
            .get();

          const boughtIds = [
            ...new Set(
              boughtLogs.docs.map((d) => d.data()).map((l) => l.contactId)
            ),
          ];
          const noBoughtIds = accountLeadsIds.filter(
            (cId) => !boughtIds.includes(cId)
          );

          promises.push(
            ...noBoughtIds.map((cId) =>
              resolvePost(COLLECTIONS.LEADS_COLLECTION_NAME, cId)
            )
          );

          let where = [];
          where.push(["trigger", "==", triggerName]);
          where.push(["id", "==", entrance.id]);
          where.push(["collection", "==", entrance.collection]);

          let query = {
            collectionGroup: COLLECTIONS.LOGS_COLLECTION_NAME,
            where,
            orderBy: CONSTANTS.CREATED_FIELD,
            order: "desc",
          };

          entranceQueries.push(query);

          console.log(`fetchListenedPosts > ${entrance.collection}`, {
            entrance,
            boughtLogs: boughtLogs.size,
            boughtIds: boughtIds.length,
            noBoughtIds: noBoughtIds.length,
            accountLeadsIds: accountLeadsIds.length,
          });
        }
      } else {
        let where = [];
        where.push(["trigger", "==", triggerName]);
        let query = {
          collection: entrance.collection,
          id: entrance.id,
          subcollection: COLLECTIONS.LOGS_COLLECTION_NAME,
          where,
          orderBy: CONSTANTS.CREATED_FIELD,
          order: "desc",
        };

        if (contactId) {
          query.where.push(["contactId", "==", contactId]);
          query.limit = 1;
        }

        entranceQueries.push(query);
      }
    } else if (entrance.collection === COLLECTIONS.TICKETS_COLLECTION_NAME) {
      let triggerName;
      switch (entrance.name) {
        case APP_TRIGGERS.APP_TRIGGER_NEVER_CONVERTED:
          triggerName = COLLECTION_TRIGGERS[entrance.collection].added;
          break;
        default:
          triggerName = entrance.name;
          break;
      }

      if (triggerName) {
        let where = [];
        where.push(["trigger", "==", triggerName]);
        where.push(["accountId", "==", accountId]);
        where.push(["collection", "==", entrance.collection]);
        let query = {
          collectionGroup: COLLECTIONS.LOGS_COLLECTION_NAME,
          where,
          orderBy: CONSTANTS.CREATED_FIELD,
          order: "desc",
        };

        if (contactId) {
          query.where.push([
            CONSTANTS.KEYWORDS_FIELD,
            "array-contains",
            `[contactId]:[${contactId}]`,
          ]);
          query.limit = 1;
        } else if (entrance.name === APP_TRIGGERS.APP_TRIGGER_NEVER_CONVERTED) {
          const accountLeadsIds = await getAccountLeadsIds(accountId);

          const convertedLogs = await FirestoreRef.collectionGroup(
            COLLECTIONS.LOGS_COLLECTION_NAME
          )
            .where("accountId", "==", accountId)
            .where("collection", "==", entrance.collection)
            .where("trigger", "==", triggerName)
            .orderBy("createdAt", "desc")
            .get();

          const convertedIds = [
            ...new Set(
              convertedLogs.docs.map((d) => d.data()).map((l) => l.contactId)
            ),
          ];
          const noConvertedIds = accountLeadsIds.filter(
            (cId) => !convertedIds.includes(cId)
          );

          promises.push(
            ...noConvertedIds.map((cId) =>
              resolvePost(COLLECTIONS.LEADS_COLLECTION_NAME, cId)
            )
          );

          console.log(`fetchListenedPosts > ${entrance.collection}`, {
            entrance,
            convertedLogs: convertedLogs.size,
            convertedIds: convertedIds.length,
            noConvertedIds: noConvertedIds.length,
            accountLeadsIds: accountLeadsIds.length,
          });
        }

        entranceQueries.push(query);
      }
    } else if (entrance.collection === COLLECTIONS.FUNNELS_COLLECTION_NAME) {
      entranceQueries.push({
        collection: entrance.collection,
        id: entrance.id,
      });

      let where = [];
      where.push([CONSTANTS.FUNNEL_ID_FIELD, "==", entrance.id]);
      switch (entrance.name) {
        case APP_TRIGGERS.APP_TRIGGER_CURRENT_STAGE:
        case APP_TRIGGERS.APP_TRIGGER_STAGEIN:
        case APP_TRIGGERS.APP_TRIGGER_WON:
        case APP_TRIGGERS.APP_TRIGGER_LOST:
          where.push(["stage", "==", `${entrance.stage}`]);
          break;
        // case APP_TRIGGERS.APP_TRIGGER_STAGEOUT:
        // break;
        default:
          break;
      }
      if (contactId) where.push(["contactId", "==", contactId]);
      entranceQueries.push({
        collection: entrance.collection,
        id: entrance.id,
        relatedCollection: COLLECTIONS.DEALS_COLLECTION_NAME,
        where,
      });

      where = [];
      where.push(["trigger", "==", entrance.name]);
      where.push(["id", "==", entrance.id]);
      where.push(["collection", "==", COLLECTIONS.FUNNELS_COLLECTION_NAME]);
      let query = {
        collection: entrance.collection,
        id: entrance.id,
        subcollection: COLLECTIONS.LOGS_COLLECTION_NAME,
        where,
      };

      if (contactId) {
        query.where.push(["contactId", "==", contactId]);
        query.limit = 1;
      }

      entranceQueries.push(query);
    } else if (entrance.collection === COLLECTIONS.SHOTX_COLLECTION_NAME) {
      console.log("FETCH_LISTENED_POSTS > SHOTX ENTRANCE:", {
        entrance,
      });

      switch (entrance.name) {
        case APP_TRIGGERS.APP_TRIGGER_INTERATION_ADDED:
        case APP_TRIGGERS.APP_TRIGGER_MESSAGE_RECEIVED:
          {
            let where = [
              ["trigger", "==", entrance.name],
              ["accountId", "==", accountId],
              ["collection", "==", COLLECTIONS.LEADS_COLLECTION_NAME],
            ];

            let query = {
              collection: COLLECTIONS.LEADS_COLLECTION_NAME,
              id: contactId,
              where,
            };

            entranceQueries.push(query);
          }
          break;
        default: {
          //TODO LISTAR TODOS OS POSTS DE CADA CAMPAIGN
          console.log(`FETCH_LISTENED_POSTS > SHOTX ENTRANCE > DEFAULT:`, {
            entrance,
          });

          let where = [
            ["trigger", "==", entrance.name],
            ["accountId", "==", accountId],
            ["collection", "==", COLLECTIONS.LEADS_COLLECTION_NAME],
          ];

          const logId = logUpdated && logUpdated.id ? logUpdated.id : null;

          let query = {
            collection: COLLECTIONS.LEADS_COLLECTION_NAME,
            id: contactId,
            subcollection: COLLECTIONS.LOGS_COLLECTION_NAME,
            where,
            logId,
          };

          entranceQueries.push(query);
          console.log(`FETCH_LISTENED_POSTS > SHOTX ENTRANCE: `, {
            where,
            entranceQueries,
            entrance,
          });
        }
      }
    } else if (entrance.id) {
      console.log(`FETCH_LISTENED_POSTS > ENTRANCE ID: `, { entrance });

      entranceQueries.push({
        collection: entrance.collection,
        id: entrance.id,
      });

      let where = [];
      where.push(["trigger", "==", entrance.name]);
      let query = {
        collection: entrance.collection,
        id: entrance.id,
        subcollection: COLLECTIONS.LOGS_COLLECTION_NAME,
        where,
      };

      if (contactId) {
        query.where.push(["contactId", "==", contactId]);
        query.limit = 1;
      }

      entranceQueries.push(query);
    } else if (logCollections.includes(entrance.collection)) {
      console.log(`FETCH_LISTENED_POSTS > ENTRANCE DEFAULT: `, { entrance });

      let where = [];
      where.push(["trigger", "==", entrance.name]);
      where.push(["accountId", "==", accountId]);
      where.push(["collection", "==", entrance.collection]);
      let query = {
        collectionGroup: COLLECTIONS.LOGS_COLLECTION_NAME,
        where,
        orderBy: CONSTANTS.CREATED_FIELD,
        order: "desc",
      };

      if (contactId) {
        query.where.push([
          CONSTANTS.KEYWORDS_FIELD,
          "array-contains",
          `[contactId]:[${contactId}]`,
        ]);
        query.limit = 1;
      }

      entranceQueries.push(query);
    }

    queries.push(
      ...entranceQueries.map((q) => {
        let { subcollection, collectionGroup } = q;

        if (
          [subcollection, collectionGroup].includes(
            COLLECTIONS.LOGS_COLLECTION_NAME
          )
        ) {
          if (isValid(start)) {
            q.orderBy = CONSTANTS.CREATED_FIELD;
            q.order = "desc";
            if (!q.where) q.where = [];
            q.where.push([
              CONSTANTS.CREATED_FIELD,
              ">=",
              moment(start).valueOf(),
            ]);
          }
          if (isValid(end)) {
            q.orderBy = CONSTANTS.CREATED_FIELD;
            q.order = "desc";
            if (!q.where) q.where = [];
            q.where.push([
              CONSTANTS.CREATED_FIELD,
              "<=",
              moment(end).valueOf(),
            ]);
          }
        }

        return q;
      })
    );
  } // END FOR EACH ENTRANCE

  console.log(`FETCH_LISTENED_POSTS > QUERIES: `, { queries });
  if (contactId) {
    queries.push({
      collection: COLLECTIONS.LEADS_COLLECTION_NAME,
      id: contactId,
    });
  }

  queries
    .reduce(
      (all, q) => [
        ...all,
        ...[q.evaluate || !all.find((a) => areEqualObjects(a, q)) ? q : null],
      ],
      []
    )
    .filter((r) => r)
    .forEach((q) => {
      if (q.collectionGroup) {
        let promise = new Promise((res) => {
          let queryRef = FirestoreRef.collectionGroup(q.collectionGroup);
          if (q.where)
            q.where.forEach((w) => {
              queryRef = queryRef.where(w[0], w[1], w[2]);
            });
          if (q.orderBy)
            queryRef = queryRef.orderBy(q.orderBy, q.order || "desc");
          if (q.limit) queryRef = queryRef.limit(q.limit);
          queryRef
            .get()
            .then((snapshot) => {
              const posts = snapshot.docs.map((d) => d.data());
              return res(posts);
            })
            .catch((error) => {
              console.error(error);
              return res([]);
            });
        });
        promises.push(promise);
      } else if (q.subcollection) {
        let promise = new Promise((res) => {
          fetchSubCollection(
            q.collection,
            q.id,
            q.subcollection,
            q.where,
            q.limit,
            q.orderBy,
            q.order
          )
            .then((sub) => {
              return res(sub.map((s) => ({ ...s, query: q })));
            })
            .catch((error) => {
              console.error(error);
              return res([]);
            });
        });

        promises.push(promise);
      } else if (q.relatedCollection) {
        let promise = new Promise((res) => {
          let queryRef = FirestoreRef.collection(q.relatedCollection);
          if (q.where)
            q.where.forEach((w) => {
              queryRef = queryRef.where(w[0], w[1], w[2]);
            });
          if (q.orderBy)
            queryRef = queryRef.orderBy(q.orderBy, q.order || "desc");
          if (q.limit) queryRef = queryRef.limit(q.limit);
          queryRef
            .get()
            .then((snapshot) => {
              const sub = [];
              snapshot.forEach((doc) =>
                sub.push({ ...doc.data(), related: q })
              );
              return res(sub);
            })
            .catch((error) => {
              console.error(error);
              return res([]);
            });
        });

        promises.push(promise);
      } else if (q.evaluate) {
        let promise = new Promise((res) => {
          return fetchPost(q.collection, q.id, true)
            .then((post) => q.evaluate(post))
            .then(({ post, result }) => {
              console.log("evaluate result:", result);
              return result ? res(post) : res([]);
            })
            .catch((error) => {
              console.error(error);
              return res([]);
            });
        });

        promises.push(promise);
      } else if (q.collection && q.id) {
        promises.push(resolvePost(q.collection, q.id, true));
      } else if (q.collection) {
        promises.push(
          new Promise((resolveCollection) =>
            fetchCollection(
              q.collection,
              q.where,
              q.limit,
              q.orderBy,
              q.order,
              q.or
            )
              .then(resolveCollection)
              .catch((err) => {
                console.log("err queries reduce", { q, err });
                resolveCollection(null);
              })
          )
        );
      }
    });

  // TODO promise.all
  return Promise.all(promises)
    .then((results) => {
      console.log(`FETCH_LISTENED_POSTS > RESULTS: `, { results });
      return results.reduce(
        (all, r) =>
          [...all, ...(Array.isArray(r) ? r : [r])].filter(isLoopableObject),
        []
      );
    })
    .then(async (posts) => {
      const logs = posts.filter((p) => p.logId);

      const pendingPostsMap = logs.reduce((all, log) => {
        let { id, collection } = log;
        if (
          id &&
          collection &&
          COLLECTIONS.coreCollections.includes(collection) &&
          !posts.find((p) => p.collection === collection && p.ID === id)
        ) {
          return {
            ...all,
            [collection]: [...new Set([...(all[collection] || []), id])],
          };
        }
        return all;
      }, {});

      if (isLoopableObject(pendingPostsMap)) {
        const promises = [];
        Object.keys(pendingPostsMap).forEach((collection, i) => {
          const pendingIds = pendingPostsMap[collection];
          promises.push(
            ...pendingIds.map((id) => resolvePost(collection, id, true))
          );
        });
        const pendingPosts = await Promise.all(promises);
        console.log("FETCH_LISTENED_POSTS > PENDING POSTS", {
          pendingPosts: pendingPosts.map((p) =>
            helpers.objectDebugger(p, ["ID", "collection"])
          ),
        });
        return [...posts, ...pendingPosts];
      }

      console.log(`FETCH_LISTENED_POSTS > RESULTS > LOGS AND POSTS: `, {
        logs,
        posts,
      });

      return posts;
    })
    .catch((err) => {
      console.error("FETCH_LISTENED_POSTS > ERROR", { err });
      return [];
    });
};

// grabHereToCopyPaste
const evaluateTriggers = ({
  triggers,
  config,
  contactId,
  entrancesPosts,
  deal,
  hasDeal,
}) => {
  entrancesPosts = entrancesPosts.filter((eP) => isLoopableObject(eP));

  let trigger_relation = config.trigger_relation || "and";

  let debug = {
    contactId,
    triggers,
    trigger_relation,
    groups: [],
    results: {},
  },
    loops = [];

  let postMatch = false;
  let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
  console.log(` EVALUATE_TRIGGERS > START:`, {
    triggers,
    config,
    contactId,
    entrancesPosts,
    deal,
  });

  if (Array.isArray(triggers)) {
    let postMatches = [];

    let evaluateDate = ({ start, end, date }) => {
      if (
        date &&
        moment(date).isValid() &&
        start &&
        moment(start).isValid() &&
        end &&
        moment(end).isValid()
      ) {
        return (
          new Date(date).getTime() >= new Date(start).getTime() &&
          new Date(date).getTime() <= new Date(end).getTime()
        );
      }
      if (
        start &&
        moment(start).isValid() &&
        new Date(date).getTime() < new Date(start).getTime()
      ) {
        return false;
      }
      if (
        end &&
        moment(end).isValid() &&
        new Date(date).getTime() > new Date(end).getTime()
      ) {
        return false;
      }
      return true;
    };

    triggers.forEach((triggerObj) => {
      if (postMatch) return;

      const { relation, entrances } = triggerObj;

      let groupMatch = false;
      let groupMatches = [];
      let groupTriggers = [];

      if (Array.isArray(entrances))
        entrances.forEach((entrance) => {
          if (postMatch || groupMatch) return;

          try {
            let { name, collection, start, end } = entrance;

            let triggerName;
            switch (entrance.name) {
              case APP_TRIGGERS.APP_TRIGGER_DIDNT_OPEN:
                triggerName = APP_TRIGGERS.APP_TRIGGER_OPENED;
                break;
              case APP_TRIGGERS.APP_TRIGGER_DIDNT_CLICK:
                triggerName = APP_TRIGGERS.APP_TRIGGER_CLICKED;
                break;
              case APP_TRIGGERS.APP_TRIGGER_NEVER_BOUGHT:
                triggerName = APP_TRIGGERS.APP_TRIGGER_BOUGHT;
                break;
              case APP_TRIGGERS.APP_TRIGGER_NEVER_CONVERTED:
                triggerName = COLLECTION_TRIGGERS[entrance.collection].added;
                break;
              default:
                triggerName = entrance.name;
                break;
            }

            let evaluated = false;
            let timeMatch = evaluateDate({ start, end, date: momentNowISO });
            let eMatch = false;
            let eDateMatch;

            let relatedPosts = entrancesPosts
              .filter((rP) => rP.related)
              .filter(
                (rP) =>
                  (!rP.related.id && !entrance.id) ||
                  entrance.id === rP.related.id
              )
              .filter((rP) => rP.related.collection === entrance.collection);
            console.log(`EVALUATE_TRIGGERS > RELATED_POSTS:`, { relatedPosts });

            let eChildren = entrancesPosts
              .filter((eP) => eP.query && !eP.logId)
              .filter(
                (eP) =>
                  (!eP.query.id && !entrance.id) || entrance.id === eP.query.id
              )
              .filter((eP) => eP.query.collection === entrance.collection);
            console.log(`EVALUATE_TRIGGERS > E_CHILDREN:`, { eChildren });

            // TODO elogs
            let eLogs = [];

            if (
              entrance.collection === COLLECTIONS.SHOTX_COLLECTION_NAME &&
              entrance.name === APP_TRIGGERS.APP_TRIGGER_MESSAGE_RECEIVED
            ) {
              postMatches.push(Boolean(true));
            }

            if (entrance.collection !== COLLECTIONS.MAILING_COLLECTION_NAME) {
              // TODO: verificar os filtros alterados
              eLogs = entrancesPosts
                .filter((eP) => eP.logId)
                .filter((log) => log.trigger === triggerName)
                // .filter(log=>log.trigger===triggerName && (!entrance.id || log.id===entrance.id))
                .filter((log) => !log.contactId || log.contactId === contactId)
                .filter(
                  (log) =>
                    log.collection === entrance.collection ||
                    (log.collection === LEADS_COLLECTION_NAME &&
                      entrance.collection === COLLECTIONS.SHOTX_COLLECTION_NAME)
                )
                .filter(({ date }) => evaluateDate({ start, end, date }))
                .sort(helpers.sortByUpdateDate);
              console.log(`EVALUATE_TRIGGERS > E_LOGS:`, { eLogs });
            }

            let lastLog = [...eLogs].pop() || {};
            let lastLogDate = lastLog.date || momentNowISO;
            let lastLogDateMatch =
              (!start && !end) ||
              evaluateDate({ start, end, date: lastLogDate });
            if (!lastLogDateMatch) lastLog = {};

            let shotx = false;
            if (entrance.collection === "shotx") {
              console.log(
                "EVALUATE_TRIGGERS > SHOTX ENTRANCEPOSTS:",
                entrancesPosts
              );
              console.log(`EVALUATE_TRIGGERS > ENTRANCES:`, entrance);
              const leadsEntraces = entrancesPosts.filter(
                (eP) => eP.collection === COLLECTIONS.LEADS_COLLECTION_NAME
              );
              //TODO VERIFICAR OS FILTROS
              // .filter(
              //   (eP) =>
              //     eP.context &&
              //     eP.context.collection === COLLECTIONS.SHOTX_COLLECTION_NAME
              // );
              if (leadsEntraces) {
                shotx = true;
              }
              console.log(`EVALUATE_TRIGGERS > LEADS_ENTRANCES:`, {
                leadsEntraces,
                shotx,
              });
            }

            let ePost = entrancesPosts.find(
              (eP) =>
                (eP.collection === entrance.collection || shotx) &&
                ((Boolean(entrance.id) && eP.ID === entrance.id) ||
                  (!entrance.id && lastLog.id && lastLog.id === eP.ID))
            );
            console.log(`EVALUATE_TRIGGERS > E_POST:`, { ePost });

            let relatedPost;

            if (!evaluated) {
              switch (entrance.collection) {
                case COLLECTIONS.TAGS_TAXONOMY_NAME:
                  // lead or deal
                  ePost =
                    deal &&
                      deal.collection === entrance.taxCollection &&
                      (deal.contactId === contactId || deal.ID === contactId)
                      ? deal
                      : entrancesPosts.find(
                        (eP) =>
                          !eP.logId &&
                          eP.collection === entrance.taxCollection &&
                          (eP.contactId === contactId || eP.ID === contactId)
                      ) || {};
                  console.log("EVALUATE_TRIGGERS > TAGS > ePOST", ePost);
                  if (ePost) {
                    // tag
                    const tagPost = entrancesPosts.find(
                      (eP) =>
                        !eP.logId &&
                        eP.query &&
                        eP.query.collection ===
                        COLLECTIONS.TAXONOMIES_COLLECTION_NAME &&
                        eP.query.id === entrance.taxCollection &&
                        eP.ID === entrance.id
                    );
                    console.log("EVALUATE_TRIGGERS > TAGS > TAGPOST", tagPost);
                    switch (entrance.name) {
                      case APP_TRIGGERS.APP_TRIGGER_TAGHASTAG:
                        eMatch =
                          timeMatch &&
                          Boolean(tagPost) &&
                          (ePost.tags || []).includes(entrance.id);
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_TAG_ADDED:
                        eMatch =
                          Boolean(tagPost) &&
                          (ePost.tags || []).includes(entrance.id);
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_TAGHASNOT:
                        eMatch = !(ePost.tags || []).includes(entrance.id);
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_TAG_REMOVED:
                        eMatch =
                          Boolean(eLogs.length) &&
                          !(ePost.tags || []).includes(entrance.id);
                        break;
                      default:
                        break;
                    }
                  }
                  break;
                case COLLECTIONS.EVENTS_COLLECTION_NAME:
                  if (ePost) {
                    let eParticipant = eChildren.find(
                      (ePart) => ePart.ID === contactId
                    );
                    // log('eParticipant',eParticipant);
                    switch (entrance.name) {
                      case APP_TRIGGERS.APP_TRIGGER_ADDED_PARTICIPANT:
                        eMatch = Boolean(eParticipant);
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_REMOVED_PARTICIPANT:
                        eMatch =
                          Boolean(eLogs.length) &&
                          Boolean(eParticipant) !== true;
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_CONFIRMED:
                        eMatch = eParticipant && eParticipant.confirmed;
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_UNCONFIRMED:
                        eMatch =
                          Boolean(eLogs.length) &&
                          eParticipant &&
                          !eParticipant.confirmed;
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_CHECKEDIN:
                        eMatch = eParticipant && eParticipant.checkin;
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_DIDNTCHECKIN:
                        eMatch =
                          eParticipant &&
                          !eParticipant.checkin &&
                          moment(ePost.end).isValid() &&
                          new Date(ePost.end).getTime() < new Date().getTime();
                        eDateMatch = timeMatch;
                        break;
                      default:
                        break;
                    }
                  }
                  break;
                case COLLECTIONS.FUNNELS_COLLECTION_NAME:
                  if (ePost) {
                    relatedPost = relatedPosts
                      .filter((eDeal) => eDeal.contactId === contactId)
                      .find((deal) => {
                        let dealMatch = false;
                        switch (entrance.name) {
                          case APP_TRIGGERS.APP_TRIGGER_ADDED_TO_FUNNEL:
                            dealMatch = true;
                            break;
                          case APP_TRIGGERS.APP_TRIGGER_WON:
                            dealMatch = `${deal.stage}` === "won";
                            break;
                          case APP_TRIGGERS.APP_TRIGGER_LOST:
                            dealMatch = `${deal.stage}` === "lost";
                            break;
                          case APP_TRIGGERS.APP_TRIGGER_CURRENT_STAGE:
                            dealMatch = `${deal.stage}` === `${entrance.stage}`;
                            eDateMatch = timeMatch;
                            break;
                          case APP_TRIGGERS.APP_TRIGGER_STAGEIN:
                            dealMatch =
                              `${deal.stage}` === `${entrance.stageIn}` &&
                              eLogs.find(
                                (log) =>
                                  /* !log.evaluated && */ `${(log.data || {}).currentStage
                                  }` === `${deal.stage}`
                              );
                            break;
                          case APP_TRIGGERS.APP_TRIGGER_STAGEOUT:
                            dealMatch = eLogs.find(
                              (log) =>
                                /* !log.evaluated && */ `${(log.data || {}).prevStage
                                }` === `${deal.stage}`
                            );
                            break;
                          case APP_TRIGGERS.APP_TRIGGER_PROGRESSED:
                            dealMatch = eLogs.find((log) => {
                              return (
                                /* !log.evaluated && */ `${(log.data || {}).currentStage
                                }` &&
                                !isNaN(parseInt(log.data.currentStage)) &&
                                !isNaN(parseInt(log.data.prevStage)) &&
                                parseInt(log.data.currentStage) >
                                parseInt(log.data.prevStage)
                              );
                            });
                            break;
                          case APP_TRIGGERS.APP_TRIGGER_REGRESSED:
                            dealMatch = eLogs.find((log) => {
                              return (
                                /* !log.evaluated && */ `${(log.data || {}).currentStage
                                }` &&
                                !isNaN(parseInt(log.data.currentStage)) &&
                                !isNaN(parseInt(log.data.prevStage)) &&
                                parseInt(log.data.currentStage) <
                                parseInt(log.data.prevStage)
                              );
                            });
                            break;
                          default:
                            break;
                        }
                        return dealMatch;
                      });
                    eMatch = Boolean(relatedPost);
                  }
                  break;
                case COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME:
                  if (ePost && ePost.status === CONSTANTS.PUBLISH_STATUS) {
                    switch (entrance.name) {
                      case APP_TRIGGERS.APP_TRIGGER_ADDED_TO_SEGMENTATION:
                        eMatch =
                          Boolean(eLogs.length) ||
                          (ePost.leads || []).includes(contactId);
                        break;
                      case APP_TRIGGERS.APP_TRIGGER_REMOVED_FROM_SEGMENTATION:
                        eMatch =
                          Boolean(eLogs.length) &&
                          !(ePost.leads || []).includes(contactId);
                        break;
                      default:
                        break;
                    }
                  }
                  break;
                case COLLECTIONS.TASKLISTS_COLLECTION_NAME:
                  if (Boolean(ePost) && Array.isArray(ePost.tasks)) {
                    let eTasks = ePost.tasks;
                    switch (entrance.name) {
                      case APP_TRIGGERS.APP_TRIGGER_COMPLETED:
                        eMatch =
                          eTasks.filter((l) => l.completed).length ===
                          eTasks.length;
                        break;
                      default:
                        break;
                    }
                  } else if (
                    Boolean(ePost) &&
                    isLoopableObject(ePost.tasks) &&
                    Array.isArray((ePost.tasks[entrance.id] || {}).tasks)
                  ) {
                    let eTasks = ePost.tasks[entrance.id].tasks;
                    switch (entrance.name) {
                      case APP_TRIGGERS.APP_TRIGGER_COMPLETED:
                        eMatch =
                          eTasks.filter((l) => l.completed).length ===
                          eTasks.length;
                        break;
                      default:
                        break;
                    }
                  }
                  break;
                case COLLECTIONS.TICKETS_COLLECTION_NAME:
                  switch (entrance.name) {
                    case COLLECTION_TRIGGERS[entrance.collection].added:
                      eMatch =
                        Boolean(ePost) &&
                        ePost.contactId === contactId &&
                        ePost.status === CONSTANTS.PUBLISH_STATUS;
                      break;
                    case APP_TRIGGERS.APP_TRIGGER_NEVER_CONVERTED:
                      eMatch = !eLogs.length;
                      break;
                    case APP_TRIGGERS.APP_TRIGGER_TICKET_CANCELED:
                      eMatch =
                        Boolean(ePost) &&
                        ePost.contactId === contactId &&
                        ePost.status !== CONSTANTS.PUBLISH_STATUS;
                      break;
                    default:
                      break;
                  }
                  break;
                case COLLECTIONS.PRODUCTS_COLLECTION_NAME:
                  switch (entrance.name) {
                    case APP_TRIGGERS.APP_TRIGGER_BOUGHT:
                      eMatch = Boolean(ePost) && Boolean(eLogs.length);
                      break;
                    case APP_TRIGGERS.APP_TRIGGER_NEVER_BOUGHT:
                      eMatch = Boolean(ePost) && !eLogs.length;
                      break;
                    default:
                      break;
                  }
                  break;
                case COLLECTIONS.STORES_COLLECTION_NAME:
                  eMatch =
                    Boolean(ePost) &&
                    ePost.status === CONSTANTS.PUBLISH_STATUS &&
                    Boolean(eLogs.length);
                  break;
                case COLLECTIONS.MAILING_COLLECTION_NAME:
                case COLLECTIONS.CAMPAIGNS_COLLECTION_NAME:
                case COLLECTIONS.FORMS_COLLECTION_NAME:
                case COLLECTIONS.QUESTIONNAIRES_COLLECTION_NAME:
                case COLLECTIONS.INTEGRATIONS_COLLECTION_NAME:
                case COLLECTIONS.LEADS_COLLECTION_NAME:
                case COLLECTIONS.SHOTX_COLLECTION_NAME:
                  if (
                    entrance.collection === COLLECTIONS.MAILING_COLLECTION_NAME
                  ) {
                    let triggerArray =
                      entrance.name === "opened"
                        ? ["opened", "viewed"]
                        : [entrance.name];
                    eLogs = entrancesPosts
                      .filter((eP) => eP.logId)
                      .filter((log) => triggerArray.includes(log.trigger))
                      .filter(
                        (log) => !log.contactId || log.contactId === contactId
                      )
                      .filter(({ date }) => evaluateDate({ start, end, date }))
                      .sort(helpers.sortByUpdateDate);
                  }
                  // logs
                  console.log("EVALUATE_TRIGGERS > E_LOGS:", {
                    eLogs,
                    entrance,
                  });
                  switch (entrance.name) {
                    case APP_TRIGGERS.APP_TRIGGER_DIDNT_OPEN:
                    case APP_TRIGGERS.APP_TRIGGER_DIDNT_CLICK:
                      eMatch = !eLogs.length;
                      break;
                    case APP_TRIGGERS.APP_TRIGGER_MESSAGE_RECEIVED:
                    case APP_TRIGGERS.APP_TRIGGER_INTERATION_ADDED:
                      eMatch = true;

                      break;
                    default:
                      eMatch = Boolean(eLogs.length);

                      break;
                  }
                  break;
                case COLLECTIONS.DEALS_COLLECTION_NAME:
                  // Se não existe Deal won ou lost retorna True
                  eMatch = Boolean(hasDeal);

                  break;
                default:
                  break;
              }
            }

            if (
              ePost &&
              COLLECTION_TRIGGERS[collection] &&
              name === COLLECTION_TRIGGERS[collection].added
            ) {
              eDateMatch = evaluateDate({ start, end, date: ePost.date });
            } else if (eDateMatch === undefined) {
              eDateMatch = lastLogDateMatch;
            }
            console.log(`EVALUATE_TRIGGERS > E_DATE_MATCH:`, { eDateMatch });

            let isMatch = eMatch && eDateMatch;
            console.log(`EVALUATE_TRIGGERS > IS_MATCH:`, { isMatch });
            console.log(`EVALUATE_TRIGGERS > ENTRANCE:`, { entrance });

            groupMatches.push(Boolean(isMatch));

            if (isMatch && relation === "or") groupMatch = true;

            loops.push({
              isMatch,
              eMatch,
              eDateMatch,
              timeMatch,
              lastLogDateMatch,
              entrance,
              groupMatch,
              result: `${entrance.name}: ${Boolean(isMatch)} (in ${ePost && (ePost.ID || ePost.id)
                } ${ePost && ePost.collection})`,
              lastLog: helpers.objectDebugger(lastLog, [
                "id",
                "collection",
                "date",
                "trigger",
              ]),
              eLogs: eLogs.map((l) =>
                helpers.objectDebugger(l, [
                  "id",
                  "collection",
                  "date",
                  "trigger",
                ])
              ),
              eChildren: eLogs.map((c) =>
                helpers.objectDebugger(c, ["ID", "collection", "date"])
              ),
              relatedPost: helpers.objectDebugger(relatedPost, [
                "ID",
                "collection",
                "date",
              ]),
              ePost: helpers.objectDebugger(ePost, [
                "ID",
                "collection",
                "date",
              ]),
            });
          } catch (error) {
            console.error(error);
          }

          groupTriggers.push(entrance.name);
        });

      groupMatch =
        relation === "or"
          ? groupMatches.includes(true)
          : !groupMatches.includes(false);
      console.log(`EVALUATE_TRIGGERS > GROUP_MATCH + GROUPMATCHES:`, {
        groupMatch,
        groupMatches,
      });

      postMatches.push(Boolean(groupMatch));
      console.log(`EVALUATE_TRIGGERS > POST_MATCHES:`, { postMatches });

      if (groupMatch && trigger_relation === "or") postMatch = true;
      console.log(`EVALUATE_TRIGGERS > POST_MATCHES:`, { postMatches });

      debug.groups.push({
        groupMatch,
        groupMatches,
        groupTriggers,
        postMatch,
        loops: JSON.parse(JSON.stringify(loops)),
      });
      loops = [];
    });

    postMatch =
      trigger_relation === "or"
        ? postMatches.includes(true)
        : !postMatches.includes(false);
    console.log(`EVALUATE_TRIGGERS > POST_MATCH + POSTMATCHES:`, {
      postMatch,
      postMatches,
    });

    debug.results = {
      postMatch,
      postMatches,
    };

    console.log(`EVALUATE_TRIGGERS > TRIGGERS > DEBUG:`, { debug });
  }

  debug.entrancesPosts =
    entrancesPosts.length <= 20
      ? entrancesPosts.map((eP) => ({
        collection: eP.collection,
        id: eP.id || eP.ID,
        query: eP.query,
      }))
      : entrancesPosts.length;
  debug = replaceUndefined(debug);

  console.log(`EVALUATE_TRIGGERS > RESULT > DEBUG:`, { debug });

  return postMatch;
};

const getSizeDeal = async (accountId) => {
  let where = [];
  where.push(["stage", "not-in", ["won", "lost"]]);
  where.push(["accountId", "==", accountId]);

  let ref = FirestoreRef.collection(COLLECTIONS.DEALS_COLLECTION_NAME);

  where.forEach((w) => {
    ref = ref.where(w[0], w[1], w[2]);
  });

  ref.limit(1);

  const snapshot = await ref.get();
  const sizeDeal = snapshot.docs.length;

  // Não existe Negocio Ativo
  // Se encontrar algum deal ativo ( != de won e lost ) retorna false
  let resultHasDeal = sizeDeal < 1;

  return resultHasDeal;
};

// grabHereToCopyPaste
const evaluateAutomationTriggers = async (
  automation,
  contactId,
  deal,
  logUpdated
) => {
  console.log(`EVALUATE_AUTOMATIONS_TRIGGERS > START > AUTOMATION:`, {
    automation,
  });

  console.log(`EVALUATE_AUTOMATIONS_TRIGGERS > START > CONTACTID:`, {
    contactId,
  });

  console.log(`EVALUATE_AUTOMATIONS_TRIGGERS > START > DEAL:`, {
    deal,
  });

  console.log(`EVALUATE_AUTOMATIONS_TRIGGERS > START > LOGUPDATED:`, {
    logUpdated,
  });
  const { accountId, config, ID } = automation;

  // TODO evaluate trigger
  let evaluate = async (entrances, relation) => {
    let result = false;
    if (relation === "or") {
      for (const entrance of entrances) {
        const triggers = [{ relation, entrances: [entrance] }];
        // eslint-disable-next-line no-await-in-loop
        const entrancesPosts = await fetchListenedPosts({
          accountId,
          triggers,
          contactId,
          automationId: ID,
          logUpdated,
        });

        let hasDeal;
        let resultHasDeal = false;
        if (Array.isArray(triggers)) {
          triggers.forEach((triggerObj) => {
            const { entrances } = triggerObj;
            if (Array.isArray(entrances)) {
              entrances.forEach(async (entrance) => {
                if (entrance.collection === COLLECTIONS.DEALS_COLLECTION_NAME) {
                  resultHasDeal = true;
                }
              });
            }
          });
        }

        if (resultHasDeal) {
          hasDeal = await getSizeDeal(accountId);
        }

        result = evaluateTriggers({
          triggers,
          config: { ...config, trigger_relation: relation },
          contactId,
          entrancesPosts,
          deal,
          hasDeal,
        });

        if (result) break;
      }
    } else {
      const triggers = [{ relation, entrances }];

      const entrancesPosts = await fetchListenedPosts({
        accountId,
        triggers,
        contactId,
        automationId: ID,
      });

      let hasDeal;
      let resultHasDeal = false;
      if (Array.isArray(triggers)) {
        triggers.forEach((triggerObj) => {
          const { entrances } = triggerObj;
          if (Array.isArray(entrances)) {
            entrances.forEach(async (entrance) => {
              if (entrance.collection === COLLECTIONS.DEALS_COLLECTION_NAME) {
                resultHasDeal = true;
              }
            });
          }
        });
      }

      if (resultHasDeal) {
        hasDeal = await getSizeDeal(accountId);
      }

      //const hasDeal = await getSizeDeal(triggers, entrancesPosts);
      result = evaluateTriggers({
        triggers,
        config: { ...config, trigger_relation: relation },
        contactId,
        entrancesPosts,
        deal,
        hasDeal: hasDeal || false,
      });
    }
    return result;
  };

  let postMatches = [];
  let { triggers } = automation;
  let trigger_relation = (config || {}).trigger_relation || "and";
  let triggersGroups = Array.isArray(triggers)
    ? triggers.filter((t) => Array.isArray(t.entrances) && t.entrances.length)
    : [];

  for (const { entrances, relation } of triggersGroups) {
    // eslint-disable-next-line no-await-in-loop
    let groupMatch = await evaluate(entrances, relation);

    postMatches.push(Boolean(groupMatch));
    if (groupMatch && trigger_relation === "or") {
      break;
    } else if (!groupMatch && trigger_relation === "and") {
      break;
    }
  }

  let postMatch =
    trigger_relation === "or"
      ? postMatches.includes(true)
      : !postMatches.includes(false);

  return postMatch ? automation : null;
};

// grabHereToCopyPaste
const automationCron = () => {
  const fetchJobs = () => {
    return new Promise((res) => {
      let now = momentNow().format(MOMENT_ISO);
      return FirestoreRef.collection(COLLECTIONS.CRONJOBS_COLLECTION_NAME)
        .where("type", "==", CRONJOB_TYPES.AUTOMATION)
        .where("executed", "==", false)
        .where("scheduled_date", "<=", now)
        .get()
        .then((snapshot) => {
          let jobs = [];
          snapshot.forEach((doc) => {
            const data = doc.data();
            if (data.automation) {
              jobs.push(data);
              doc.ref.update({
                executed: true,
                execution_date: momentNow().format(MOMENT_ISO),
              });
            }
          });
          return res({ jobs });
        });
    });
  };

  const fetchContacts = ({ jobs, contactIds, dealIds }) => {
    return new Promise((res, rej) => {
      const promises = [];

      contactIds.forEach((cId) => {
        promises.push(
          resolvePost(COLLECTIONS.LEADS_COLLECTION_NAME, cId, true)
        );
      });
      dealIds.forEach((dId) => {
        promises.push(
          resolvePost(COLLECTIONS.DEALS_COLLECTION_NAME, dId, true)
        );
      });

      if (promises.length) {
        return Promise.all(promises).then((results) => {
          return res({
            jobs,
            contacts: results.filter(
              (r) => r && r.collection === COLLECTIONS.LEADS_COLLECTION_NAME
            ),
            deals: results.filter(
              (r) => r && r.collection === COLLECTIONS.DEALS_COLLECTION_NAME
            ),
          });
        });
      }

      return res({ jobs, contacts: [], deals: [] });
    });
  };

  const fetchAutomations = ({ jobs, contacts, deals }) => {
    return new Promise((res, rej) => {
      if (jobs.length) {
        let queries = [];

        jobs.forEach((j) => {
          const {
            context,
            context: { collection, id },
          } = j;
          if (
            !queries.find((q) => q.collection === collection && q.id === id)
          ) {
            queries.push({ ...context });
          }
        });

        let promises = queries.map((q) => {
          const { collection, id } = q;
          return fetchPost(collection, id);
        });

        return Promise.all(promises).then((automations) => {
          automations = automations.filter((a) => {
            let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
            let isExpired =
              a.end &&
              moment(a.end).isValid() &&
              new Date(a.end).getTime() < new Date(momentNowISO).getTime();
            return !isExpired && a.status === CONSTANTS.PUBLISH_STATUS;
          });
          return res({ jobs, contacts, deals, automations });
        });
      }
      return res({ jobs, contacts, deals, automations: [] });
    });
  };

  const fireActions = ({ jobs, contacts, deals, automations }) =>
    new Promise(async (res) => {
      let promises = [];

      for (const job of jobs) {
        const {
          context: { collection, id },
        } = job;
        const contact = contacts.find((c) => c.ID === job.contactId);
        const deal = deals.find((c) => c.ID === job.dealId);
        const automation = automations.find(
          (a) => a.collection === collection && a.ID === id
        );
        const actions = job.actions;

        if (contact && automation) {
          const result = await evaluateAutomationTriggers(
            automation,
            job.contactId,
            deal
          );
          if (result)
            promises.push(doActions({ contact, deal, automation, actions }));
        }
      }

      return Promise.all(promises)
        .then((results) => res({ results }))
        .catch((error) => console.error(error) || res({ error }));
    });

  return fetchJobs()
    .then(({ jobs }) => {
      // console.log('fetchJobs > jobs',jobs && jobs.length);
      if (jobs && jobs.length) {
        let contactIds = [
          ...new Set(jobs.filter((j) => j.contactId).map((j) => j.contactId)),
        ];
        let dealIds = [
          ...new Set(jobs.filter((j) => j.dealId).map((j) => j.dealId)),
        ];
        return fetchContacts({ jobs, contactIds, dealIds });
      }
      return { jobs, contacts: [], deals: [] };
    })
    .then(({ jobs, contacts, deals }) => {
      // console.log('fetchContacts > contacts', contacts.length);
      if (contacts.length || deals.length) {
        return fetchAutomations({ jobs, contacts, deals });
      }
      return { jobs, contacts, deals, automations: [] };
    })
    .then(({ jobs, contacts, deals, automations }) => {
      // console.log('fetchAutomations > automations', automations.length);
      if (automations.length) {
        return fireActions({ jobs, contacts, deals, automations });
      }
      return { results: 0, error: false };
    })
    .catch((error) => {
      console.error(error);
      return null;
    });
};

module.exports = {
  evaluateTriggers,
  evaluateAutomationTriggers,
  fetchSegmentationsContactIds,
  fetchContactIdsFromTriggers,
  fetchContactsFromTriggers,
  fetchListenedPosts,
  automationCron,
  getOthersLeads,
  doActions,
};
