/* eslint no-unused-vars: "off" */


/**
 * Copyright 2016 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
// eslint-disable-next-line no-unused-expressions
'use strict';

const serviceAccountPath = 'C:\\wamp\\www\\firebase\\';
const firebaseLocalPath = 'C:\\wamp\\www\\www\\qiplus-firebase\\';

// [START imports]
const fs = require("fs");
const admin = require('firebase-admin');
const firebase = require('firebase');
const functions = require('firebase-functions');
const https = require('https');

// const info = functions.config().info;
const rules = fs.readFileSync(`${firebaseLocalPath}firestore.rules`, "utf8");
const firebasePort = require(`${firebaseLocalPath}firebase.json`).emulators.firestore.port;
const port = firebasePort ? firebasePort : 8080;

let webmasterId, webmasterAcc, workRef, workAdmin, workStorage, workBucket, workAppId;

const nodemailer = require('nodemailer');
const util = require('util');
const inspect = util.inspect;
const simpleParser = require('mailparser').simpleParser;

const { FieldPath } = require('@google-cloud/firestore');

const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')
const argv = yargs(hideBin(process.argv)).argv

let env, envOption, accountId;

function log(obj, showHidden, depth, color) {
	if (typeof obj==='string') {
		let o=[]; Object.keys(arguments).forEach((k,i)=>i>0&&(o.push(arguments[k])))
		obj = { msg: obj, data: o }
		showHidden=false;
		depth=8;
		color=true;
	}
	console.warn(inspect(obj, showHidden===true, depth||8, color!==false ));
};
console.log = function () {
	if (arguments.length > 1) {
		log(...arguments);
		return;
	}
	console.warn(...arguments);
}
const exit = () => process.exit();

switch (argv.env) {
	case 'p': case 'prod': case 'production':
		env = 'production'
	break;	
	case 'e': case 'em': case 'emulator':
		env = 'emulator'
	break;	
	case 'd': case 'dev': case 'development':
		env = 'development'
	break;	
	case undefined:
	default:
		console.log('\n\n\n');		
		log(" ---------------- NO ENV DEFINED !!!!! ------------------ ");
		console.log('\n\n\n');
		exit()
	return
}

// -----------------------------
// env -------------------------
// -----------------------------
process.env.NODE_TLS_REJECT_UNAUTHORIZED = 0;
process.env.NODE_ENV = env;
// -----------------------------

const helpers = require(`../../functions/helpers`);
const CONSTANTS = require(`../../functions/constants`);
const ROLES = require(`../../functions/constants/roles`)
const COLLECTIONS = require(`../../functions/constants/collections`)

const { KEYWORDS_SUBCOLLECTIONS_MAP, KEYWORDS_SUBCOLLECTION_NAME, KEYWORDS_FIELDS_MAP, COLLECTION_TRIGGERS } = COLLECTIONS;

const {
	APP_TRIGGERS,
	DEFAULT_LOCALE,
	MOMENT_ISO,
	MOMENT_SHORT,
} = CONSTANTS;

const { jsonClone, isset, generateLongTextKeywords, generateKeywords, utf8BytesSize, isLoopable, generateKeywordsDocs, isLoopableObject, getDeepValue, momentNow, uniqueTimestamp, areEqualObjects } = helpers;

const {
	pagarmeClient,
	getTransaction,
	getPlan,
	createPlan,
	createSubscription,
	createTransaction,
	getSubscription,
	cancelSubscription,
	getSubscriptionTransactions,
	updateSubscription,
} = require('./pagarme');

const {
	DEFAULT_MAILING_DOMAIN,
	MAILGUN_PUB_KEY,
	MAILGUN_API_KEY,
	EMAIL_APP_DOMAIN, 
	DEFAULT_FROM_EMAIL, 
	STORAGE_ENDPOINT, 
	MESSAGES_ENDPOINT, 
	emailApi, 
	emailApp, 
	mailingApp, 
	mailingApi, 
	getRoutes, 
	forwardMailbox, 
	getDomainMailboxes, 
	deleteDomainMailbox, 
	createDomainMailbox, 
	updateMailboxPass, 
	getEmailLogs, 
	insertEmailLogs,
	validateMailbox
} = require('../../functions/mailgun');

const $lojasFCK = {
	"131": "79033", // Loja Conveniada
	"3823": "79033", // QI Plus
	"44": "7077", // Posto Amigão
	"3821": "7077", // Grupo FCK
	"46": "7077", // To da HOra
	"42": "7077", // FCK Auto Center
}

const $defaultOwners = {
	"4178": "45146", // WakeUP - Apice Desenvolvimento
	// "11773": "94418", // Fabiane -  Fabiane
	"7955": "80712", // Felipi Adauto -  Felipi Adauto 
	"3387": "7013", // Marcelo Marrom - Marcelo Marrom
	"2055": "107", // Ultrapassando Limites - Rodrigo Cardozo
	"5431": "52970", // Co_Letando - ex: Saulo Ricci 
	"3274": "58449", // World Ventures - ex: Larissa
	"3841": "10848", // Romanni - Romanni

	// 70634 - Alyson
	"9647": "70634", // IDEAH - Alyson
	"6038": "70634", // MAMA - Alyson

	// 50404 - Pedro Gadelha
	"3586": "50404", // MBI - P Gadelha
	// "4247": "50404", // clock - P Gadelha
	// "4065": "50404", // AirBitClub - P Gadelha

	// 79033 - Conveniado QI Plus
	"3823": "79033", // QI Plus
	"131": "79033", // Loja Conveniada

	// 7077 - Fabião
	"44": "7077", // Posto Amigão
	"3821": "7077", // Grupo FCK
	"46": "7077", // To da HOra
	"42": "7077", // FCK Auto Center

	// 7036 - Klebão
}

/* -------------------------------------  */
/* DEV INITIALIZATION */
/* -------------------------------------  */
const FB_APP_ID = 'br-com-qiplus';
const serviceAccount = require(`${serviceAccountPath}${FB_APP_ID}-53acb267292e.json`);

// console.log('serviceAccount',serviceAccount);
// console.log('process.env',process.env);

// Initialize Firebase 
const config = {
	credential: admin.credential.cert(serviceAccount),
	apiKey: 'AIzaSyAKrHqfH_gV3gOqYQx9eZm9v3YqDggLW9o', // Your Api key will be here
	authDomain: `${FB_APP_ID}.firebaseapp.com`, // Your auth domain
	databaseURL: `https://${FB_APP_ID}.firebaseio.com`, // query base url
	projectId: `${FB_APP_ID}`, // project id
	storageBucket: `${FB_APP_ID}.appspot.com`, // storage bucket
	messagingSenderId: "************" // messaging sender id
};

const DevelopmentFirebase = firebase.initializeApp(config);
const DevelopmentAdmin = admin.initializeApp(config);

const DevelopmentAdminAuth = DevelopmentAdmin.auth();
const FirebaseAuth = firebase.auth();
const DevelopmentRef = firebase.firestore();
const DevFunctions = firebase.functions();
const DevelopmentStorage = admin.storage();
const DevelopmentBucket = DevelopmentStorage.bucket()

/* -------------------------------------  */
// [END initialize]
/* -------------------------------------  */


/* -------------------------------------  */
/* EMULATOR INITIALIZATION */
/* -------------------------------------  */
// Initialize Firebase 
const EmulatorFirebase = firebase.initializeApp(config, 'Emulator');
const EmulatorAdmin = admin.initializeApp(config, 'Emulator');

const EmulatorAdminAuth = EmulatorAdmin.auth();
const EmulatorAuth = EmulatorFirebase.auth();
const EmulatorRef = EmulatorFirebase.firestore();

EmulatorRef.settings({
	host: "localhost:8080",
	ssl: false
});

/* -------------------------------------  */
// [END initialize]
/* -------------------------------------  */



/* -------------------------------------  */
/* PRODUCTION INITIALIZATION */
/* -------------------------------------  */
const QIPLUS_APP_ID = 'qiplus-50eee';
const QIPLUSserviceAccount = require(`${serviceAccountPath}${QIPLUS_APP_ID}-750bd5ceeb82.json`);

// Initialize Firebase 
const QIPLUSconfig = {
	credential: admin.credential.cert(QIPLUSserviceAccount),
	apiKey: "AIzaSyCcnEC41-poq-Amu9HSTwA_Is-UPngxXEI", // Your Api key will be here
	authDomain: `${QIPLUS_APP_ID}.firebaseapp.com`, // Your auth domain
	databaseURL: `https://${QIPLUS_APP_ID}.firebaseio.com`, // query base url
	projectId: `${QIPLUS_APP_ID}`, // project id
	storageBucket: `${QIPLUS_APP_ID}.appspot.com`, // storage bucket
	messagingSenderId: "************", // messaging sender id
	appId: "1:************:web:6ebfc0c6a2da6b9011e2a5",
	measurementId: "G-M0YX8B7N7K"   
};

const ProductionFirebase = firebase.initializeApp(QIPLUSconfig, 'Production');
const ProductionAdmin = admin.initializeApp(QIPLUSconfig, 'Production');

const ProductionAdminAuth = ProductionAdmin.auth();
const ProductionAuth = ProductionFirebase.auth();
const ProductionRef = ProductionFirebase.firestore();
const ProductionFunctions = ProductionFirebase.functions();
const ProductionStorage = ProductionAdmin.storage()
const ProductionBucket = ProductionStorage.bucket()

/* -------------------------------------  */
// [END initialize]
/* -------------------------------------  */

const moment = require('moment');
const axios = require('axios');
const qs = require('qs');

// -------------------------	
const envOptions = {
	development: {
		APP_ID: 'br-com-qiplus',
		webmasterId: '1',
		webmasterAcc: 'i9TtRqSINnaVr0f7x4uDBtk6SmY2',
		ref: DevelopmentRef,
		admin: DevelopmentAdmin, 
		storage: DevelopmentStorage, 
		bucket: DevelopmentBucket, 
		appId: FB_APP_ID, 
	},
	production: {
		APP_ID: 'qiplus-50eee',
		webmasterId: '1',
		webmasterAcc: 'WYtM0UIKMXYVD6woul4zVMg2sr13',
		ref: ProductionRef,
		admin: ProductionAdmin,
		storage: ProductionStorage,
		bucket: ProductionBucket,
		appId: QIPLUS_APP_ID,
	},
	emulator: {
		webmasterId: '1',
		webmasterAcc: 'i9TtRqSINnaVr0f7x4uDBtk6SmY2',
		ref: EmulatorRef,
		admin: EmulatorAdmin,
	}
}

let FirestoreRef = DevelopmentRef;

switch (env) {
	case 'production':
		FirestoreRef = ProductionRef;
	break;	
	case 'emulator':
		FirestoreRef = EmulatorRef;
	break;	
	case 'development':
	default:
		FirestoreRef = DevelopmentRef;
	break;	
}

const { replaceUndefined } = helpers;

// const { promises } = require('dns');
// [END imports]

let locale = DEFAULT_LOCALE;
moment.locale(locale);

const $GLOBALS = {};

const ownerRole = "owner";
const ownerLevel = ROLES.QIPLUS_ROLES_LEVELS[ownerRole];


const adminId = 'i9TtRqSINnaVr0f7x4uDBtk6SmY2';
const adminMail = '<EMAIL>';
const AdminAuth = admin.auth();

DevelopmentAdminAuth.createCustomToken(adminId)
	.then(customToken => {
		// Send token back to client
		// console.log('Firebase > customToken', customToken);
		FirebaseAuth.signInWithCustomToken(customToken)
			.then(async result => {
				// Send token back to client
				// console.log('result', result);

				console.log(' ');
				console.log(' ***  -------------------------------------------------------  ');
				console.log(`Dev started @ ${moment().format(CONSTANTS.MOMENT_LOCAL)} `);
				console.log(' ***  -------------------------------------------------------  ');
				console.log(' ');

				setTimeout(firebaseActions, 500 );

			})
			.catch(error => {
				console.log('FirebaseAuth > SignIn Error:', error);
			});
	})
	.catch(error => {
		console.log('Error creating custom token:', error);
	});
	

const QIPLUSadminId = 'WYtM0UIKMXYVD6woul4zVMg2sr13';
ProductionAdminAuth.createCustomToken(QIPLUSadminId)
	.then(customToken => {
		// Send token back to client
		// console.log('QIPLUS > customToken', customToken);
		ProductionAuth.signInWithCustomToken(customToken)
			.then(result => {
				// Send token back to client
				// console.log('result', result);
				console.log(' ');
				console.log(' ***  -------------------------------------------------------  ');
				console.log(`Production started @ ${moment().format(CONSTANTS.MOMENT_LOCAL)} `);
				console.log(' ***  -------------------------------------------------------  ');
				console.log(' ');
			})
			.catch(error => {
				console.log('ProductionAuth > SignIn Error:', error);
			});
	})
	.catch(error => {
		console.log('Error creating custom token:', error);
	});	


EmulatorAdminAuth.createCustomToken(adminId)
	.then(customToken => {
		// Send token back to client
		// console.log('Firebase > customToken', customToken);
		EmulatorAuth.signInWithCustomToken(customToken)
			.then(result => {
				// Send token back to client
				// console.log('result', result);

				console.log(' ');
				console.log(' ***  -------------------------------------------------------  ');
				console.log(`Emulator started @ ${moment().format(CONSTANTS.MOMENT_LOCAL)} `);
				console.log(' ***  -------------------------------------------------------  ');
				console.log(' ');

			})
			.catch(error => {
				console.log('EmulatorAuth > SignIn Error:', error);
			});
	})
	.catch(error => {
		console.log('Error creating custom token:', error);
	});
	
const buildPages = (query, callbackFn, workRef) => {

	const results = {
		query,
		fetchedPages: {},
		startTime: moment().format(CONSTANTS.MOMENT_LOCAL)
	};
	const pages = [];
	let children = [];
	let lastKey = '';

	if (!query || !query.db) {
		// throw new functions.https.HttpsError('failed-precondition', 'The function needs arguments.');
	}

    /* 
    // Checking that the user is authenticated.
    if (!context.auth) {
        // Throwing an HttpsError so that the client gets the error details.
        throw new functions.https.HttpsError('failed-precondition', 'The function must be called ' +
        'while authenticated.');
    }
    */

	const db = query.db;
	const page = query.page;
	const order = query.order === 'desc' ? 'desc' : 'asc';
	const orderKey = query.orderKey;
	const idKey = query.idKey || 'ID';
	const startKey = query.startKey;
	const endKey = query.endKey;
	const equalKey = query.equalKey;
	const equalVal = query.equalVal;
	const filterKeys = query.filterKeys && typeof query.filterKeys === 'object' && Object.keys(query.filterKeys).length ? query.filterKeys : false;
	const filterMode = query.filterMode || 'and';
	const perpage = (query.perpage && parseInt(query.perpage, 10)) || 50;
	const where = Array.isArray(query.where) && query.where.length ? query.where : false;
	const limit = query.limit;
	const fetchPages = (
		Array.isArray(query.fetchPages) ?
			query.fetchPages : (!isNaN(query.fetchPages) ? [query.fetchPages] : false)
	);

	const dbRef = workRef.collection(db)

	let dbRefQuery = dbRef;

	Array.isArray(where) && where.forEach(queryArgs => (
		dbRefQuery = dbRef.where(queryArgs[0], queryArgs[1], queryArgs[2])
	))

	if (equalKey && equalVal) {
		dbRefQuery = dbRefQuery.where(equalKey, '==', equalVal)
	}

	if (idKey && startKey) {
		dbRefQuery = dbRefQuery.where(idKey, '>', startKey)
	}

	if (idKey && endKey) {
		dbRefQuery = dbRefQuery.where(idKey, '<=', endKey)
	}

	if (orderKey) {
		dbRefQuery = dbRefQuery.orderBy(orderKey, order)
	}

	if (limit) {
		dbRefQuery = dbRefQuery.limit(Math.min(limit, CONSTANTS.FIRESTORE_MAX_LIMIT))
	}

	dbRefQuery.get().then(snapshot => {

		results.numChildren = snapshot.size;

		const dbChildren = [];
		let childrenKeys = [];

		snapshot.forEach(doc => {

			const dbElem = doc.data();
			const dbKey = dbElem.ID || dbElem.id || doc.id;

			lastKey = dbKey;

			let isMatch = true;
			if (filterKeys) {
				const matches = {};
				Object.keys(filterKeys).forEach(filterKey => {
					const filterVal = filterKeys[filterKey];
					const keyValue = (filterKey in dbElem) ? dbElem[filterKey] : 'skipKey';
					if (keyValue !== 'skipKey') {
						const keyMatch = keyValue && (typeof keyValue === 'object') && Object.keys(keyValue).length ?
							Object.keys(keyValue).find(k => helpers.matchIfNumber(keyValue[k], filterVal)) :
							(
								Array.isArray(keyValue) ?
									keyValue.find(v => helpers.matchIfNumber(v, filterVal)) :
									helpers.matchIfNumber(keyValue, filterVal)
							)
						matches[filterKey] = keyMatch;
					} else {
						matches[filterKey] = false;
					}
				})
				isMatch = (filterMode === 'or') ?
					Object.keys(filterKeys).find(f => matches[f]) :
					Object.keys(filterKeys).filter(f => matches[f]).length === Object.keys(filterKeys).length;
			}
			if (isMatch) {
				children.push(dbElem);
			}

			dbChildren.push(dbElem);

		})


		if (orderKey && children.length) {
			children.sort((a, b) => {
				return a[orderKey] && b[orderKey] ?
					(
						a[orderKey] > b[orderKey] ? (order !== 'asc' ? -1 : 1) : (order !== 'asc' ? 1 : -1)
					)
					: 0
			})
		}

		if (limit && filterKeys && children.length) {
			children = children.filter((dbKey, count) => count < limit)
		}

		childrenKeys = children.map(c => c.ID || c.id)

		// pagination
		let page = [];

		childrenKeys.forEach((dbKey, count, arr) => {
			page.push(dbKey)
			if (page.length === perpage || count === arr.length - 1) {
				pages.push(page)
				page = [];
			}
		})

		fetchPages && (fetchPages.filter(p => (p < 0 ? pages[pages.length + p] : pages[p - 1])).forEach(p => {
			const arrayIndex = p < 0 ? pages.length + p : p - 1;
			const pageNum = arrayIndex + 1;
			results.fetchedPages[pageNum] = pages[arrayIndex].map(dbKey => children.find(c => (c.ID || c.id) === dbKey));
		}));

		results.lastKey = lastKey;
		results.numResults = children.length;
		results.numKeys = childrenKeys.length;
		results.numPages = pages.length;
		results.pages = pages;
		results.endTime = moment().format(CONSTANTS.MOMENT_LOCAL)

		// console.log('results', results);

		typeof callbackFn === "function" && callbackFn(results)

		return dbChildren;
	}).catch((error) => {
		console.error('error', error);
	})

}


const emailModel = {
    smtp: "qiplus",
    smtp_integration: "",
    cc: "",
    cco: "",
    bcc: "",
    fromName: "",
    subject: "",
    other_recipients: "",
    scheduled_date: "",
    contacts: [],
    segmentations: [],
    context: {},
};

const removeEventParticipants = (eventId, callbackFn) => {

	const participantsCol = FirestoreRef.collection('events').doc(eventId).collection('participants')
	
	// console.log('-----------------------');
	// console.log('eventId',eventId);
	// console.log('removeIds',removeIds.length);
	
	participantsCol.get().then(partSnapshot=>{
		
		const promises = [];
		
		partSnapshot.forEach(doc=>{
			const ID = doc.data().ID
			if ( ID==='owner'||ID==='id'||ID==='ID' ) {
				promises.push(participantsCol.doc(ID).delete())
			}
		})
		
		console.log('- partSnapshot -', {
			'eventId': eventId,
			'promises': promises.length,
			'partSnapshot.size': partSnapshot.size,
		});		
		console.log(' ');

		if (promises.length) {
		
			Promise.all(promises).then(results=>{

				console.log('--------- Promise.all --------------');
				console.log('eventId',eventId);
				console.log('results',results.length);
				console.log('------------------------------------');		
				
				callbackFn()
			})
			
		}else{

			callbackFn()

		}
		
	})

};

const updateEventParticipants = (eventId, participantsData, callbackFn) => {

	const participantIds = Object.keys(participantsData).filter(pId=>pId!=='owner'&&pId!=='id'&&pId!=='ID')
	const participantsCol = FirestoreRef.collection('events').doc(eventId).collection('participants')

	// console.log('-----------------------');
	// console.log('eventId',eventId);
	// console.log('participantIds',participantIds.length);
	
	participantsCol.get().then(partSnapshot=>{

		const promises = [];
		const participants = []
		
		partSnapshot.forEach(doc=>{
			participants.push(doc.data())
		})
		
		participantIds.forEach((cId,i)=>{
			if (!participants.find(p=>p.ID===cId)) {
				
				const participant = {
					ID: cId,
					collection: COLLECTIONS.LEADS_COLLECTION_NAME,
					confirmed: participantsData[cId],
					checkin: false,
					checkinTime: "",
					logs: moment().format(CONSTANTS.MOMENT_ISO),
					modified: moment().format(CONSTANTS.MOMENT_ISO),
					context: {},
				}
				
				promises.push(participantsCol.doc(cId).set(participant))

			}
		})
		
		console.log('- partSnapshot -', {
			'eventId': eventId,
			'promises': promises.length,
			'partSnapshot.size': partSnapshot.size,
			'participants.length': participants.length,
			'participantIds': participantIds.length,		
		});
		console.log(' ');

		if (promises.length) {
		
			Promise.all(promises).then(results=>{

				console.log('--------- Promise.all --------------');
				console.log('eventId',eventId);
				console.log('results',results.length);
				console.log('-----------------------');		
				
				callbackFn()
			})
			
		}else{

			callbackFn()

		}
		
	})
};

const getAuthUser = async (data,log) => {	
	try {
		const userRecord = await AdminAuth.getUserByEmail(data.email);
		// See the UserRecord reference doc for the contents of userRecord.
		// console.log('Successfully fetched user data:', userRecord.toJSON());
		return userRecord;
	}
	catch (error) {
		if ( log ) {
			console.log('Error fetching user data:', error);
		}
		return null;
	}
}

const cloneToDevelopment = (srcRef, collections, srcAdmin) => cloneToRef(srcRef, DevelopmentRef, collections, srcAdmin)
const cloneToProduction = (srcRef, collections, srcAdmin) => cloneToRef(srcRef, ProductionRef, collections, srcAdmin)
const cloneToEmulator = (srcRef, collections, srcAdmin) => cloneToRef(srcRef, EmulatorRef, collections, srcAdmin)

const cloneToRef = (srcRef, tgtRef, collections, srcAdmin) => {
	collections.forEach(({ collection, where })=>{
		var collectionRef = srcRef.collection(collection)
		if ( where ) where.forEach(w=>collectionRef=collectionRef.where(w[0],w[1],w[2]))
		collectionRef.get().then((snapshot)=> {
			console.log(`collectionRef > ${collection} > size`,snapshot.size);
			
			const batch = tgtRef.batch()
			const collectionRef = tgtRef.collection(collection)
			
			snapshot.forEach(doc=>{
				let docId = doc.id;
				let docRef = collectionRef.doc(docId)
				batch.set(docRef, doc.data())
			})
			
			batch.commit()
			.then(r=>{
				console.log('commit ok');

				if (!srcAdmin) {
					console.log('no source admin informed - will not clone subcollections');
					return;
				}

				snapshot.forEach(async doc=>{
					let docId = doc.id;
					let docRef = tgtRef.collection(collection).doc(docId)
					
					const docPath = `${collection}/${docId}`;
					const subcollections = await srcAdmin.firestore().doc(docPath).listCollections();
	
					if (Array.isArray(subcollections)) {
						const subcollectionIds = subcollections.map(col=>col.id);
						subcollectionIds.forEach(sCol=>{

							collectionRef.doc(docId).collection(sCol).get()
							.then(s=>{
								const sBatch = tgtRef.batch();
								s.forEach(sDoc=>{
									let sDocRef = docRef.collection(sCol).doc(sDoc.id);
									sBatch.set(sDocRef, sDoc.data())
								})
								sBatch.commit().then(r=>{
									console.log('sBatch ok:',sCol);
								})
							})
							
						})
						console.log('subcollectionIds',subcollectionIds);
					}
				})			

				tgtRef.collection(collection).get().then((snapshot)=> {
					console.log(`tgtRef > ${collection} > size`,snapshot.size);
				}).catch((err)=> {
					console.error(err);
				});				
			})
			
		}).catch((err)=> {
			// This will be an "population is too big" error.
			console.error(err);
		});			
	})
}

// grabHereToCopyPaste
const fetchSubCollection = (collection, docId, subcollection, where, limit, orderBy, order) => {
	return new Promise((res,rej)=>{
		let query = FirestoreRef.collection(collection).doc(docId).collection(subcollection)
		if (where) {
			where.forEach(w=>{ query = query.where(w[0],w[1],w[2]) })
		}
		if (orderBy) {
			query = query.orderBy(orderBy, order||'desc');
		}
		if (limit && !isNaN(limit)) query = query.limit(limit);
		query.get().then(snapshot=>{
			const posts = [];
			snapshot.forEach(doc=>posts.push(doc.data()))
			return res(posts)
		}).catch(error=>{
			console.error(error);
			return res([])
		})
	})
}

// grabHereToCopyPaste
const fetchCollection = (collection, where, limit, orderBy, order) => {
	return new Promise((res,rej)=>{
		let query = FirestoreRef.collection(collection)
		if (where) {
			where.forEach(w=>{ query = query.where(w[0],w[1],w[2]) })
		}
		if (orderBy) {
			query = query.orderBy(orderBy, order||'asc');
		}
		if (limit && !isNaN(limit)) query = query.limit(limit);
		query.get().then(snapshot=>{
			const posts = [];
			snapshot.forEach(doc=>{
				const post = doc.data();
				posts.push(post)	
			})
			return res(posts)
		}).catch(error=>{
			console.error(error);
			return res([])
		})
	})
}

// grabHereToCopyPaste
const fetchDoc = (path, createUnexistant) => {
	return new Promise(async (res,rej)=>{

		let pathArr = Array.isArray(path) ? path : path.split('/');
		path = pathArr.join('/');
		
		if ( pathArr.length%2!==0 ) {
			if ( createUnexistant ) {
				return res(await FirestoreRef.collection(`${path}`).doc().get());
			}
			return res(null);
		}

		return FirestoreRef.doc(`${path}`).get()
		.then(doc=>{
			if (doc.exists) return res(doc);
			return res(null);
		})
		.catch(error=>{
			console.error(error);
			return rej(error);
		})
	})
}

// grabHereToCopyPaste
const updatePost = (collection, updatedPost, ID) => {
	return new Promise((res,rej)=>{
		updatedPost = replaceUndefined(updatedPost)
		const postId = ID||updatedPost.ID;
		FirestoreRef.collection(collection).doc(`${postId}`).update(updatedPost)
		.then(result=>{
			return res(updatedPost)		
		})
		.catch(error=>{
			console.error(error);
			return rej(error)
		})
	})
}


// grabHereToCopyPaste
const fetchPost = (collection, postId, noCache) => {
	return new Promise((res,rej)=>{
		return FirestoreRef.collection(collection).doc(postId).get()
		.then(doc=>{
			if (doc.exists) {
				const post = doc.data();
				return res(post||{})
			}
			const err = `ERROR_FETCHING_${collection}_${postId}`;
			return rej(err)
		})
		.catch(error=>{
			console.error(error);
			return rej(error)
		})
	})
}

// grabHereToCopyPaste
const addNewPost = (collection, newPost) => {
	return new Promise((res,rej)=>{
		newPost = replaceUndefined(newPost)
		FirestoreRef.collection(collection).add(newPost)
		.then(result=>{
			if (result.id) {
				const newPostId = result.id;
				const post = {
					...newPost,
					ID: newPostId,
				}
				return res(post)
			}
			const err = 'ERROR_ADDING_NEW_POST';
			return rej(err)
		})
		.catch(error=>{
			console.error(error);
			return rej(error)
		})
	})
}

// grabHereToCopyPaste
const getAccountLeadsIds = async (accountId, fromCache) => {
	
	let accountLeadsRef = await FirestoreRef.collectionGroup(COLLECTIONS.KEYWORDS_SUBCOLLECTIONS_MAP[COLLECTIONS.LEADS_COLLECTION_NAME])
	.where("field","==","accountId")
	.where("accountId","==",accountId)
	.where("keywords","array-contains",accountId)
	.orderBy("updatedAt","desc")
	.get()

	const accountLeadsDocs = accountLeadsRef.docs.map(d=>d.data())
	const accountLeadsIds = accountLeadsDocs.map(kDoc=>kDoc.docId)

	return accountLeadsIds
}

let tests = [
/* 
cloneToEmulator(workRef, workAdmin,[
	// { collection: COLLECTIONS.QIUSERS_COLLECTION_NAME },
	{ collection: COLLECTIONS.QIUSERS_COLLECTION_NAME, where: [["ID","==","1"]] },
	{ collection: COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME },
	{ collection: COLLECTIONS.ACCOUNTS_COLLECTION_NAME },
	{ collection: COLLECTIONS.FORMS_COLLECTION_NAME, where: [["ID","==","8583"]] },
	{ collection: COLLECTIONS.AUTOMATIONS_COLLECTION_NAME, where: [["ID","==","UiWXHt3gzdw7E6WvqRvq"]] },
	{ collection: COLLECTIONS.FUNNELS_COLLECTION_NAME, where: [["owner","==","hqLNhza3ATVfYWKumnHNVFhZNK63"]] },
	{ collection: COLLECTIONS.MAILING_COLLECTION_NAME, where: [["accountId","==","hqLNhza3ATVfYWKumnHNVFhZNK63"]] },
	{ collection: COLLECTIONS.LEADS_COLLECTION_NAME, where: [["accountId","==","hqLNhza3ATVfYWKumnHNVFhZNK63"]] },
	{ collection: COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME, where: [["ID","==","3PyGTxLClDw9DhDhSZlP"]] },
])
cloneToEmulator(workRef, workAdmin, [
	{ collection: COLLECTIONS.QIUSERS_COLLECTION_NAME, where: [["ID","==","1"]] },
	{ collection: COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME },
	{ collection: COLLECTIONS.ACCOUNTS_COLLECTION_NAME },
	{ collection: COLLECTIONS.FORMS_COLLECTION_NAME, where: [["ID","==","8583"]] },
	{ collection: COLLECTIONS.FUNNELS_COLLECTION_NAME, where: [["owner","==",webmasterAcc]] },
	{ collection: COLLECTIONS.MAILING_COLLECTION_NAME, where: [["accountId","==",webmasterAcc]] },
	{ collection: COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME, },
	{ collection: COLLECTIONS.CAMPAIGNS_COLLECTION_NAME, },	
	{ collection: COLLECTIONS.LEADS_COLLECTION_NAME, where: [["accountId","==",webmasterAcc]] },
])

*/
	// forwardMailbox('support', '<EMAIL>', results=>{
	// 	console.log('results',results)
	// 	emailApi.get('/routes', function (error, body) {
	// 		console.log('routes',body);
	// 	});
	// });

	/* 
	var msg = {
		from: 'Marcelo <<EMAIL>>',
		to: '<EMAIL>',
		subject: 'Hello from QIPLUs mail',
		html: '<p><b>Testing</b> some Mailgun awesomness!</p>'
	};

	emailApp.send(msg, (error, body)=> {
		console.log('emailApp > result > error', error);
		console.log('emailApp > result > body', body);
	});

	var msg = {
		from: 'Marcelo <<EMAIL>>',
		to: '<EMAIL>',
		subject: 'Hello from QIPLUs mailing',
		html: '<p><b>Testing</b> some Mailgun awesomness!</p>'
	};

	mailingApp.send(msg, (error, body)=> {
		console.log('mailingApp > result > error', error);
		console.log('mailingApp > result > body', body);
	});
*/
]

let webmasterAccountBkp = {
    "id": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
    "ID": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
    "title": "Webmaster - Plano Córtex",
    "slug": "",
    "content": "",
    "thumbnail": "https://qiplus.com.br/wp-content/uploads/IMG_20150927_154142104.jpg",
    "date": "2020-05-08 02:09:22",
    "modified": "2020-10-13 16:45:45",
    "collection": "accounts",
    "post_type": "account",
    "type": "individual",
    "config_url": "",
    "edit_url": "",
    "config": {
        "automations_included": 0,
        "billing": true,
        "campaigns_included": 0,
        "custom_plan": true,
        "email_option": "rel",
        "emails_included": 0,
        "emails_towards_base": 0,
        "events_included": 10,
        "forms_included": 0,
        "funnels_included": 0,
        "landing-pages_included": 0,
        "mailboxes_included": 0,
        "payment_method": "",
        "plan_type": "marketing",
        "qiusers_included": 20,
        "smtp_enabled": true,
        "trial_days": 0
    },
    "billing_data": {
        "address": {
            "city": "São Paulo",
            "complementary": "Apto 12",
            "country": "BR",
            "neighborhood": "Consolação",
            "state": "SP",
            "street": "Rua Bela Cintra",
            "street_number": "71",
            "zipcode": "********"
        },
        "customer": {
            "document_number": "***********",
            "document_type": "cpf",
            "email": "<EMAIL>",
            "external_id": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
            "name": "Webmaster QIPlus",
            "type": "individual"
        },
        "phone": {
            "ddd": 11,
            "ddi": 55,
            "id": 589589,
            "number": "68026916",
            "object": "phone"
        }
    },
    "values": {
        "extra_automations_monthly": 69,
        "extra_automations_yearly": 49,
        "extra_campaigns_monthly": 69,
        "extra_campaigns_yearly": 49,
        "extra_event": "100.00",
        "extra_forms_monthly": 69,
        "extra_forms_yearly": 49,
        "extra_funnels_monthly": 69,
        "extra_funnels_yearly": 49,
        "extra_landing-pages_monthly": 69,
        "extra_landing-pages_yearly": 49,
        "extra_mailboxes_monthly": 69,
        "extra_mailboxes_yearly": 49,
        "extra_qiusers_monthly": 69,
        "extra_qiusers_yearly": 49,
        "extra_user_monthly": 69,
        "extra_user_yearly": "49.00",
        "implementation": 1800
    },
    "data": {
        "contacts": "10000",
        "contacts_max": "10000",
        "contacts_min": 5001,
        "monthly_value": "765.90",
        "price": "18250.80",
        "yearly_value": "589.90"
    },
    "levels": {
        "automations": 6,
        "barcode_products": 6,
        "campaigns": 5,
        "events": 5,
        "forms": 6,
        "funnels": 6,
        "landing-pages": 5,
        "live_qiplus": 6,
        "mailing": 6,
        "questionnaires": 6,
        "raffles": 5,
        "tickets": 6,
        "trackings": 5
    },
    "modules": {
        "automations": true,
        "barcode_products": true,
        "campaigns": true,
        "contracts": true,
        "events": false,
        "forms": true,
        "funnels": true,
        "landing-pages": true,
        "live_qiplus": false,
        "mailboxes": true,
        "mailing": true,
        "prizes": 0,
        "qiusers": true,
        "questionnaires": true,
        "raffles": true,
        "tickets": true,
        "trackings": true
    },
    "active": true,
    "uid": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
    "planId": "11817",
    "plan": {
        "ID": "11817",
        "author": "7036",
        "collection": "qiplus-plans",
        "config": {
            "email_option": "rel",
            "emails_towards_base": "",
            "events_included": 10,
            "implementation_charge": true,
            "order": 4,
            "payment_methods": [
                "credit_card",
                "boleto"
            ],
            "plan_type": "marketing",
            "qiusers_included": 0,
            "total_emails": "",
            "users_included": 1
        },
        "config_url": "https://qiplus.com.br/wp-admin/post.php?post=11817&action=edit",
        "content": "",
        "data": {
            "full_description": "",
            "short_description": "TODOS OS RECURSOS DO QIPLUS"
        },
        "date": "2019-08-27 19:35:59",
        "edit_url": "https://qiplus.com.br/wp-admin/post.php?post=11817&action=edit",
        "id": "11817",
        "levels": {
            "automations": 6,
            "barcode_products": 6,
            "campaigns": 5,
            "events": 5,
            "forms": 6,
            "funnels": 6,
            "landing-pages": 5,
            "live_qiplus": 6,
            "mailing": 6,
            "questionnaires": 6,
            "raffles": 5,
            "tickets": 6,
            "trackings": 5
        },
        "modified": "2020-10-08 09:04:28",
        "modules": {
            "automations": true,
            "barcode_products": true,
            "campaigns": true,
            "contracts": true,
            "events": false,
            "forms": true,
            "funnels": true,
            "landing-pages": true,
            "live_qiplus": false,
            "mailboxes": true,
            "mailing": true,
            "qiusers": true,
            "questionnaires": true,
            "raffles": true,
            "tickets": true,
            "trackings": true
        },
        "owner": "1",
        "post_type": "planos-qiplus",
        "slug": "plano-cortex",
        "status": "publish",
        "stores": [
            "3823"
        ],
        "thumbnail": "",
        "title": "Plano Córtex",
        "url": "https://qiplus.com.br/?post_type=planos-qiplus&#038;p=11817",
        "values": {
            "extra_automations_monthly": 69,
            "extra_automations_yearly": 49,
            "extra_campaigns_monthly": 69,
            "extra_campaigns_yearly": 49,
            "extra_event": "100.00",
            "extra_forms_monthly": 69,
            "extra_forms_yearly": 49,
            "extra_funnels_monthly": 69,
            "extra_funnels_yearly": 49,
            "extra_landing-pages_monthly": 69,
            "extra_landing-pages_yearly": 49,
            "extra_mailboxes_monthly": 69,
            "extra_mailboxes_yearly": 49,
            "extra_qiusers_monthly": 69,
            "extra_qiusers_yearly": 49,
            "extra_user_monthly": 69,
            "extra_user_yearly": "49.00",
            "implementation": "1800.00"
        }
    },
    "parentId": "",
    "affiliateId": "",
    "pagarme": {
        "days": 365,
        "installments": 1,
        "payment_method": "credit_card",
        "plan_id": 509115,
        "recurrency": "yearly",
        "status": "paid",
        "subscription": {
            "address": null,
            "card": {
                "brand": "visa",
                "country": "BRAZIL",
                "date_created": "2020-10-07T22:02:12.798Z",
                "date_updated": "2020-10-07T22:02:13.255Z",
                "expiration_date": "1021",
                "fingerprint": "ck9wcui0o23ze0k09cy0egeqy",
                "first_digits": "498453",
                "holder_name": "MARCELO V A LIMA",
                "id": "card_ckfzxtewt076n0g9tgf22htxe",
                "last_digits": "1771",
                "object": "card",
                "valid": true
            },
            "card_brand": "visa",
            "card_last_digits": "1771",
            "charges": 0,
            "current_period_end": "2021-10-07T22:31:47.681Z",
            "current_period_start": "2020-10-09T22:31:47.681Z",
            "current_transaction": {
                "acquirer_id": "5d265e71509bab131c57e1fd",
                "acquirer_name": "pagarme",
                "acquirer_response_code": "0000",
                "addition": null,
                "amount": 1825080,
                "antifraud_metadata": {},
                "antifraud_score": null,
                "authorization_code": "72743",
                "authorized_amount": 1825080,
                "boleto_barcode": null,
                "boleto_expiration_date": null,
                "boleto_url": null,
                "capture_method": "ecommerce",
                "card_brand": "visa",
                "card_first_digits": "498453",
                "card_holder_name": "MARCELO V A LIMA",
                "card_last_digits": "1771",
                "card_magstripe_fallback": false,
                "card_pin_mode": null,
                "cost": 120,
                "cvm_pin": false,
                "date_created": "2020-10-07T22:02:12.809Z",
                "date_updated": "2020-10-07T22:02:13.275Z",
                "device": null,
                "discount": null,
                "fraud_covered": false,
                "fraud_reimbursed": null,
                "id": 9971069,
                "installments": 1,
                "ip": "**************",
                "local_time": null,
                "local_transaction_id": null,
                "metadata": {
                    "uid": "i9TtRqSINnaVr0f7x4uDBtk6SmY2"
                },
                "nsu": 9971069,
                "object": "transaction",
                "order_id": null,
                "paid_amount": 1825080,
                "payment": null,
                "payment_method": "credit_card",
                "postback_url": null,
                "private_label": null,
                "receipt_url": null,
                "reference_key": null,
                "referer": "api_key",
                "refunded_amount": 0,
                "refuse_reason": null,
                "risk_level": "very_low",
                "soft_descriptor": null,
                "status": "paid",
                "status_reason": "acquirer",
                "subscription_id": 526334,
                "tid": 9971069
            },
            "customer": {
                "birthday": null,
                "born_at": null,
                "country": null,
                "date_created": "2020-10-07T22:02:12.769Z",
                "document_number": "***********",
                "document_type": "cpf",
                "documents": [],
                "email": "<EMAIL>",
                "external_id": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
                "gender": null,
                "id": 3864187,
                "name": "Webmaster QIPlus",
                "object": "customer",
                "phone_numbers": null,
                "type": "individual"
            },
            "date_created": "2020-10-07T22:02:13.262Z",
            "date_updated": "2020-10-09T22:31:47.682Z",
            "fine": {},
            "id": 526334,
            "interest": {},
            "manage_token": "test_subscription_8Fe7iV20gID6Or9BOBf8GszxZAopfn",
            "manage_url": "https://pagar.me/customers/#/subscriptions/526334?token=test_subscription_8Fe7iV20gID6Or9BOBf8GszxZAopfn",
            "metadata": {
                "uid": "i9TtRqSINnaVr0f7x4uDBtk6SmY2"
            },
            "object": "subscription",
            "payment_method": "credit_card",
            "phone": null,
            "plan": {
                "amount": 1825080,
                "charges": null,
                "color": null,
                "date_created": "2020-10-09T22:31:44.479Z",
                "days": 365,
                "id": 509115,
                "installments": 1,
                "invoice_reminder": null,
                "name": "Plano Córtex - 1",
                "object": "plan",
                "payment_deadline_charges_interval": 1,
                "payment_methods": [
                    "credit_card"
                ],
                "trial_days": 0
            },
            "postback_url": "https://qiplus.com.br/?hook=pagarme&subject=subscription&action=update&enviroinment=development",
            "settled_charges": null,
            "soft_descriptor": null,
            "status": "paid"
        },
        "subscription_id": 526334
    },
    "settings": {
        "goals": [
            {
                "type": "conversionsCount",
                "role": "",
                "value": "",
                "period": "monthly"
            }
        ],
        "investments": []
    },
    "owner": "1",
    "author": "1",
    "stores": [
        "3823"
    ],
    "status": "publish",
    "url": "https://qiplus.com.br/?post_type=planos-qiplus&#038;p=11817",
    "accountId": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
    "contactSlider": null,
    "createdAt": *************,
    "keywords": [],
    "locale": "pt_BR",
    "logs": {
        "updated": {
            "date": "2020-10-12 12:51:57",
            "operator_id": "1",
            "user": "1"
        }
    },
    "options": {
        "contacts_max": "125000",
        "contacts_min": 100001,
        "monthly_value": "3170.90",
        "yearly_value": "2439.90"
    },
    "plan_id": 482115,
    "subscription_id": 490209,
    "updatedAt": *************,
    "user_id": "1",
    "userdata": {
        "ID": "1",
        "address": {
            "city": "São Paulo",
            "comp": "Apto 12",
            "complementary": "Apto 12",
            "country": "Brasil",
            "neighborhood": "Consolação",
            "num": "71",
            "postalCode": "********",
            "state": "SP",
            "street": "Rua Bela Cintra",
            "street_number": "71",
            "zipcode": "********"
        },
        "author": "",
        "avatar": "https://qiplus.com.br/wp-content/uploads/IMG_20150927_154142104.jpg",
        "birthday": "21/03/1981",
        "city": "São Paulo",
        "cnpj": "",
        "collection": "qiusers",
        "companyName": "",
        "cpf": "***********",
        "dateRegistered": "2017-09-13 10:49:35",
        "description": "",
        "displayName": "Webmaster QIPlus",
        "email": "<EMAIL>",
        "event": "6087",
        "facebook": "facebook.com/marceloviana",
        "firstName": "Webmaster",
        "gender": "masc",
        "id": "1",
        "instagram": "instagram.com/marceviana",
        "keywords": [],
        "lastName": "QIPlus",
        "level": 1,
        "linkedin": "",
        "locale": "pt_BR",
        "manager": "",
        "maritalStatus": "Solteiro",
        "mobile": "(11) 68026-916",
        "modified": "2020-05-05 20:22:21",
        "modules": {
            "automations": true,
            "barcode_products": true,
            "campaigns": true,
            "emails": true,
            "events": true,
            "forms": true,
            "landing_pages": true,
            "live_qiplus": true,
            "questionnaires": true,
            "raffles": true,
            "tickets": true,
            "trackings": true
        },
        "nickname": "webmaster",
        "occupation": "Designer",
        "owner": "1",
        "phone": "(54) 1168-0269",
        "profile": "1",
        "promoter": "",
        "pts": 54325,
        "pts_gained": 0,
        "qrcode": "https://chart.googleapis.com/chart?cht=qr&chl=user_id%3D1%26user_login%3Dmarcelo.sdharma%40gmail.com%26first_name%3DWebmaster%26last_name%3DQIPlus&chs=300x300&chld=L|0",
        "qualification": "",
        "roles": [
            "webmaster",
            "admin"
        ],
        "score": 0,
        "score_gained": 0,
        "seller": "",
        "state": "SP",
        "stores": [
            "3823"
        ],
        "total_pts": "62595",
        "twitter": "twitter.com/marceloviana",
        "uid": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
        "url": "",
        "website": "",
        "wpuser": "1"
    }
}

let webmasterDev = {
	"ID": "1",
	"accountId": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
	"address": {
	  "city": "São Paulo",
	  "comp": "Apto 12",
	  "neighborhood": "Consolação",
	  "num": "71",
	  "postalCode": "01415-000",
	  "ref": "Mackenzie",
	  "state": "SP",
	  "street": "Rua Bela Cintra"
	},
	"author": "",
	"avatar": "https://qiplus.com.br/wp-content/uploads/IMG_20150927_154142104.jpg",
	"birthday": "21/03/1981",
	"city": "São Paulo",
	"cnpj": "",
	"collection": "qiusers",
	"companyName": "",
	"cpf": "***********",
	"date": "2017-09-13 10:49:35",
	"dateRegistered": "2017-09-13 10:49:35",
	"description": "",
	"displayName": "Webmaster QIPlus",
	"email": "<EMAIL>",
	"event": "6087",
	"facebook": "facebook.com/marceloviana",
	"firstName": "Webmaster",
	"gender": "masc",
	"id": "1",
	"instagram": "instagram.com/marceviana",
	"keywords": [],
	"lastName": "QIPlus",
	"level": 1,
	"linkedin": "",
	"locale": "pt_BR",
	"manager": "",
	"maritalStatus": "Solteiro",
	"mobile": "(11) 68026-916",
	"modified": "2020-05-11 00:52:56",
	"modules": {
	  "automations": true,
	  "barcode_products": true,
	  "campaigns": true,
	  "emails": true,
	  "events": true,
	  "forms": true,
	  "landing_pages": true,
	  "live_qiplus": true,
	  "questionnaires": true,
	  "raffles": true,
	  "tickets": true,
	  "trackings": true
	},
	"nickname": "webmaster",
	"occupation": "Designer",
	"owner": "1",
	"phone": "(54) 1168-0269",
	"profile": "1",
	"promoter": "",
	"pts": 54325,
	"pts_gained": 0,
	"qrcode": "https://chart.googleapis.com/chart?cht=qr&chl=user_id%3D1%26user_login%3Dmarcelo.sdharma%40gmail.com%26first_name%3DWebmaster%26last_name%3DQIPlus&chs=300x300&chld=L|0",
	"qualification": "",
	"roles": [
	  "webmaster",
	  "admin"
	],
	"score": 0,
	"score_gained": 0,
	"seller": "",
	"state": "SP",
	"status": "active",
	"stores": [
	  "3823"
	],
	"total_pts": "62595",
	"twitter": "twitter.com/marceloviana",
	"uid": "i9TtRqSINnaVr0f7x4uDBtk6SmY2",
	"url": "",
	"website": "",
	"wpuser": "1"
}

/* 

	
	// createDomainMailbox('info', 'Fck123@Amg!', results=>{
	// 	console.log('results',results)
	// 	emailApi.get('/routes', function (error, body) {
	// 		console.log('routes',body);
	// 		exit();
	// 	});
	// }, null, '<EMAIL>')
	

	// let transporter = nodemailer.createTransport({
	// 	host: 'smtp.mailtrap.io',
	// 	port: 2525,
	// 	auth: {
	// 	   user: '1c2e09e2593dd9',
	// 	   pass: 'e9fb67cfa1df4b'
	// 	}
	// });

	// let transporter = nodemailer.createTransport({
	// 	host: 'smtp.mailgun.org',
	// 	// port: 465,
	// 	// port: 587,
	// 	// secure: true, // use SSL
	// 	secure: false,
	// 	logger: true,
	// 	debug: true,
	// 	ignoreTLS: true, // add this 
	// 	auth: {
	// 	   user: '<EMAIL>',
	// 	   pass: 'qiplus5585'
	// 	}
	// });

	let transporter = nodemailer.createTransport({
		service: 'gmail',
		// host: 'smtp.gmail.com',
		// port: 465,
		secure: true, // use SSL
		tls: {
			// do not fail on invalid certs
			rejectUnauthorized: false
		},
		auth: {
		  user: '<EMAIL>',
		  pass: '558585aa'
		}
	});

	const message = {
		from: 'Marcelo || QIPlus <<EMAIL>>', 			// Sender address
		to: '<EMAIL>',         	// List of recipients
		subject: 'Test Nodemailer', 			// Subject line
		html: '<p><img alt="" src="https://qiplus.com.br/ckfiles/i9TtRqSINnaVr0f7x4uDBtk6SmY2/images/template-logo-600x200.jpg" style="height:200px; width:600px" /></p><p>&nbsp;</p><p>Ol&aacute;&nbsp;%recipient.firstName%&nbsp;%recipient.lastName%,</p><p>&nbsp;</p><p>Confirme seus dados:</p><p>%recipient.phone%</p><p>%recipient.mobile%</p><p>%recipient.email%</p><p>&nbsp;</p><p><strong>Equipe QIPlus</strong></p><p>&nbsp;</p>' 		// Plain text body
	};

	// transporter.sendMail(message, function(err, info) {
	// 	if (err) {
	// 	  console.log({ err })
	// 	} else {
	// 	  console.log({ info });
	// 	}
	// 	exit()
	// });
	
	// const message = {
	// 	host: 'smtp.gmail.com',
	// 	port: 587,
	// 	user: '<EMAIL>',
	// 	pass: '558585aa',
	// 	from: 'Marcelo || QIPlus <<EMAIL>>', 			// Sender address
	// 	to: '<EMAIL>',         	// List of recipients
	// 	subject: 'Test Nodemailer', 			// Subject line
	// 	html: '<p><img alt="" src="https://qiplus.com.br/ckfiles/i9TtRqSINnaVr0f7x4uDBtk6SmY2/images/template-logo-600x200.jpg" style="height:200px; width:600px" /></p><p>&nbsp;</p><p>Ol&aacute;&nbsp;%recipient.firstName%&nbsp;%recipient.lastName%,</p><p>&nbsp;</p><p>Confirme seus dados:</p><p>%recipient.phone%</p><p>%recipient.mobile%</p><p>%recipient.email%</p><p>&nbsp;</p><p><strong>Equipe QIPlus</strong></p><p>&nbsp;</p>' 		// Plain text body
	// };
	
	// const sendSMTPMail = DevFunctions.httpsCallable('sendSMTPMail');

	// sendSMTPMail(message)
	// .then(response=>{
	// 	console.log('data',response.data);
	// 	exit();
	// })
	// .catch(err=>console.error(err)||exit())
	
	// const data = {
	// 	action: 'fck_send_smtp_mail',
	// 	host: 'smtp.gmail.com',
	// 	port: 587,
	// 	from_name: 'marcelo',
	// 	from_email: '<EMAIL>',
	// 	pass: '558585aa',
	// 	encryption: 'tls',
	// 	to: '<EMAIL>',         				// List of recipients
	// 	subject: 'Test PHPMailer', 								// Subject line
	// 	message: '<h1>Have the most fun you can via PHP!</h1>'	// Plain text body
	// };

	// const params = new URLSearchParams();
	// Object.keys(data).forEach((key,i)=>{
	// 	params.append(key, data[key]);		
	// })

	// axios.post(CONSTANTS.WP_AJAX_URL, params)
	// .then(function (response) {
	// 	console.log('axios > response', { data: response.data })
	// 	const { status, data } = response;
	// 	if (status === 200 && data.sent) {
			
	// 	}else{

	// 	}
	// 	console.log({ data: response.data });
	// 	console.log({ status: response.status });
	// 	console.log({ statusText: response.statusText });
	// 	// console.log({ headers: response.headers });
	// 	// console.log({ config: response.config });
	// 	exit();
	// })
	// .catch(err=>console.error({ err })||exit());
*/



let folderModel = {
	id: '',
	handle: '',
	type: 'inbox',
	title: '',
	icon: 'ti-cloud-down',
	emails: []
	/* 
	'id': 'inbox',
	'handle': 'inbox',
	'type': 'inbox',
	'title': 'mailboxes.inbox',
	'icon': 'ti-cloud-down',
	'emails': []
	*/	
}
let defaultFolders = [
	{
		...folderModel,
		'id': 'inbox',
		'handle': 'inbox',
		'type': 'inbox',
		'title': 'mailboxes.inbox',
		'icon': 'ti-cloud-down',
		'emails': []
	},
	{
		...folderModel,
		'id': 'sent',
		'handle': 'sent',
		'type': 'sent',
		'title': 'mailboxes.sent',
		'icon': 'ti-cloud-up',
		'emails': []
	},
	{
		...folderModel,
		'id': 'draft',
		'handle': 'draft',
		'type': 'draft',
		'title': 'mailboxes.draft',
		'icon': 'ti-write',
		'emails': []
	},
	{
		...folderModel,
		'id': 'spam',
		'handle': 'spam',
		'type': 'inbox',
		'title': 'mailboxes.spam',
		'icon': 'ti-alert',
		'emails': []
	},
	{
		...folderModel,
		'id': 'trash',
		'handle': 'trash',
		'type': 'inbox',
		'title': 'mailboxes.trash',
		'icon': 'ti-trash',
		'emails': []
	}
]

const assignAccountByOwner = async (collections) => {

	const accountOwners = {};
	const accountOrphans = [];
	
	let batch = FirestoreRef.batch();
	let promises = [];
	let count = 0;

	FirestoreRef.collection(COLLECTIONS.ACCOUNTS_COLLECTION_NAME).get().then(snapshot=>{

		snapshot.forEach(doc=>{
			let data = doc.data();
			let accountId = data.ID;
			let owner = data.owner;
			if ( owner ) {
				accountOwners[owner] = accountId;
			}else{
				console.log('No owner', { accountId });
			}				
		})	
		console.log(inspect({ accountOwners },true,8,true));

		collections.forEach(collection=>{
			
			let p = FirestoreRef.collection(collection).get().then(snapshot=>{
				let orphans = 0;
				snapshot.forEach(doc=>{

					let data = doc.data();
					let accountId = data.accountId;
					let owner = data.owner;

					if (!data.ID) return;
					
					if (!accountId || accountId===CONSTANTS.ORPHANS_ACCOUNT) {
						orphans++;
						
						let orphan = ({
							owner,
							accountId,
							ID: data.ID,
							title: data.title,
							collection
						})

						if ( (owner in accountOwners) && accountOwners[owner] !== CONSTANTS.ORPHANS_ACCOUNT ) {
							accountId = accountOwners[owner];
							if ( accountId ) {
								count++;
								// console.log('Found > accountId',accountId);
								if ( count % 500 === 0 ) {
									batch.commit();
									batch = FirestoreRef.batch();
								}
								batch.update(doc.ref, ({ accountId }))
							}else{
								console.log('Not Found > orphan',orphan);
							}
						}else{
							accountOrphans.push(orphan)
						}

						collection === COLLECTIONS.LEADS_COLLECTION_NAME && console.log('orphan',{ orphan });

					}
				})

				return { collection, orphans }
			})
			
			promises.push(p)
				
		})

		Promise.all(promises)
		.then(r=>{
			console.log(inspect({ r, accountOrphans },true,8,true));			
			batch.commit().then(r=>console.log('batch done'))
		})

	})	

}

const mailActions = async () => {
	
	// -------------------------

	let base64Str = ``, filePath = 'attachments/test2.jpg', metadata = { contentType: 'image/jpeg', test: true };

	// return uploadBase64File(base64Str, filePath, metadata, true)
	// .then(url=>{
	// 	console.log(`url: ${url}`)
	// })
	// .catch(err=>{
	// 	console.log('err',err);		
	// })

	// return getStoredFileUrl('attachments/test.jpg')
	// .then(url=>{
	// 	console.log(`url: ${url}`)
	// })
	// .catch(err=>{
	// 	console.log('err',err);		
	// })
	
	// cloneToProduction(workRef, workAdmin,[
	// 	{ collection: COLLECTIONS.MAILBOXES_COLLECTION_NAME },
	// ])
	
	
	let data = {
		folder: 'inbox',
		// action: 'list',
		action: 'read',
		range: '1:1',
		// range: 'last',
		// page: 1,
		// perpage: 5,
		config: {
			user: '<EMAIL>',
			password: 'Fck123@Amg!',
			host: 'imappro.zoho.com',
			// --------------------------------
			// user: '<EMAIL>',
			// password: 'sd22dharma',
			// host: 'imap.hostinger.com.ar',
			// --------------------------------
			// user: '<EMAIL>',
			// password: 'Postre5473',
			// host: 'imap.tecnifer.com.ar',
			// --------------------------------
			port: 993,
			tls: true
		}
	}

	// return getIMAPMail(data);
	// return getIMAPFolders(data);
	

	var MailParser = require("mailparser-mit").MailParser;
	var mailparser = new MailParser();

	firebase.functions().httpsCallable('getIMAPMail')(data)
	.then(async response=>{
		console.log('response',util.inspect(response.data.results, true, 8, true))
		// let encodedMsg = response.data.results.messages['1'].body;
		// let html = await simpleParser(encodedMsg)
		// console.log('encodedMsg',encodedMsg);
		// console.log('html',inspect(html.textAsHtml));

		// if (response.data.results.messages) {

		// 	let rawEmail = response.data.results.messages['1'].buffers.reduce((a,b)=>a+b,'');
	
		// 	// setup an event listener when the parsing finishes
		// 	mailparser.on("end", function(mail_object){
		// 		console.log("From:", mail_object.from); //[{address:'<EMAIL>',name:'Sender Name'}]
		// 		console.log("Subject:", mail_object.subject); // Hello world!
		// 		console.log("Text body:", mail_object.text); // How are you today?
		// 		exit()
		// 	});
	
		// 	// send the email source to the parser
		// 	mailparser.write(rawEmail);
		// 	mailparser.end();
			
		// }
				
		// exit();
	})
	.catch(error=>console.log('error',error)||exit())


	// firebase.functions().httpsCallable('getIMAPFolders')(data)
	// .then(response=>{
	// 	console.log('response',util.inspect(response.data, true, 8, true))
	// 	if ( Array.isArray((response.data.results||{}).folders) ) {
	// 		let folders = response.data.results.folders.map(f=>{
	// 			const { name, children, data } = f;
	// 			const { special_use_attrib } = data||{};
	// 			let d, t, n = (name.replace(/ /g,'')||'').toLowerCase()
	// 			if ( special_use_attrib ) {
	// 				t = (special_use_attrib.replace(/\\/g,'')||'').toLowerCase()
	// 				d = defaultFolders.find(f=>f.type===t);
	// 			}
	// 			d = d || defaultFolders.find(f=>f.type===n);
	// 			return {
	// 				...folderModel,
	// 				data,
	// 				children,
	// 				id: name,
	// 				handle: name,
	// 				type: d ? d.type : 'inbox',
	// 				title: name,
	// 				icon: d ? d.icon : 'zmdi zmdi-folder',
	// 			}
	// 		})
	// 		console.log('folders',inspect(folders, true, 8, true));			
	// 	}
	// 	exit()
	// })
	// .catch(error=>console.log('error',error)||exit())

	// exit()
}

const updateFunnelStats = (FirestoreRef, where) => {

	let FunnelsRef = FirestoreRef.collection(COLLECTIONS.FUNNELS_COLLECTION_NAME)
	
	Array.isArray(where) && where.forEach(queryArgs => (
		FunnelsRef = FunnelsRef.where(queryArgs[0], queryArgs[1], queryArgs[2])
	))

	return FunnelsRef.get().then(snap=>{

		let promises = snap.docs.map(async doc=>{
			
			const funnel = doc.data();
			const { pipeline } = funnel;

			if (!pipeline) return null;
			
			const dealsQuery = await FirestoreRef.collection(COLLECTIONS.DEALS_COLLECTION_NAME).where(CONSTANTS.FUNNEL_ID_FIELD,"==",doc.id).get();
			const deals = dealsQuery.docs.map(d=>d.data())
			
			dealsQuery.docs.filter(d=>typeof d.data().stage==="number").forEach(d=>{
				d.ref.update({ stage: `${d.data().stage}` })
			})

			let stats = {
				deals: deals.length,
				won: deals.filter(d=>d.stage==="won").length,
				lost: deals.filter(d=>d.stage==="lost").length,
			}

			let stages = []
			Object.keys(pipeline).filter(k=>k!=='won'&&k!=='lost')
			.forEach(k=>{
				stats[k] = deals.filter(d=>d.stage.toString()===k.toString()).length;
				stages.push(k)
			})

			console.log('updateFunnelStats', { stats, id: doc.id, stages });

			return doc.ref.update({ stats });
			
		})

		return Promise.all(promises);

	})
		
}

const updateKeywordsSubcollection = async (doc, FirestoreRef) => {

	// --------------------------------
	// KEYWORDS SUBCOLLECTION
	// keywords fields to subcollection
	// --------------------------------

	const docRef = doc.ref;
	const post = doc.data();
	const docId = doc.id;

	let { collection, owner, accountId } = post;

	log({ collection, owner, accountId });

	if ( !collection || !owner || !accountId ) {
		log(post)
		return;
	}

	let promises = [];
	
	// keywords
	let keywordsSource;

	const inheritedData = {
		docRef,
		docId,
		owner,
		accountId
	};

	switch (collection) {
		case COLLECTIONS.DEALS_COLLECTION_NAME:

			var contactId = post.contactId||'';

			var lead = {};
			if ( contactId ) {
				try { 
					lead = await fetchPost(COLLECTIONS.LEADS_COLLECTION_NAME, contactId); 
					inheritedData.contactRef = FirestoreRef.collection(COLLECTIONS.LEADS_COLLECTION_NAME).doc(contactId)
				} catch (error) { console.log('updateKeywordsSubcollection > err', { error }); }
			}
			
			var mergedContact = {
				...(lead||{}),
				...(post.contact||{}),
			}

			Object.keys(mergedContact).forEach((field,i)=>{
				const val = mergedContact[field];
				if (lead[field] && val==="") {
					mergedContact[field] = lead[field];
				}
			})
			
			keywordsSource = { 
				...mergedContact, 
				...post,
			};
				
		break;
		default:
			keywordsSource = { ...post };
		break;
	}
	
	const keywordsCollection = KEYWORDS_SUBCOLLECTIONS_MAP[collection] || KEYWORDS_SUBCOLLECTION_NAME;
	
	const keywordDocs = generateKeywordsDocs(keywordsSource);
		
	let keywordsBatch = FirestoreRef.batch();

	keywordDocs.forEach(keywordDoc=>{
		keywordDoc = {
			...keywordDoc,
			...inheritedData
		}
		let { index } = keywordDoc;
		let newDoc = replaceUndefined(keywordDoc);
		let kDocRef = docRef.collection(keywordsCollection).doc(index)
		keywordsBatch.set(kDocRef, newDoc)
	})
	
	promises.push(keywordsBatch.commit())

	return Promise.all(promises)

}

const deleteSubCollectionsFromTrashedPosts = async (workRef, workAdmin) => {

	const trashPath = `trash/posts`;
	const subcollections = await workAdmin.firestore().doc(trashPath).listCollections();
	const subcollectionIds = subcollections.map(col=>col.id);
	console.log(`subcollectionIds`,subcollectionIds);
	
	if (Array.isArray(subcollections)) {
		subcollectionIds.forEach(mainCol=>{

			workRef.collection(`${trashPath}/${mainCol}`).get()
			.then(subSnap=>{
				// console.log(`docPromises > ${mainCol} > subSnap`,subSnap.size);
				const docPromises = subSnap.docs.map(d=>{
					return workAdmin.firestore().doc(`${mainCol}/${d.id}`).listCollections()
				})
				return Promise.all(docPromises)
			})
			.then(results=>{
				results.forEach(subCols=>{
					if (subCols.length) {
						console.log(`${mainCol} > subcolections`, subCols.map(s=>s.id));
						subCols.forEach(sColRef=>{
							let col = sColRef.id;
							let parent = sColRef.parent.id;
							
							sColRef.get().then(async sColSnap=>{
								let parentDoc = await sColRef.parent.get();
								console.log(`${mainCol} > ${parent} > ${col}`, sColSnap.size, parentDoc.exists);
								if (!parentDoc.exists) {
									// >>>>>>>>>> 					<<<<<<<<<<<
									// >>>>>>>>>>  DESCOMENTAR AQUI	<<<<<<<<<<<
									// >>>>>>>>>> 					<<<<<<<<<<<
									// Promise.all(sColSnap.docs.map(sColDoc=>sColDoc.ref.delete()))
									// .then(deleteResults=>{
									// 	console.log(`${mainCol} > ${parent} > ${col} > delete`, deleteResults.length);
									// 	console.log('-------------------------------------------- ');
									// })
									// >>>>>>>>>> DESCOMENTAR ATE AQUI -----------------------------
								}
							})
						});
					}
				})
			})
			
		})
	}
	
}
// -------------------------

const addAndUpdateLogsToPosts = (workRef, collection) => {

	workRef.collection(collection).limit(1000).get()
	.then(snap=>{
		console.log(`addAndUpdateLogsToPosts  > snap`);
		console.log({ posts: snap.size, /* ids: snap.docs.map(d=>d.id) */ });
			
		let cancelAll = false;
		snap.forEach(async fDoc=>{
			if (cancelAll) {
				exit()
				return;
			}
			
			const docId = fDoc.id;
			const docRef = fDoc.ref;
			const post = fDoc.data();
			const postId = post.ID;
				
			let { title, date, data, context } = post;
			
			let postLogsQuery = workRef.collection(collection).doc(postId).collection(COLLECTIONS.LOGS_COLLECTION_NAME)
			
			let where = []			
			where.push(["trigger","==",APP_TRIGGERS.APP_TRIGGER_ADDED])
			where.forEach(queryArgs => (
				postLogsQuery = postLogsQuery.where(queryArgs[0], queryArgs[1], queryArgs[2])
			))

			let postLogsSnap = await postLogsQuery.limit(5000).get()
			let count = { postId, title, date, total: data.total, size: postLogsSnap.size }
			
			let promises = []

			try {
				
				if (!postLogsSnap.size) {
					
					let triggerName = APP_TRIGGERS.APP_TRIGGER_ADDED; 
					let contactId = data.contactId||post.contactId||'';
			
					const logKeys = [CONSTANTS.DATE_FIELD, CONSTANTS.MODIFIED_FIELD, ...CONSTANTS.RELATIONAL_FIELDS];
					const logData = {};
					
					logKeys.forEach(key=>{
						if ( key in post ) {
							logData[key] = post[key];
						}	
					})
					
					switch (collection) {
						case COLLECTIONS.TICKETS_COLLECTION_NAME:
							logData['total'] = (post.data||{}).total||0
							logData['value'] = (post.data||{}).value||0
							logData['operator_id'] = (post.context||{}).operator_id||0
						break;
						default:break;
					}

					const newLog = {
						contactId,
						user_id: contactId,
						operator_id: post.operatorId||post.author||0,
						id: docId,
						collection,
						trigger: triggerName,
						date,
						owner: logData.owner||'',
						accountId: logData.accountId||'',
						data: logData,
						context: post.context||{},
					}
	
					// promises = []
					
					let newLogRef = workRef.collection(collection).doc(postId).collection(COLLECTIONS.LOGS_COLLECTION_NAME).doc()
					let newLogId = newLogRef.id;
					
					promises = [
						newLogRef.set({
							...newLog,
							evaluated: true,
							createdAt: post.createdAt||new Date(date).getTime(),
							updatedAt: post.updatedAt||new Date(date).getTime(),
						})
						// .catch(err=>{
						// 	log({msg: 'add > errr',err})
						// 	cancelAll = true;
						// 	exit()
						// })
					]				
				}else{
	
					promises = postLogsSnap.docs.map(lDoc=>{
						return new Promise(async res=>{
							const logId = lDoc.id;
							const logRef = lDoc.ref;
							const lData = lDoc.data();
							
							let contactId = lData.contactId||data.contactId||post.contactId||'';
							// console.log('getLogs > post > lData',lData);
		
							let { trigger, date, createdAt } = lData
							if ( !lData.data ) {
								lData.data = {}
							}
		
							switch (collection) {
								case COLLECTIONS.TICKETS_COLLECTION_NAME:
									lData.data['total'] = (post.data||{}).total||0
									lData.data['value'] = (post.data||{}).value||0
									lData.data['operator_id'] = (post.context||{}).operator_id||0
								break;
								default:break;
							}

							// count.lData = lData;
		
							// return res(count)
							if (!contactId) {
								return res(count)
							}
		
							let newLogRef = workRef.collection(COLLECTIONS.LEADS_COLLECTION_NAME).doc(contactId).collection(COLLECTIONS.LOGS_COLLECTION_NAME).doc()
							let newLogId = newLogRef.id;

							return newLogRef.set({
								...lData,
								logId: newLogId,
								originId: logId,
								evaluated: true,
								createdAt: post.createdAt||new Date(date).getTime(),
								updatedAt: post.updatedAt||new Date(date).getTime(),
							}).then(r=>res(count))

						})
					})
				}
	
				let results = await Promise.all(promises)
				.catch(err=>{
					log({msg: 'results errr',err})
					cancelAll = true;
				})
				
				let r = results.pop()
	
				if (count.size) {
					log(count);
				}else{
					log({ r });
				}

			} catch (error) {
				log({msg: 'trycatch errr',error})
				cancelAll = true;
				exit()
			}


		})
	})

}

const replaceTrigger = (collection,trigger,logData) => {

	let {
		SELLER_FIELD, PROMOTER_FIELD, MANAGER_FIELD, QIUSER_FIELD, AUTHOR_FIELD, OWNER_FIELD, TEAM_FIELD, AFFILIATE_FIELD, PARENT_FIELD, ACCOUNT_FIELD, CAMPAIGN_FIELD, CONTACT_ID_FIELD, FUNNEL_ID_FIELD
	} = CONSTANTS

	let newTriggers = {
		[SELLER_FIELD]: `${SELLER_FIELD}_changed`,
		[PROMOTER_FIELD]: `${PROMOTER_FIELD}_changed`,
		[MANAGER_FIELD]: `${MANAGER_FIELD}_changed`,
		[QIUSER_FIELD]: `${QIUSER_FIELD}_changed`,
		[AUTHOR_FIELD]: `${AUTHOR_FIELD}_changed`,
		[OWNER_FIELD]: `${OWNER_FIELD}_changed`,
		[TEAM_FIELD]: `${TEAM_FIELD}_changed`,
		[AFFILIATE_FIELD]: `${AFFILIATE_FIELD}_changed`,
		[PARENT_FIELD]: `${PARENT_FIELD}_changed`,
		[ACCOUNT_FIELD]: `${ACCOUNT_FIELD}_changed`,
		[CAMPAIGN_FIELD]: `${CAMPAIGN_FIELD}_changed`,
		[CONTACT_ID_FIELD]: `${CONTACT_ID_FIELD}_changed`,
		[FUNNEL_ID_FIELD]: `${FUNNEL_ID_FIELD}_changed`,
	}

	let orig = trigger
	if (newTriggers[trigger]) {
		trigger = newTriggers[trigger]
	}else
	if ( COLLECTION_TRIGGERS[collection] ) {
		let update = true
		switch (trigger) {
			case COLLECTION_TRIGGERS[collection].added:
			case APP_TRIGGERS.APP_TRIGGER_ADDED:
				switch (collection) {
					case COLLECTIONS.STORES_COLLECTION_NAME:
						if ( logData.contactId	|| 
							((logData.context||{}).collection && (logData.context||{}).collection!==collection)
						) {								
							trigger = APP_TRIGGERS.APP_TRIGGER_ADDED_TO_STORE
							update = false
						}							
					break;
					case COLLECTIONS.INTEGRATIONS_COLLECTION_NAME:
						if ( logData.contactId	|| 
							((logData.context||{}).collection && (logData.context||{}).collection!==collection) || 
							!(logData.data||{})._REQUEST	
						) {								
							trigger = APP_TRIGGERS.APP_TRIGGER_ADDED_VIA_INTEGRATION
							update = false
						}
					break;
					case COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME:
						if ( logData.contactId	|| 
							((logData.context||{}).collection && (logData.context||{}).collection!==collection)
						) {								
							trigger = APP_TRIGGERS.APP_TRIGGER_ADDED_TO_SEGMENTATION
							update = false
						}							
					break;
					case COLLECTIONS.FUNNELS_COLLECTION_NAME:
						if ( logData.contactId	|| 
							('stage' in (logData.data||{})) ||
							('currentStage' in (logData.data||{})) 
						) {								
							trigger = APP_TRIGGERS.APP_TRIGGER_ADDED_TO_FUNNEL
							update = false
						}
					break;
					default:
						break;
				}
				update && (trigger = COLLECTION_TRIGGERS[collection].added)
			break;
			case APP_TRIGGERS.APP_TRIGGER_REMOVED:
				if (logData.contactId) {
					switch (collection) {
						case COLLECTIONS.STORES_COLLECTION_NAME:
							trigger = APP_TRIGGERS.APP_TRIGGER_REMOVED_FROM_STORE
						break;
						case COLLECTIONS.INTEGRATIONS_COLLECTION_NAME:
							trigger = APP_TRIGGERS.APP_TRIGGER_REMOVED
						break;
						case COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME:
							trigger = APP_TRIGGERS.APP_TRIGGER_REMOVED_FROM_SEGMENTATION
						break;
						case COLLECTIONS.FUNNELS_COLLECTION_NAME:
							trigger = APP_TRIGGERS.APP_TRIGGER_FUNNEL_REMOVED
						break;
						default:
							break;
					}
				}else{
					trigger = COLLECTION_TRIGGERS[collection].removed
				}
			break;
			case APP_TRIGGERS.APP_TRIGGER_UPDATED:
				if (logData.contactId) {
					switch (collection) {
						case COLLECTIONS.INTEGRATIONS_COLLECTION_NAME:
							trigger = APP_TRIGGERS.APP_TRIGGER_UPDATED_VIA_INTEGRATION
						break;
						default:break;
					}
				}else{
					trigger = COLLECTION_TRIGGERS[collection].changed
				}
			break;
			default:break;
		}
	}		
	
	return trigger
}

const updateCollectionLogs = async (FirestoreRef, collections, options) => {

	let { accountId, orderBy, order, limit, commit, debug, queries, postsQueries, ...other } = options||{}
	
	orderBy=orderBy||CONSTANTS.UPDATED_FIELD
	order=order||(orderBy===CONSTANTS.UPDATED_FIELD?'desc':undefined)
	queries = queries||[]
	postsQueries = postsQueries||[]
	
	let shouldCommit = commit===1||commit===true
	
	let batch = new helpers.FirestoreBatchHandler(FirestoreRef)
	
	let targetCollections = typeof collections === 'string' ? [collections] : collections

	let promises = targetCollections.map(collection=>new Promise(async resolveAll=>{

		batch.reset()
		
		let lastPos=""
		let lastUsedPos=lastPos
		let totalSize = 0
		
		let updateLogs = async lastPos => {
				
			lastPos && (lastUsedPos = lastPos);

			let postsQuery = FirestoreRef.collection(collection)

			accountId && (postsQuery = postsQuery.where(CONSTANTS.ACCOUNT_FIELD,"==",accountId))
			postsQueries.forEach(queryArgs=>(postsQuery=postsQuery.where(queryArgs[0],queryArgs[1],queryArgs[2])))

			orderBy && (postsQuery = postsQuery.orderBy(orderBy,order))
			lastPos && (postsQuery = postsQuery.startAt(lastPos))
			limit && (postsQuery = postsQuery.limit(limit))
			
			return postsQuery.get()
			.then(postsSnap=>{
				
				totalSize += postsSnap.size
				
				log({ size: postsSnap.size, collection, lastPos })
				// return 

				let postIds = []
				let allLogs=[]
				let updatedLogs=[]
				let removeLogs=[]
				let diffLogs=[]
				let upCounter = 0								
				
				let replacedTriggers = []	
			
				let promises = postsSnap.docs.map(doc=>new Promise(resolve=>{
					
					const docId = doc.id;
					const docRef = doc.ref;
					const post = doc.data();
					const postId = post.ID || docId;
					
					lastPos = post[orderBy]

					postIds.push(postId)
					
					// upCounter = 0
					let innerP = []
					// let logsQuery = FirestoreRef.collection(COLLECTIONS.LOGS_COLLECTION_NAME).doc(collection).collection(postId);
					let logsQuery = docRef.collection(COLLECTIONS.LOGS_COLLECTION_NAME);
					
					queries.forEach(queryArgs=>(logsQuery=logsQuery.where(queryArgs[0],queryArgs[1],queryArgs[2])))
					
					innerP.push(
						logsQuery.get().then(snapshot=>{
							snapshot.docs.forEach((d, i)=>{				

								let logData = d.data()
								let logRef = d.ref
								let logId = d.id
								let { trigger } = logData
								
								if (d.data && d.data().id === postId && d.data().collection === collection)  {
																	
									allLogs.push(logData)

									let data = logData.data||{}
									let context = logData.context||{}
									let logContext = context
									let newData = logData

									let updates = { 
										trigger,
										fields: [], 
										keywords: [], 
										collections: [] 
									}

									let pushKeywords = (field,val,col,isRelated) => {
		
										let fieldVal = `[${field}]:[${val}]`;
										let colVal = `[collection]:[${col}]`;
								
										updates.keywords.push(val)		
										updates.keywords.push(`${fieldVal}`)
										updates.keywords.push(`${colVal};${fieldVal}`)
								
										updates.fields = [...new Set([...updates.fields, field])]
										updates.collections = [...new Set([...updates.collections, col])]
								
										if (!isRelated) {
											CONSTANTS.RELATIONAL_FIELDS
											.filter(r=>newData[r]||data[r]||logContext[r])
											.forEach(related=>{
												let relCol = CONSTANTS.RELATIONAL_COLLECTIONS[related]
												let relVal = newData[related]||data[related]||logContext[related]
												if ( relVal ) {
								
													let rVal = `[${related}]:[${relVal}]`;
													let rColVal = `[relatedCol]:[${relCol}]`;
										
													updates.keywords.push(`${rVal};${fieldVal}`)					
													updates.keywords.push(`${rVal};${colVal}`)
													updates.keywords.push(`${rVal};${colVal};${fieldVal}`)
													updates.keywords.push(`${rColVal};${rVal};${colVal};${fieldVal}`)				
												}
											})
										}
								
									}

									let keywordFields = ['trigger','id','contactId','date']

									keywordFields
									.filter(field=>logData[field])
									.forEach(field=>{
										let val = updates[field]||logData[field];
										let col = collection;
										pushKeywords(field, val, col)
									})
								
									let dateFormats = [CONSTANTS.MOMENT_ISO_DAY]
								
									dateFormats
									.forEach(format=>{
										let val = logData.createdAt ? moment(logData.createdAt).format(format) : moment(logData.date).format(format);
										let col = collection;
										pushKeywords(format, val, col)
									})
									
									CONSTANTS.RELATIONAL_FIELDS
									.filter(field=>logData[field]||data[field]||context[field])
									.map(field=>{
										let col = CONSTANTS.RELATIONAL_COLLECTIONS[field]
										let val = logData[field]||data[field]||context[field]
										if ( val ) {
											pushKeywords(field, val, col, true)
										}
										return null
									})
								
									let updatedLog = {
										...logData,
										...updates
									}
									
									if (!helpers.areEqualObjects({...updatedLog},{...logData})) {
										shouldCommit && batch.update(logRef, updates)
										updatedLogs.push(updatedLog)
										diffLogs.push(helpers.diffObjects({...updatedLog/* ,keywords:'' */}, {...logData/* ,keywords:'' */}, 100))
									}
									
									if (!updates.keywords.length) {
										log({ collection, logData, msg: "NOOOOO" })
									}else
									if (upCounter<1) {
										// log({ collection, keywords: updates.keywords })
									}
									
									upCounter++
									
									// let newRef = FirestoreRef.collection(collection).doc(postId).collection(COLLECTIONS.LOGS_COLLECTION_NAME).doc(logId);
									// shouldCommit && batch.set(newRef, logData)
									
								}else{

									removeLogs.push(logData)

									shouldCommit && batch.delete(logRef)

								}
							})
							// log({ logs: snapshot.size, collection })
							return null
						})
					)

					return Promise.all(innerP)
					.then(()=>{
						resolve()
					})

				}))
				
				Promise.all(promises)
				.then(r=>{
					
					let onFinish=transactions=>{

						let updates = {}
						updatedLogs.forEach(l=>{
							updates[l.trigger]=(updates[l.trigger]||0)+1
							// updates[l.date.split(' ')[0]]=(updates[l.date.split(' ')[0]]||0)+1
						})
						let current = {}
						allLogs.forEach((l,i)=>{
							current[l.trigger]=(current[l.trigger]||0)+1
							// current[l.date.split(' ')[0]]=(current[l.date.split(' ')[0]]||0)+1
						})
						let removes = {
							triggers: {},
							collections: {},
						}
						removeLogs.forEach(l=>{
							removes.triggers[l.trigger]=(removes.triggers[l.trigger]||0)+1
							removes.collections[l.collection]=(removes.collections[l.collection]||0)+1
						})

						Object.keys(current).forEach(k=>{
							if (updates[k]===current[k]) {
								delete current[k]
							}
						})

						let result = { 
							collection, 
							lastPos,
							totalSize,
							size: postsSnap.size, 
							uLen: updatedLogs.length, 
							logs: [...allLogs,...removeLogs].length, 
							removes, 
							current, 
							updates
						}

						if (shouldCommit) {
							let tx = {}
							Object.keys(transactions).forEach((k,i)=>{
								let l = Array.isArray(transactions[k]) ? transactions[k].length : transactions[k];
								l && (tx[k]=l)
							})
							result = { ...result, tx }
						}
						
						debug && log({ diffLogs })

						if (replacedTriggers.length) {
							let [a] = replacedTriggers
							let { orig, trigger, collection, contactId, data, context } = a
							log({ orig, trigger, collection, contactId, data, context, keyword: (a.keywords||[])[0], len: replacedTriggers.length },'chklok',100)
						};
						
						log(result);
						
						if (postsSnap.size===limit && lastPos && lastPos!==lastUsedPos) {
							updateLogs(lastPos)
						}else{
							resolveAll(result)
						}
					}
					
					if (shouldCommit) {
						batch.commit(onFinish)
					}else{
						onFinish()
					}
					
				})

			})
		}

		updateLogs()
		
	}))
	
	Promise.all(promises).then(r=>{
		console.log(r.length,' finisehd > waiting:', batch.waiting );
		!batch.waiting && exit()
	})
	
}

// -------------------------
const updateCycleDays = () => {

	return FirestoreRef.collection(COLLECTIONS.FUNNELS_COLLECTION_NAME).get().then(async snap=>{

		let promises = snap.docs.map(async doc=>{

			let funnelId = doc.id;
			let funnel = doc.data();

			const dealsCount = await FirestoreRef.collection(COLLECTIONS.DEALS_COLLECTION_NAME).where(CONSTANTS.FUNNEL_ID_FIELD,"==",funnelId).get();
			const deals = dealsCount.docs.map(d=>d.data())

			let stats = {
				...funnel.stats||{},
				deals: deals.length
			}

			let { pipeline } = funnel;

			Object.keys(pipeline).forEach(k=>{
				stats[k] = deals.filter(d=>d.stage.toString()===k.toString()).length;
			})
			
			let logPromises = []
			
			let wonCycleDays = 0;
			let wonCycleSeconds = 0;
			
			deals.filter(d=>d.stage===CONSTANTS.WON_STAGE)
			.forEach((deal,i) => {
				if (('wonCycleDays' in (deal.stats||{})) && helpers.isNumeric(deal.stats.wonCycleDays)) {
					const days = deal.stats.wonCycleDays||0;
					const seconds = deal.stats.wonCycleSeconds || days*24*60*60;
					wonCycleDays+=days;
					wonCycleSeconds+=seconds;
				}else{
					logPromises.push(
						FirestoreRef.collection(COLLECTIONS.DEALS_COLLECTION_NAME).doc(deal.ID).collection(COLLECTIONS.LOGS_COLLECTION_NAME)
						.where("trigger","==","won")
						.get()
						.then(s=>{
							let log = s.docs.map(d=>d.data()).sort(helpers.sortByUpdateDate)[0]
							if (log && log.date && deal.date) {
								const days = Math.abs(moment(log.date).diff(moment(deal.date),'days'));
								const seconds = Math.abs(moment(log.date).diff(moment(deal.date),'seconds'));
								if (helpers.isNumeric(days)) {
									wonCycleDays += days;
									wonCycleDays += seconds;
								}
							}
						})
					)
					
				}
			});

			return Promise.all(logPromises)
			.then(results=>{

				if (!results.length) {
					return null
				}
				
				stats.wonCycleDays = wonCycleDays;
				stats.wonCycleSeconds = wonCycleSeconds;

				log({ funnelId, results: results.length, stats })

				// return (doc.ref.get());
				return (doc.ref.update({ stats }));

			})
	
		})

		return Promise.all(promises)
		
	})
	
}

let updateBirthdays = (accountId, limit, startAfter) => new Promise(resolveAll=>{
		
	limit = limit||5000;

	let batch = new helpers.FirestoreBatchHandler(FirestoreRef)
	let totalTx = 0;
	let lastPos = "";
	let lastUsedPos=lastPos;
	
	let updateBDay = (lastPos) => {
		
		lastPos && (lastUsedPos = lastPos);

		let query = FirestoreRef.collection(COLLECTIONS.LEADS_COLLECTION_NAME)
		.where("birthday",">","")
		.orderBy('birthday','asc')
		
		// accountId && (query = query.where("accountId","==",accountId));
		lastPos && (query = query.startAfter(lastPos));
	
		query = query.limit(limit)

		query.get().then(async ({ size, docs })=>{
			
			totalTx+=size;
			for (const d of docs) {
				lastPos = d.data().email;
			}
			
			// log({ size, lastPos });

			for (const d of docs) {
	
				let docRef = d.ref;
	
				let newData = d.data()
				let updatedData = {}
	
				let email = newData.email||"";
				if ( 
					email.indexOf('teste')>-1 
					||
					(email.indexOf('@qiplus.com')>-1 && !email.split('@qiplus.com')[1] )
	
				) {
					continue;
				}
				
				if (!('birthday_verified' in newData)) {
				
					let bday = helpers.sTrim(newData.birthday).replace(/ /g,'');
					if (bday.indexOf('/')>=0) {
						let birthday = bday.split('/').reverse().map((v,i)=>{
							let val = v;
							switch (i) {
								case 0:							
									if (val.toString().length<4) val = Number('20'+val)-1>=new Date().getFullYear() ? '19'+val : '20'+val;
								break;					
								default:
									while (val.toString().length<2) val = '0'+val;
								break;
							}
							return val;
						}).join('-')
						updatedData.birthday = birthday;
					}
					
					let momentBday = moment(updatedData.birthday||bday);
					let isValidDate = momentBday.isValid();
			
					updatedData.birthday_verified = isValidDate;
					
					if (isValidDate) {
						updatedData.birth = {
							year: momentBday.format('YYYY'),
							month: momentBday.format('MM'),
							day: momentBday.format('DD'),
						}
					}
				}
	
				// log({ updates: Object.keys(updatedData).length, email: newData.email });
	
				!!Object.keys(updatedData).length && batch.update(docRef, updatedData)
				
			}
	
			log({ size, lastPos, totalTx });

			if (size===limit && lastPos && lastPos!==lastUsedPos) {
				updateBDay(lastPos)
			}else{
				batch.commit((transactions)=>{
					let tx = {}
					Object.keys(transactions).forEach((k,i)=>{
						let l = Array.isArray(transactions[k]) ? transactions[k].length : transactions[k];
						l && (tx[k]=l)
					})
					log({ tx, size, lastPos, totalTx });
					resolveAll(totalTx)
				})
			}	
	
		})
	}

	updateBDay(startAfter||"");

})

let batchEmailValidation = (accountId, limit, startAfter) => new Promise(resolveAll=>{
		
	limit = limit||5000;

	let batch = new helpers.FirestoreBatchHandler(FirestoreRef)
	let totalTx = 0;
	let lastPos = "";
	let lastUsedPos=lastPos;
	
	let validate = (lastPos) => {
		
		lastPos && (lastUsedPos = lastPos);

		let query = FirestoreRef.collection(COLLECTIONS.LEADS_COLLECTION_NAME)
		.where("email",">","")
		
		accountId && (query = query.where("accountId","==",accountId));
		
		query = query.orderBy('email','asc')

		lastPos && (query = query.startAfter(lastPos));
		
		query
		.limit(limit)
		.get().then(async ({ size, docs })=>{
			
			totalTx+=size;
			for (const d of docs) {
				lastPos = d.data().email;
			}
			let entered=0, hasMail=0, validated=0;
			
			log({ size, lastPos });

			for (const d of docs) {
	
				let docRef = d.ref;
	
				let newData = d.data()
				let updatedData = {}
	
				let email = newData.email||"";
				if ( 
					email.indexOf('teste')>-1 
					||
					(email.indexOf('@qiplus.com')>-1 && !email.split('@qiplus.com')[1] )
	
				) {
					continue;
				}
				
				if ( !('mailbox_verified' in newData) ) {
					entered++
					if (email) {
						try {
							hasMail++
							let emailValidation = await validateEmailAddress(email);
							log({ emailValidation });
							
							if (emailValidation && emailValidation.address) {
								validated++
								updatedData.mailbox = emailValidation;
								updatedData.is_valid = Boolean(emailValidation.is_valid);
								updatedData.mailbox_verified = ['true',true].includes(emailValidation.mailbox_verification);
							}
						} catch (error) {
							console.error(error);
							updatedData.mailbox_verified = null;
						}
					}else{
						updatedData.mailbox_verified = null;
						updatedData.is_valid = false;
					}
				}
	
				// log({ updates: Object.keys(updatedData).length, email: newData.email });
	
				!!Object.keys(updatedData).length && batch.update(docRef, updatedData)
				
			}
	
			log({ size, lastPos, entered, hasMail, validated, totalTx });

			if (size===limit && lastPos && lastPos!==lastUsedPos) {
				validate(lastPos)
			}else{
				batch.commit((transactions)=>{
					let tx = {}
					Object.keys(transactions).forEach((k,i)=>{
						let l = Array.isArray(transactions[k]) ? transactions[k].length : transactions[k];
						l && (tx[k]=l)
					})
					log({ tx, size, lastPos, totalTx });
					resolveAll(totalTx)
				})
			}	
	
		})
	}

	validate(startAfter||"");

})

const validateEmailAddress = email => new Promise(res=>{
	validateMailbox(email,({error, body})=>{
		log('validateEmailAddress', {error, body})
		if (error) return res(null)
		return res(body)
	})
})

const updateRef = (docRef, data) => new Promise(async res=>{
	let updatedRef = docRef && docRef.get ? await docRef.get() : null;
	if (updatedRef && updatedRef.exists) {
		return docRef.update(data).then(r=>res({
			...updatedRef.data(),
			...data
		}))
	}
	return res(null)
});

// -------------------------
const coreCollections = []

coreCollections[0] = COLLECTIONS.LEADS_COLLECTION_NAME;
coreCollections[1] = COLLECTIONS.DEALS_COLLECTION_NAME;
coreCollections[2] = COLLECTIONS.QIUSERS_COLLECTION_NAME;
coreCollections[3] = COLLECTIONS.TEAMS_COLLECTION_NAME;
coreCollections[4] = COLLECTIONS.FUNNELS_COLLECTION_NAME;
coreCollections[5] = COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME;
coreCollections[6] = COLLECTIONS.INTEGRATIONS_COLLECTION_NAME;
coreCollections[7] = COLLECTIONS.TICKETS_COLLECTION_NAME;
coreCollections[8] = COLLECTIONS.FORMS_COLLECTION_NAME;
coreCollections[9] = COLLECTIONS.MAILING_COLLECTION_NAME;
coreCollections[10] = COLLECTIONS.CAMPAIGNS_COLLECTION_NAME;
coreCollections[11] = COLLECTIONS.LANDING_PAGES_COLLECTION_NAME;
coreCollections[12] = COLLECTIONS.PRODUCTS_COLLECTION_NAME;
coreCollections[13] = COLLECTIONS.AUTOMATIONS_COLLECTION_NAME;
coreCollections[14] = COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME;
coreCollections[15] = COLLECTIONS.ACCOUNTS_COLLECTION_NAME;
coreCollections[16] = COLLECTIONS.CONTRACTS_COLLECTION_NAME;
coreCollections[17] = COLLECTIONS.EVENTS_COLLECTION_NAME;
coreCollections[18] = COLLECTIONS.CALENDAR_COLLECTION_NAME;
coreCollections[19] = COLLECTIONS.QUESTIONNAIRES_COLLECTION_NAME;
coreCollections[20] = COLLECTIONS.TASKLISTS_COLLECTION_NAME;
coreCollections[21] = COLLECTIONS.STORES_COLLECTION_NAME;


const firebaseActions = async () => {
	
	// -----------------------------
	// envOption = envOptions.emulator;
	// envOption = envOptions.production;
	// envOption = envOptions.development;
	envOption = envOptions[env];
	
	// -----------------------------
	
	// -----------------------------
	webmasterId = envOption.webmasterId;
	webmasterAcc = envOption.webmasterAcc;
	workStorage = envOption.storage;
	workBucket = envOption.bucket;
	workAppId = envOption.appId;
	workRef = envOption.ref;
	workAdmin = envOption.admin;
	// -----------------------------

	let promises = []
	let debug = argv.debug===1||false
	let shouldCommit = argv.commit===1||false
	let targetCollections = coreCollections.filter((c,i)=>{
		return argv.col===i || (argv.cols||[]).includes(i)
	})
	let batch = new helpers.FirestoreBatchHandler(FirestoreRef);
	
	// let r = await Promise.all([
	// 	'<EMAIL>',
	// ].map(e=>validateEmailAddress(e)))
	// log({ r })
	// return 
	
	// let lastPos = "";
	// // lastPos = "<EMAIL>";

	
	let query = FirestoreRef.collection('leads')
	// .where("is_valid","==",true)
	.where("mailbox_verified","==",true)
	// .where("birthday_verified","==",true)
	// .where("birth.month","==","03")
	// .where("email",">","")
	// .orderBy('email','asc')
	
	query.get().then(async ({ size, docs })=>{
		log({ size });
	});
	return;
	
	// batchEmailValidation(null, 10, "")
	// .then(r=>{
	// 	log({ r })
	// 	setTimeout(() => {exit()}, 5000 );
	// });

	// updateBirthdays(null, null, "")
	// .then(r=>{
	// 	log({ r })
	// 	setTimeout(() => {exit()}, 5000 );
	// });
	
	
	// const s = await getSubscription(2846567)
	// log({ s })
	
	// let conta = {
	// 	sId: 2559797,
	// 	accountId: 'XNjzQixAm1OkM6At1115e9bwMLW2',
	// 	ownerId: 'XNjzQixAm1OkM6At1115e9bwMLW2',
	// 	planName: 'Plano Sinapses',
	// 	date: '2021-08-12 00:00:00'
	// }

	// try {

	// 	let { accountId, ownerId, sId, date, planName } = conta
		
	// 	let scheduled_moment = moment(date)
	// 	let scheduled_date = scheduled_moment.format(MOMENT_ISO)

	// 	const { CRONJOB_SUBJECTS, CRONJOB_TYPES } = CONSTANTS;

	// 	promises = await addNewPost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, {
	// 		api: CRONJOB_SUBJECTS.INVOICE,
	// 		executed: false,
	// 		execution_date: "",
	// 		scheduled_date,
	// 		date: moment().format(MOMENT_ISO),
	// 		modified: moment().format(MOMENT_ISO),
	// 		type: CRONJOB_TYPES.BILLING_SCHEDULE,
	// 		data: { 
	// 			description: `Assinatura ${planName}`,
	// 			subscription_id: sId,
	// 			accountId, 
	// 			ownerId
	// 		},
	// 		context: {
	// 			collection: 'accounts',
	// 			id: accountId
	// 		},
	// 	})

	// 	let results = promises

	// 	log({ results })
		
	// } catch (error) {
	// 	log({ error })
	// }
	
	// try {
	// 	const s = await ProductionFunctions.httpsCallable('doBillingCron')({ hi: 'oi' })
	// 	log({ s })
	// } catch (error) {
	// 	log({ error })
	// }



	// exit()
}

