const { ROLES, CONSTANTS, COLLECTIONS,  FirestoreRef, helpers, moment, momentNow, langMessages, i18nLabels } = require('../init');
const { getFieldValue, extractShortcodes, extractShortcodeFields, replaceAll, isLoopableObject, json<PERSON><PERSON> } = helpers;

const {
	CONTRACT_FIELDS_GROUP,
	TICKET_FIELDS_GROUP,
	DATE_FIELDS_GROUP,
	TEAM_FIELDS_GROUP,
	ADDRESS_FIELDS_GROUP,
	QIUSERS_FIELDS_GROUP,
	CUSTOM_FIELDS_GROUP,
	SHORTCODE_PATH_SEPARATOR,
	SHORTCODE_LABEL_SEPARATOR,
	SHORTCODE_PARAMS_SEPARATOR,
	MOMENT_SHORT,
	MOMENT_ISO,
} = CONSTANTS;

// grabHereToCopyPaste
const replaceShortCodes = ( content, contentPost, srcCollection, fetchedPosts, fetchedTaxonomies ) => {

	fetchedPosts = fetchedPosts || [];
	fetchedTaxonomies = fetchedTaxonomies || {};
	
	return new Promise((resolve, reject)=>{

		const replacements = {}
		
		if ( content && typeof content === 'string' ) {

			const requiredCollections = {}
			const relatedCollections = {}
			const shortcodes = extractShortcodes(content)
			const shortcodeFields = extractShortcodeFields(content)
			
			shortcodeFields.forEach((s,i)=>{

				let shortcode = shortcodes[i];
				let shortcodeContent = s;
				let shortcodeField = shortcodeContent.split(SHORTCODE_LABEL_SEPARATOR)[0];
				let shortcodePath = (shortcodeContent.indexOf(SHORTCODE_PATH_SEPARATOR)!==-1 && shortcodeContent.split(SHORTCODE_PATH_SEPARATOR)[0]) || '';
				let field = shortcodeContent.replace(`${shortcodePath}${SHORTCODE_PATH_SEPARATOR}`,'').split(SHORTCODE_PARAMS_SEPARATOR)[0];
				let keyArr = field.split(':');
				
				let collection = '';
				let key = '';
				let name = '';
				let group = '';
				let docId = '';
				let value = '';
				let role = '';
				let relatedField = '';
				let sourcePost = '';
				
				switch (shortcodePath) {
					case CONTRACT_FIELDS_GROUP:
						collection = COLLECTIONS.CONTRACTS_COLLECTION_NAME;
						sourcePost = contentPost.collection === COLLECTIONS.CONTRACTS_COLLECTION_NAME ? contentPost : (fetchedPosts.find(p=>p.collection===collection)||{});
						docId = sourcePost.ID;
						name = keyArr.pop();
						key = keyArr.join(':');
						value = getFieldValue({ key, name }, sourcePost, false, fetchedPosts)
					break;
					
					case TICKET_FIELDS_GROUP:
						collection = COLLECTIONS.TICKETS_COLLECTION_NAME;
						sourcePost = fetchedPosts.find(p=>p.collection===collection)||{};
						docId = sourcePost.ID || 0;
						name = keyArr.pop();
						key = keyArr.join(':');
						value = getFieldValue({ key, name }, sourcePost, false, fetchedPosts)
					break;
					
					case DATE_FIELDS_GROUP:
						group = DATE_FIELDS_GROUP;
					break;

					case TEAM_FIELDS_GROUP:
						role = keyArr.splice(0,1)[0];
						name = keyArr.pop();
						key = keyArr.join(':');
						relatedField = role;
						
						collection = COLLECTIONS.QIUSERS_COLLECTION_NAME;
						docId = (fetchedPosts.find(p=>p.collection===srcCollection)||{})[relatedField];
						sourcePost = fetchedPosts.find(p=>p.collection===collection && p.ID===docId && Array.isArray(p.roles) && p.roles.find(r=>r===role))||{};
						value = getFieldValue({ key, name }, sourcePost, false, fetchedPosts)
					break;
					
					case ADDRESS_FIELDS_GROUP:
						collection = srcCollection;
						sourcePost = fetchedPosts.find(p=>p.collection===collection)||{};
						docId = sourcePost.ID || contentPost.contactId;
						name = field;
						key = shortcodePath;
						value = getFieldValue({ key, name }, sourcePost, false, fetchedPosts)
					break;
					
					case QIUSERS_FIELDS_GROUP:
						collection = COLLECTIONS.QIUSERS_COLLECTION_NAME;
						sourcePost = fetchedPosts.find(p=>p.collection===collection)||{};
						docId = sourcePost.ID || contentPost.contactId;
						name = field;
						key = shortcodePath;
						value = getFieldValue({ key, name }, sourcePost, false, fetchedPosts)
					break;
					
					case CUSTOM_FIELDS_GROUP:
						collection = srcCollection;
						sourcePost = fetchedPosts.find(p=>p.collection===collection)||{};
						docId = sourcePost.ID || contentPost.contactId;
						name = shortcodeField;
						key = shortcodePath;
						value = getFieldValue({ key, name }, sourcePost, false, fetchedPosts)
					break;
					
					default:
						collection = srcCollection;
						sourcePost = fetchedPosts.find(p=>p.collection===collection)||{};
						docId = sourcePost.ID || contentPost.contactId;
						name = keyArr.pop();
						key = keyArr.join(':');
						value = getFieldValue({ key, name }, sourcePost, false, fetchedPosts)
					break;
				}
				
				let momentObj = momentNow();
				
				switch (group) {
					case DATE_FIELDS_GROUP:
						if (shortcodeContent.split(SHORTCODE_PARAMS_SEPARATOR)[1]) {
							let plusTime = shortcodeContent.split(SHORTCODE_PARAMS_SEPARATOR)[1];
							let qty = plusTime.split('_')[0]
							let unity = plusTime.split('_')[1]
							let before_after = plusTime.split('_')[2]
							if (before_after==='before') {
								momentObj = momentObj.subtract(qty,unity)
							}else{
								momentObj = momentObj.add(qty,unity)
							}
						}
						break;
					default:break;
				}

				switch (field) {
					case CONSTANTS.QRCODE_FIELD:
						value =	`<img src="${value}" width="160" height="160">`;
					break;

					case CONSTANTS.SENT_FULLDATE_FIELD:
						value =	momentObj.format('DD/MM/YYYY')
					break;

					case CONSTANTS.SENT_DATE_FIELD:
						value =	momentObj.format('DD')
					break;

					case CONSTANTS.SENT_DAY_FIELD:
						value =	momentObj.format('dddd')
					break;

					case CONSTANTS.SENT_MONTH_FIELD:
						value =	momentObj.format('MMMM')
					break;

					case CONSTANTS.SENT_YEAR_FIELD:
						value =	momentObj.format('YYYY')
					break;
				
                	case CONSTANTS.MARITAL_STATUS_FIELD:
                	case CONSTANTS.GENDER_FIELD:
                	case CONSTANTS.TYPE_FIELD:
						value =	value ? langMessages[`forms.${value}`] || value : ''
					break;
					
                	case CONSTANTS.DATE_FIELD:
					case CONSTANTS.MODIFIED_FIELD:
						value =	value ? moment(value).format(MOMENT_SHORT) : ''
					break;
				
					default:						
					break;
				}

				if ( COLLECTIONS.taxonomies.find(t=>t===name) && value ) {
					
					/* RESET VALUE TO EMPTY STRING  */
					const taxName = name;
					const taxIds = Array.isArray(value) ? value : [value];
                    const taxValues = [];
                    let sourceTaxonomy;
					// ------------------------------
					
					taxIds.forEach(tId=>{
						sourceTaxonomy = ((taxName in fetchedTaxonomies) && fetchedTaxonomies[taxName].find(t=>t.collection===collection && t.ID===tId)||{});
						value = (sourceTaxonomy && sourceTaxonomy.title) || '';
						if (value) taxValues.push(value)
					})

					value = taxValues.join(', ');
					
				}else
				if ( Array.isArray(value) ) {
					
					let tableAttrs = `cellspacing="0" cellpadding="5" width="100%"`;

					let tableStyle = `width: 100%; margin: 10px auto; max-width: 540px;`;
					
					let cellStyle = `
						padding: 6px;
						border: 1px solid #d3d3d3; 
					`;			

					if ( collection === COLLECTIONS.TICKETS_COLLECTION_NAME && name === 'items' && Array.isArray(value) ) {
					
						/* RESET VALUE TO EMPTY STRING  */
						const items = value;
						value = '';
						// ------------------------------
						
						const head = ['product','qty','price'];
						const rows = [];
	
						items.forEach(item => {
							if ( isLoopableObject(item) ) {
								const cells = [];
								head.forEach(subField=>{
									const subVal = item[subField]||'';
									cells.push({ subField, subVal })
								})
								rows.push(cells)
							}
						});
	
						if ( rows.filter(cells=>Array.isArray(cells) && cells.length).length ) {
							
							let html = `<table style="${tableStyle}" ${tableAttrs}>`;

							html += `<tr>${head.map(colName=>`<td style="${cellStyle}">${(i18nLabels[name]||{})[colName]||colName}</td>`)}</tr>`;
							
							html += rows.map(row=>{
								
								let cells = head.map(h=>row.find(c=>c.subField===h)||{})
								
								let cellsMkp = cells.map(cell=>{
	
									const { subField, subVal } = cell;
									let cellValue = subVal;
									let product, itemId;
	
									switch (subField) {
										case 'price':
											cellValue = cellValue ? `$ ${cellValue||''}` : '';
										break;
										
										case 'product':
											itemId = subVal;
											product = fetchedPosts.find(p=>p.collection===COLLECTIONS.PRODUCTS_COLLECTION_NAME && p.id===itemId)||{};							
											cellValue = (product && product.title) || '';
										break;
									
										case 'qty':
										default:
											cellValue = subVal
										break;
									}
	
									return `<td style="${cellStyle}">${cellValue||''}</td>`;
	
								})	
	
								return `<tr>${cellsMkp.map(mkp=>mkp)}</tr>`;
	
							});
							
							html += `</table>`;
		
							value = html;
	
						}
	
					}else{
						
						/* RESET VALUE TO EMPTY STRING  */
						const array = value;
						value = '';
						// ------------------------------
						
						const head = [];
						const rows = [];
	
						array.forEach((item,r) => {
							if ( isLoopableObject(item) ) {
								const cells = [];
								Object.keys(item).forEach((name,i)=>{
									if (r===0) head.push(name)	
									const val = item[name];
									cells.push({ name, val })
								})
								rows.push(cells)
							}	
						});

						if ( rows.filter(cells=>Array.isArray(cells) && cells.length).length ) {
							
							let html = `<table style="${tableStyle}" ${tableAttrs}>`;
							html += `<tr>${head.map(colName=>`<td style="${cellStyle}">${colName}</td>`)}</tr>`;
							html += rows.map(cells=>{
										return `<tr>${cells.map(cell=>`<td style="${cellStyle}">${cell.subVal}</td>`)}</tr>`
									});
							html += `</table>`;
		
							value = html;
	
						}
						
					}
					
				}else
				if ( isLoopableObject(value) ) {
					const obj = value;
					value = '';
				}

				if ( typeof value !== 'string' && typeof value !== 'number' ) {
					value = '';
				}

				if ( isLoopableObject(i18nLabels[name]) && (value in i18nLabels[name])  ) {
				  	value = i18nLabels[name][value]
				}else
				if ( typeof i18nLabels[name] === 'string' ) {
					value = i18nLabels[name]					
				}

				replacements[shortcode] = {
					shortcodeField,
					shortcodePath,
					field,
					keyArr,
					collection,
					key,
					name,
					docId,
					value,
					role,
					relatedField,
					// sourcePost,
				}

				if ( collection && docId ) {

					!requiredCollections[collection] && (requiredCollections[collection]={})
					!requiredCollections[collection][docId] && (requiredCollections[collection][docId]={})
					
					const thisField = requiredCollections[collection][docId];
					requiredCollections[collection][docId] = {
						...thisField,
						fields: [ ...new Set([...thisField.fields||[], field]) ],
						docId,
						role,
					}

				}

				if ( collection && relatedField ) {

					!relatedCollections[collection] && (relatedCollections[collection]={})
					!relatedCollections[collection][relatedField] && (relatedCollections[collection][relatedField]={})
					
					const thisField = relatedCollections[collection][relatedField];
					relatedCollections[collection][relatedField] = {
						...thisField,
						fields: [ ...new Set([...thisField.fields||[], field]) ],
						relatedField,
						role,
					}

				}

				// console.groupCollapsed(`replaceShortCodes > ${shortcodeContent}`)
				// console.log('shortcode',shortcode)
				// console.log('shortcodeField',shortcodeField)
				// console.log('shortcodePath',shortcodePath)
				// console.log('field',field)
				// console.log('keyArr',keyArr)
				// console.log('------------------------------------------------------')
				// console.log('collection',collection)
				// console.log('docId',docId)
				// console.log('key',key)
				// console.log('name',name)
				// console.log('value',value)
				// console.log('role',role)
				// console.log('relatedField',relatedField)
				// console.log('sourcePost',sourcePost)
				// console.groupEnd(`replaceShortCodes > ${shortcodeContent}`)
	
			});

			console.groupCollapsed(`replaceShortCodes`)
			console.log('replacements',replacements);
			console.log('requiredCollections',requiredCollections);
			console.log('relatedCollections',relatedCollections);
			console.groupEnd(`replaceShortCodes`)

			Object.keys(replacements).forEach((shortcode,i)=>{
				const data = replacements[shortcode];
				let value = data.value || '';
				content = replaceAll(shortcode, value, content)
			})

			return resolve({ content, post: contentPost, replacements })
			
		}

		return resolve({ content: '', post: contentPost, replacements })

	})

}

module.exports = {
    replaceShortCodes
}