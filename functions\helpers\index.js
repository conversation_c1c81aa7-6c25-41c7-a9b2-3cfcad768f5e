// const langMessages = require('../../src/lang/locales/pt_BR')
const functions = require('firebase-functions');
const moment = require('moment');
const CONSTANTS = require('../constants');
const ROLES = require('../constants/roles')
const COLLECTIONS = require('../constants/collections');
const { default: Axios } = require('axios');

const {
	APP_ENV,
    MOMENT_SHORT,
	SHORTCODE_PREFIX,
	SHORTCODE_SUFIX,
	SHORTCODE_LABEL_SEPARATOR,
	SHORTCODE_GROUP_SEPARATOR,
    SHORTCODE_PATH_SEPARATOR,
    ADDRESS_FIELDS_GROUP,
    DATE_FIELDS_GROUP,
    CONTRACT_FIELDS_GROUP,
    TICKET_FIELDS_GROUP,
    CUSTOM_FIELDS_GROUP,
    TEAM_FIELDS_GROUP,
} = CONSTANTS;

const { KEYWORDS_SUBCOLLECTIONS_MAP, KEYWORDS_SUBCOLLECTION_NAME, KEYWORDS_FIELDS_MAP } = COLLECTIONS;

// grabHereToCopyPaste
const momentNow = (d) => moment(d).utcOffset(CONSTANTS.MOMENT_BR_OFFSET);

// grabHereToCopyPaste
const nowToISO = () => momentNow().format(CONSTANTS.MOMENT_ISO);

// grabHereToCopyPaste
const sortByDate = ((a, b) => {
    return !(a.date && b.date) ? 0 : (new Date(a.date).getTime() > new Date(b.date).getTime() ? -1 : 1)
})

// grabHereToCopyPaste
const sortByUpdateDate = ((a, b, order, key) => {
    let A = order === 'asc' ? b : a;
    let B = order === 'asc' ? a : b;
    return  A[key] && B[key] ? new Date(B[key]).getTime() - new Date(A[key]).getTime() : (
                A.updatedAt && B.updatedAt ? B.updatedAt - A.updatedAt : (
                    A.modified && B.modified ? new Date(B.modified).getTime() - new Date(A.modified).getTime() : (
                        A.createdAt && B.createdAt ? B.createdAt - A.createdAt : (
                            A.date && B.date ? new Date(B.date).getTime() - new Date(A.date).getTime() : 0
                        )
                    )
                )
            )
})

// grabHereToCopyPaste
const asyncForLoop = (fn, onFinish, length) => {
    onFinish = onFinish||(()=>{});
    (function innerFn(index) {
        if (index===length) {
            onFinish()
            return;
        }
        fn(index,()=>innerFn(index+1))
    })(0)
}

// grabHereToCopyPaste
const paginateArray = (array, pageSize) => {
	pageSize = pageSize||10
	let pages = [];
	let page = [];
	Array.isArray(array) && array.forEach((a,i)=>{
		page.push(a)
		if (page.length===pageSize||i===array.length-1) {
			pages.push(page)
			page = []
		}
	})
	return pages
}

// grabHereToCopyPaste
const extractShortcodeFields = (content) =>{

    const prefix = SHORTCODE_PREFIX;
    const sufix = SHORTCODE_SUFIX;
    const labelSeparator = SHORTCODE_LABEL_SEPARATOR;
    const pathSeparator = SHORTCODE_PATH_SEPARATOR;

    let shortcodeFields = []

    if ( content && typeof content === 'string' ) {
        shortcodeFields = [
            ...new Set(
                content
                .split(prefix)
                .filter(s=>s&&s.indexOf(sufix)!==-1&&s.split(sufix)[0].length)
                .map(s=>s.split(sufix)[0])
                // .map(s=>s.split(sufix)[0].split(labelSeparator)[0])
                // .filter(s=>s&&s.indexOf(pathSeparator)!==-1)
            )
        ]
    }

    return shortcodeFields

}

// grabHereToCopyPaste
const extractShortcodes = (content, shortcodeFields) =>{

    const prefix = SHORTCODE_PREFIX;
    const sufix = SHORTCODE_SUFIX;

    let shortcodes = []

    if ( content && typeof content === 'string' ) {
        shortcodeFields = extractShortcodeFields(content)
    }

    if ( Array.isArray(shortcodeFields) ) {
        shortcodes = shortcodeFields.map(s=>`${prefix}${s}${sufix}`)
    }

    return shortcodes

}

// grabHereToCopyPaste
const extractShortcodePaths = (content, shortcodeFields) =>{

    const pathSeparator = SHORTCODE_PATH_SEPARATOR;

    let shortcodePaths = []

    if ( content && typeof content === 'string' ) {
        shortcodeFields = extractShortcodeFields(content)
    }

    if ( Array.isArray(shortcodeFields) ) {
        shortcodePaths = [...new Set(shortcodeFields.filter(s=>s&&s.indexOf(pathSeparator)!==-1).map(s=>s.split(pathSeparator)[0]))]
    }

    return shortcodePaths

}

// grabHereToCopyPaste
const getShortcodePath = ( name, group ) =>{
    
    let path = '';

    switch (group) {
        case ADDRESS_FIELDS_GROUP:
        case DATE_FIELDS_GROUP:
        case CONTRACT_FIELDS_GROUP:
        case TICKET_FIELDS_GROUP:
        case CUSTOM_FIELDS_GROUP:
        case TEAM_FIELDS_GROUP:
            path = group;
        break;
        default:
            path = '';
        break;
    }

    return path;
}

// grabHereToCopyPaste
const insertPixel = (trackParams, html) => {
	/* 
	| PIXEL QIPLUS
	*/
	html = html||''
	trackParams = trackParams||[]
	
	let pixelUrl = `${CONSTANTS.PIXEL_TRACK_URL}`;

	if ( html.indexOf(CONSTANTS.QIPLUS_PIXEL_MARKUP_START)>=0 ) {
		pixelUrl = html.split(CONSTANTS.QIPLUS_PIXEL_MARKUP_START)[1].split(CONSTANTS.QIPLUS_PIXEL_MARKUP_END)[0];
		html = html.replace(`${CONSTANTS.QIPLUS_PIXEL_MARKUP_START}${pixelUrl}${CONSTANTS.QIPLUS_PIXEL_MARKUP_END}`,'');
		pixelUrl += '/';
	}

	pixelUrl+=trackParams.join('/')
	html+=`${CONSTANTS.QIPLUS_PIXEL_MARKUP_START}${pixelUrl}${CONSTANTS.QIPLUS_PIXEL_MARKUP_END}`;
	return html
}

// grabHereToCopyPaste
const insertSubscriptionLinks = (accountId, recipient, html) => {
	if (html && html.indexOf(CONSTANTS.QIPLUS_SUBSCRIPTION_MARKUP_START)===-1 && html.indexOf(CONSTANTS.SUBSCRIPTION_URL)===-1) {
		html+=`<p style="text-align:center;font-size:11px;">${CONSTANTS.QIPLUS_SUBSCRIPTION_MARKUP_START}&action=unsubscribe&account=${accountId}&recipient=${recipient}${CONSTANTS.QIPLUS_SUBSCRIPTION_MARKUP_END}</p>`;
	}
	return html
}

// grabHereToCopyPaste
const insertTrackingLinks = (trackParams, html) => {
	/* 
	| TRACK QIPLUS
	*/
	html = html||''
	trackParams = trackParams||[]
	
	let trackUrl = `${CONSTANTS.LINK_TRACK_URL}${trackParams.join('/')}${trackParams.length?'/':''}`;
	let hrefStr = 'href='

	if ( html.indexOf(hrefStr)>=0 ) {

		let replacements = {}, result, regex = /href=/g;

		while ( (result = regex.exec(html)) ) {
			let index = result.index;
			let i = index+(hrefStr.length)+1;
			let r = html.substr(i,html.length-index);
			let f = r.split('"')[0].split("'")[0].length
			let hrefAttr = html.substr(i,f)
			let thisTrackUrl = `${trackUrl}?url=${encodeURIComponent(hrefAttr)}`;
			if (hrefAttr.indexOf(CONSTANTS.TRACK_URL)===-1) {
				replacements[hrefAttr] = thisTrackUrl
			}else{
				let newHrefAttr = hrefAttr
				trackParams.forEach((keyval,i) => {
					let key = keyval.split('/')[0];
					let shouldInsert = newHrefAttr.indexOf(keyval)===-1 && newHrefAttr.indexOf(`/${key}/`)===-1;
					let shortParams = CONSTANTS.SHORT_URL_PARAMS
					Object.keys(shortParams).forEach(urlParam=>{
						let shortParam = shortParams[urlParam]
						if (
							((keyval.indexOf(`${urlParam}/`)===0||keyval.indexOf(`${shortParam}/`)===0) && 
							(newHrefAttr.indexOf(`/${urlParam}/`)!==-1||newHrefAttr.indexOf(`/${shortParam}/`)!==-1)) 
						) shouldInsert = false
					})
					if ( shouldInsert ) {
						let newHrefParam = `${keyval}/`;
						CONSTANTS.ALL_TRACK_URL_PARAMS.forEach(trackUrlBase=>{
							newHrefAttr = newHrefAttr.replace(trackUrlBase,`${trackUrlBase}${newHrefParam}`)
						})
					}
				});
				if (hrefAttr!==newHrefAttr) replacements[hrefAttr] = newHrefAttr
			}
		}

		Object.keys(replacements).forEach((href,i)=>{
			let urlParam = 'url=';
			while (html.indexOf(href)>=0 && html.indexOf(`${urlParam}${href}`)!==(html.indexOf(href)-urlParam.length)) {
				html = html.replace(href, replacements[href]);
			}
		})

	}

	return html
}

// grabHereToCopyPaste
const insertTrackingParams = (trackParams, html, replaceParams) => {
	/* 
	| TRACK QIPLUS
	*/
	html = html||''
	trackParams = trackParams||[]
	
	let trackAttributes = ['href=','src=']

	trackAttributes.forEach(trackAttr=>{
		if ( html.indexOf(trackAttr)>=0 ) {
	
			let replacements = {}, result, regex = new RegExp(trackAttr,'g')
	
			while ( (result = regex.exec(html)) ) {
				let index = result.index;
				let i = index+(trackAttr.length)+1;
				let r = html.substr(i,html.length-index);
				let f = r.split('"')[0].split("'")[0].length
				let attr = html.substr(i,f)
				if (attr.indexOf(CONSTANTS.TRACK_URL)!==-1) {
					let newHrefAttr = attr
					trackParams.forEach((keyval,i) => {
						let key = keyval.split('/')[0];
						let shouldInsert = replaceParams===true || (newHrefAttr.indexOf(keyval)===-1 && newHrefAttr.indexOf(`/${key}/`)===-1);
						let shortParams = CONSTANTS.SHORT_URL_PARAMS
						Object.keys(shortParams).forEach(urlParam=>{
							let shortParam = shortParams[urlParam]
							if (
								replaceParams !== true &&
								((keyval.indexOf(`${urlParam}/`)===0||keyval.indexOf(`${shortParam}/`)===0) && 
								(newHrefAttr.indexOf(`/${urlParam}/`)!==-1||newHrefAttr.indexOf(`/${shortParam}/`)!==-1)) 
							) shouldInsert = false
						})
						if ( shouldInsert ) {
							let newHrefParam = `${keyval}/`;
							CONSTANTS.ALL_TRACK_URL_PARAMS.forEach(trackUrlBase=>{
								newHrefAttr = newHrefAttr.replace(trackUrlBase,`${trackUrlBase}${newHrefParam}`)
							})
						}
					});
					if (attr!==newHrefAttr) replacements[attr] = newHrefAttr
				}
			}
	
			Object.keys(replacements).forEach((attr,i)=>{
				while (html.indexOf(attr)>=0 && attr !== replacements[attr]) {
					html = html.replace(attr, replacements[attr]);
				}
			})
	
		}
	})

	return html
}

// grabHereToCopyPaste
const getFetchedValue = (field, data, validate, fetchedData, arrayIndex) => {

    const { fetch_value, fetch_default, default_value, fetch } = field;

    let val;
    
    if ( (fetch_value||fetch_default) && fetchedData && isLoopableObject(fetch) ) {

        const { collection, value } = fetch;

        const items = Array.isArray(fetchedData) ? fetchedData : (
            isLoopableObject(fetchedData) && Array.isArray(fetchedData[collection]) ?
            fetchedData[collection] :
            []
        )

        let itemId = getFieldValue(fetch, data, false, null, arrayIndex);
        
        if ( itemId ) {

            const fetchedItem = items.find(item=>item.ID===itemId)
            
            if ( fetchedItem ) {

                const keysArray = (value||'').split(':');
                const name = keysArray.length === 1 ? keysArray[0] : keysArray.pop();
                const pseudo = { key: keysArray.join(':'), name }
                
                val = getFieldValue(pseudo, fetchedItem, validate, fetchedData, arrayIndex)
                
             } 
        }
        
    }

    return val !== undefined ? val : (
        ('default_value' in field) ? default_value : ""
    );

}

// grabHereToCopyPaste
const filterFields = (field, fields, data, fetchedData) => {

    const { conditional_logic } = field;

    if( Array.isArray(conditional_logic) ) {
        
        // console.groupCollapsed(`filterFields > ${field.label}`);
        // console.log(field.label);
        
        let match = conditional_logic.find(or=>{
            
            return or.length === or.filter(and=>{
                
                let compare = and.value;
                const operator = and.operator;
                
                if ( typeof compare === 'string' && compare.split(':').length > 1 ) {
                    
                    const keyArr = compare.split(':')
                    const targetIndicator = keyArr.splice(0,1)[0];
                    const targetName = keyArr.pop();
                    const targetKey = keyArr.join(':');
                    const targetCollection = and.collection;
                    const targetID = and.ID;
                    const isSelf = targetIndicator === 'self';
                    const targetData = isSelf ? data : fetchedData.find(p=>p.collection===targetCollection && p.ID===targetID);
                    
                    compare = getFieldValue({ name: targetName, key: targetKey }, targetData, false, fetchedData)

                    // console.log('targetName',targetName);
                    // console.log('targetKey',targetKey);
                    // console.log('compare',compare);
                    // console.log('targetData',targetData);

                }
                
                let targetField = fields.find(f=>f.field===and.field) || ({
                    ...and,
                    key: and.key,
                    name: and.field
                })

                const val = getFieldValue(targetField, data, false, fetchedData);

                let valMatch = false;

                switch (operator) {
                    case '==':
                        valMatch = val === compare;
                    break;
                    case '!=':
                        valMatch = val !== compare;
                    break;
                    case '==contains':
                        valMatch = val && val.indexOf && val.indexOf(compare) >= 0;
                    break;
                    case '!=contains':
                        valMatch = !val || (val.indexOf && val.indexOf(compare) === -1);
                    break;
                    case '!=empty':
                        valMatch = val !== 0 && val !== '' && val !== null && val !== undefined && val !== 'undefined';
                    break;
                    case '==empty':
                        valMatch = (val === '' || val === null);
                    break;
                    case '==email':
                        valMatch = Boolean(val) && validateEmail(val);
                    break;                        
                    case '!=email':
                        valMatch = !val || !validateEmail(val);
                    break;                        
                    default:
                    break;
                }

                // console.groupCollapsed(`filterFields > and > ${name}`);
                // console.log('name',name);
                // console.log('val',val);
                // console.log('compare',compare);
                // console.log('valMatch',valMatch);
                // console.groupEnd(`filterFields > and > ${name}`);
                
                return valMatch;

            }).length;

        })
            
        // console.log('field',field);
        // console.log('match',match);
        // console.log('data',(data && jsonClone(data))||data);
        // console.groupEnd(`filterFields > ${field.label}`);
        
        return match;
        
    }
    
    return true;

}

// grabHereToCopyPaste
const sanitizeVal = (val, returnType, defaultVal) => {
    if ( val ) {
        switch (returnType) {
            case 'float':
                val = parseFloat(val);
            break;
            case 'int':
            case 'integer':
                val = parseInt(val,10);
            break;            
            default:
                break;
        }
    }
    return !isNaN(val) ? val : (defaultVal||0)
};

// grabHereToCopyPaste
const calculate = (operator,v,t) => {
    if ( !isNaN(Number(t)) && !isNaN(Number(v)) ) {
        t = Number(t);
        v = Number(v);
        switch (operator) {
            case '+':
                t += v;
            break;
            case '-':
                t -= v;
            break;
            case '*':
                t = t*v;
            break;
            case '/':
                t = t/(v||1);
            break;
            case '%':
                t = t%v;
            break;
            case '+%':
                t = t+(t*(v/100));
            break;
            case '-%':
                t = t-(t*(v/100));
            break;
            default:
            break;
        }
    }
    return t;
}

// grabHereToCopyPaste
const calcFieldValue = (field, data, validate, fetchedData, arrayIndex) => {

    const { name, default_value, return_type, type, calc, min, max } = field;

    let returnType = return_type || 'float';

    let total = 0;

    if ( Array.isArray(calc) ) {

		const calcFields = calc.filter(f=>filterFields(f, calc, data))

        const values = calcFields.map(c=>{
            
            const { name, operator, sub_field, multiplier } = c;

            const fieldValue = getFieldValue(c, data, validate, fetchedData, arrayIndex)
            const subCalc = c.calc;

            let val;

            if ( Array.isArray(fieldValue) ) {

                let subtotal = 0;
                const items = fieldValue;

                items.forEach((item,i) => {
                    if ( sub_field && isLoopableObject(item) && (sub_field in item)) {
                                                
                        let multiplierVal = multiplier ? getFieldValue({ key: `${name}:#`, name: multiplier }, data, validate, fetchedData, i) : 1;
                        let subval = getFieldValue({ key: `${name}:#`, name: sub_field }, data, validate, fetchedData, i)
                        
                        subval = sanitizeVal(subval, returnType, 0);
                        
                        if ( multiplierVal ) {
							subval = calculate('*', subval, multiplierVal);                                                    
						}
                        subtotal = calculate(operator, subval, subtotal);                        
					}
                }); 

                val = subtotal;   
                
            }else{

                if (isLoopableObject(fieldValue) && fieldValue[name]) {
    
                    const subval = sanitizeVal(fieldValue[name], returnType, 0);
                    val = calculate(operator, subval, 0);
                    
                }else{
    
                    val = sanitizeVal(fieldValue, returnType, 0);
                    
                }
                
            }

            total = calculate(operator, val, total);
            
            return val;

        })
        
        if ( validate ) {
            switch (type) {
                case 'percent':
                case 'number':
                case 'currency':
                    
                    total = typeof min === 'number' ? Math.max(min, total) : total;
                    total = typeof max === 'number' ? Math.min(max, total) : total;
                
                    break;
            
                default:
                    break;
            }
        }
        
    }
    
    return !isNaN(total) ? total : (
        ('default_value' in field) ? default_value : 0
    );

}
// grabHereToCopyPaste
const getFieldValue = (field, data, validate, fetchedData, arrayIndex) => {
    
    const { key, name, default_value, type, calc, fetch_value, fetch_default, min, max } = field;
    
    if ( calc ) {
        return calcFieldValue(field, data, validate, fetchedData, arrayIndex)
    }else
    if ( fetch_value || fetch_default ) {
        return getFetchedValue(field, data, validate, fetchedData, arrayIndex)
    }

    const keysArray = (key||'').split(':');
    
    let evalData = jsonClone(data)||{};

    keysArray.forEach((a,c)=>{
        const k = a === '#' ? arrayIndex : a;
        
        if ( k!==undefined && isLoopable(evalData) && ( k in evalData ) ) {
            evalData = evalData[k];
        }
        
    })

    let val = isLoopableObject(evalData) && ( name in evalData ) ? evalData[name] : (
        ('default_value' in field) ? default_value : null
    );

    if ( validate ) {
        switch (type) {
            case 'percent':
            case 'number':
                
                val = typeof min === 'number' ? Math.max(min, val) : val;
                val = typeof max === 'number' ? Math.min(max, val) : val;
    
                if (type === 'percent') {
                    val = Math.min(100, Math.max(0, val));                
                }
            
                break;
        
            default:
                break;
        }
    }

    return val
    
}

// grabHereToCopyPaste
const matchIfNumber = (a, b) => (!isNaN(a) && !isNaN(b) ? Number(a) === Number(b) : a === b);

// grabHereToCopyPaste
const findUndefined = (obj) => {
	if (Array.isArray(obj)) {
		obj.forEach((a, i) => {
			if (a === undefined || typeof a === 'undefined') {
				// console.log('findUndefined >>>> FOUND UNDEFINED -------------------------');
				// console.log('a', a);
			} else
			if ( !isCyclic(a) && isLoopable(a) ) {
				findUndefined(a)
			}
		})
	} else
	if (obj && (typeof obj === "object") && Object.keys(obj) && Object.keys(obj).length) {
		Object.keys(obj).forEach((key) => {
			// console.log('findUndefined > key',key);
			if ( obj[key] === undefined || typeof obj[key] === 'undefined' ) {
				// console.log('findUndefined >>>> FOUND UNDEFINED -------------------------');
				// console.log('key', key);
				// console.log('obj', obj);
			} else
			if (typeof obj[key] === "object" && Object.keys(obj[key]).length) {
				if (!isCyclic(obj[key])) findUndefined(obj[key])
			} else
			if (Array.isArray(obj[key])) {
				if (!isCyclic(obj[key])) findUndefined(obj[key])
			}
		})
	}
}

// grabHereToCopyPaste
const isCyclic = (obj) => {
    var seenObjects = [];
	function detect (obj,level) {
        level = level||0
        var key
        if (isLoopable(obj)) {
            let keys = Object.keys(obj)
            let seenObjectIndex = seenObjects.indexOf(obj);
            if (seenObjectIndex > -1 && level > 1) {
                for (let k=0;k<keys.length;k++) {
                    key = keys[k];
                    if (isLoopable(obj[key])) {
                        console.log('cycle in index ' + seenObjectIndex, {seenObjects});
                        return true;
                    }
                }
            }
            seenObjects.push(obj);
            for (let k=0;k<keys.length;k++) {
                key = keys[k];
                if (detect(obj[key],level+1)) {
                    console.log('cycle at ' + key);
                    return true;
                }
            }
        }
        return false;
	}
	return detect(obj);
}

// grabHereToCopyPaste
const replaceUndefined = (obj, replacement) => {
	if ( isLoopable(obj) ) {
		Object.keys(obj).forEach(key => {
			if ( obj[key] === undefined || typeof obj[key] === 'undefined' ) {
				// console.log('replaceUndefined >>>> FOUND UNDEFINED -------------------------');
				obj[key] = replacement||null;
			} else
			if ( isLoopable(obj[key]) && !isCyclic(obj[key])) {
				obj[key]=replaceUndefined(obj[key], replacement)
			}
		})
	}
	return obj;
}

// grabHereToCopyPaste
const replaceKeysRecursively = (obj,replaceKeys) => {
	if (Array.isArray(obj)) {
		obj.forEach((a, i) => {
			if ( isLoopable(a) && !isCyclic(a) ) replaceKeysRecursively(a)
		})
	} else
	if (obj && (typeof obj === "object") && Object.keys(obj) && Object.keys(obj).length) {
		Object.keys(replaceKeys).forEach((key, i) => {
			// ------------------------------------------------
			// MAGIC HAPPENS HERE
			// ------------------------------------------------
			if (key in obj) (obj[replaceKeys[key]] = obj[key])
			if (key in obj) (delete obj[key])
		})
		Object.keys(obj).forEach((key) => {
			if (typeof obj[key] === "object" && Object.keys(obj[key]).length) {
				if (!isCyclic(obj[key])) replaceKeysRecursively(obj[key])
			} else
			if (Array.isArray(obj[key])) {
				if (!isCyclic(obj[key])) replaceKeysRecursively(obj[key])
			}
		})
	}
}

// grabHereToCopyPaste
const deleteRecursively = (obj,deleteKeys) => {
	if (Array.isArray(obj)) {
		obj.forEach((a, i) => {
			if ( !isCyclic(a) && isLoopable(a) ) {
				deleteRecursively(a,deleteKeys)
			}
		})
	} else
	if (obj && (typeof obj === "object") && Object.keys(obj) && Object.keys(obj).length) {
		deleteKeys.forEach((key, i) => {
			// ------------------------------------------------
			// MAGIC HAPPENS HERE
			// ------------------------------------------------
			if (key in obj) (delete obj[key])
			// if (key in obj) console.log('deleteRecursively > key', key);
		})
		Object.keys(obj).length && Object.keys(obj).forEach((key) => {
			if (typeof obj[key] === "object" && Object.keys(obj[key]).length) {
				if (!isCyclic(obj[key])) deleteRecursively(obj[key],deleteKeys)
			} else
			if (Array.isArray(obj[key])) {
				if (!isCyclic(obj[key])) deleteRecursively(obj[key],deleteKeys)
			}
		})
	}
}

// grabHereToCopyPaste
const sanitizeFieldTypes = (obj, colsMap, recursive) =>{

	!colsMap && (colsMap = {})

	!colsMap.stringKeys && (colsMap.stringKeys = [
		'id',
		'ID',
		'user',
		'users',
		'context_id',
		'operator_id',
		'user_id',
		'author',
		'owner',
		'owners',
		'seller',
		'sellers',
		'manager',
		'managers',
		'promoter',
		'promoters',
		'task_id',
		'task',
		'tasks',
		'store',
		'stores',
		'post_id',
		'trigger_id',
		'funnel',
		'dbid',
		'posts',
		'product',
		'products',
		'event',
		'cpf',
		'cnpj',
		'mobile',
		'mobileCC',
		'phone',
		'phoneCC',
		'event_id',
		'i_id',		
	]);

	!colsMap.integersKeys && (colsMap.integersKeys = ['createdAt','updatedAt','pts','pts_gained','score','score_gained']);

	!colsMap.booleanKeys && (colsMap.booleanKeys = []);

	!colsMap.numOnlyKeys && (colsMap.numOnlyKeys = ['cpf','cnpj']);

	if ( recursive===true && Array.isArray(obj) ) {
		obj.forEach(c => {
			if ( isLoopable(c) && !isCyclic(c) ) sanitizeFieldTypes(c,colsMap,recursive)
		})
	} else
	if ( isLoopableObject(obj) ) {
		
		colsMap.integersKeys.forEach((key, i) => {
			// ------------------------------------------------
			// MAGIC HAPPENS HERE
			// ------------------------------------------------
			if ( key in obj ) {
				var val = obj[key];
				if ( isLoopable(val) ) {
					Object.keys(val).forEach(k=>{ 
						if ( typeof obj[key][k]==='string' ) 
						obj[key][k] = parseInt(obj[key][k], 10) 
					})
				}else{
					if ( typeof obj[key]==='string' )
					obj[key] = parseInt(obj[key], 10);
				}
			}
			// ------------------------------------------------
		})

		colsMap.booleanKeys.forEach((key, i) => {
			// ------------------------------------------------
			// MAGIC HAPPENS HERE
			// ------------------------------------------------
			if ( key in obj ) {
				var val = obj[key];
				if ( isLoopable(val) ) {
					Object.keys(val).forEach(k=>{ obj[key][k] = convertToBoolean(val[k]) })
				}else{
					obj[key] = convertToBoolean(obj[key])
				}
			}
			// ------------------------------------------------
		})
		
		colsMap.numOnlyKeys.forEach((key, i) => {
			// ------------------------------------------------
			// MAGIC HAPPENS HERE
			// ------------------------------------------------
			if ( key in obj ) {
				var val = obj[key];
				if ( isLoopable(val) ) {
					Object.keys(val).forEach(k=>{ obj[key][k] = convertTonumOnly(obj[key][k]) })
				}else{
					obj[key] = convertTonumOnly(obj[key])
				}
			}
			// ------------------------------------------------
		})
		
		colsMap.stringKeys.forEach((key, i) => {
			// ------------------------------------------------
			// MAGIC HAPPENS HERE
			// ------------------------------------------------
			if ( key in obj ) {
				var val = obj[key];
				if ( typeof val==='number' ) obj[key] = val.toString()
				if ( Array.isArray(val) ) val.forEach((v,i) => (typeof v==='number') && (obj[key][i] = v.toString()) )
				// if (typeof val === 'number') console.log('sanitizeFieldTypes > key', key);
			}
			// ------------------------------------------------
		})
		
		if ( recursive === true ) {
			Object.keys(obj).forEach((key) => {
				if (!isCyclic(obj[key]) && isLoopable(obj[key])) {
					sanitizeFieldTypes(obj[key],colsMap,recursive)
				}
			})
		}
	}

	return obj
}

// grabHereToCopyPaste
const isEmptyObject = obj => {
    return Boolean(obj) && !Array.isArray(obj) && (typeof obj === "object") && !Object.keys(obj).length;
}

// grabHereToCopyPaste
const isLoopableObject = val => {
	return (Boolean(val) && !Array.isArray(val) && (typeof val === "object") && Object.keys(val) && Object.keys(val).length)
};

// grabHereToCopyPaste
const isLoopable = val => {
	return ( Array.isArray(val) || isLoopableObject(val) ) 
}

// grabHereToCopyPaste
const isNumeric = value => {
    //Validate Number values
	if (hasStr(value,',')) {
        try {
            let pieces = value.toString().split(',')
            if ( pieces.length===2 && ( value.toString().indexOf('.')===-1 || value.toString().indexOf(',') > value.toString().indexOf('.') ) ) {
                value = pieces[0].replaceAll('.','')+'.'+pieces[1];
            }else
            if( value.toString().split('.').length===2 && value.toString().indexOf('.') > value.toString().indexOf(',') ) {
                value = value.replaceAll(',','');
            }
        } catch (err) {
			console.log('isNumeric > err',err);
		}
    }
	return (["bigint","number","string"].includes(typeof value)) && !isNaN(Number(value));
}

// grabHereToCopyPaste
const numberFormat = (number, decimals, dec_point, thousands_sep) => {
	number = (String(number||'')).replace(/[^0-9+\-Ee.]/g, '');
	var n = !isFinite(Number(number)) ? 0 : Number(number),
	prec = !isFinite(Number(decimals)) ? 0 : Math.abs(decimals),
	sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
	dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
	s = '',
	toFixedFix = function(n, prec) {
		var k = Math.pow(10, prec);
		return String((Math.round(n * k) / k).toFixed(prec));
	};
    // Fix for IE parseFloat(0.55).toFixed(0) = 0;
    s = (prec?toFixedFix(n,prec):String(Math.round(n))).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec-s[1].length+1).join('0');
    }
    return s.join(dec);
}

// grabHereToCopyPaste
const convertTonumOnly = val => {
	val = typeof val === 'number' ? val : ( typeof val === 'string' ? val.replace(/[^\d]/g, '') : val );
	return val;
}

// grabHereToCopyPaste
const convertToBoolean = (val) => {
	val = ((typeof val === 'string') && !isNaN(Number(val)) && val !== '') ? Number(val) : val;
	val = (typeof val === 'number') ? (val === 0 ? false : (val === 1 ? true : val)) : val;
	return Boolean(val);
}

// grabHereToCopyPaste
const numericBoolean = val => (convertToBoolean(val) ? 1 : 0)

// grabHereToCopyPaste
const capitalizeStr = (s) => {
	if (typeof s !== 'string') return s
	return s.charAt(0).toUpperCase() + s.slice(1)
}

let mapOrphansTimeout;
// grabHereToCopyPaste
const mapOrphans = (children,collection,callbackFn) => {
    if (typeof children === "object" && Object.keys(children).length) {
        clearTimeout(mapOrphansTimeout)
        mapOrphansTimeout = setTimeout(() => {
        
            const orphans = [];
            const owners = {};
            const stores = {};
            const storesCount = {};
            const ownersCount = {};
    
            Object.keys(children).forEach(id => {
                const child = children[id];
                Array.isArray(child.stores) && child.stores.forEach(storeId => {
                    !stores[storeId] && (stores[storeId] = [])
                    stores[storeId].push(id)
                })
                if (child.owner) {
                    !owners[child.owner] && (owners[child.owner] = [])
                    child.owner && owners[child.owner].push(id)
                } else {
                    orphans.push(id)
                }
            })
            
            Object.keys(stores).length && Object.keys(stores).forEach(storeId => {
                storesCount[storeId] = stores[storeId].length;
            })
            
            Object.keys(owners).length && Object.keys(owners).forEach(ownerId => {
                ownersCount[ownerId] = owners[ownerId].length;
            })

            orphans.length && (console.error(`${(collection && (collection + "'s ")) || ''}orphans`, orphans))

			callbackFn && callbackFn(orphans)

        }, CONSTANTS.timer_10_Seconds)
    }
}

// grabHereToCopyPaste
const QR_STRING = 'user_id=[USER_ID]&user_login=[USER_LOGIN]&first_name=[FIRST_NAME]&last_name=[LAST_NAME]';

// grabHereToCopyPaste
const getQRString = (userID = '', userEmail = '', firstName = '', lastName = '') => {

	let qrString = QR_STRING;

	const searchArr = ['[USER_ID]', '[USER_LOGIN]', '[FIRST_NAME]', '[LAST_NAME]'];
	const replaceArr = [userID, userEmail, firstName, lastName];
	
	searchArr.forEach((str,i)=>qrString=qrString.replace(str,replaceArr[i]))

	return qrString;

}

// grabHereToCopyPaste
const createQRCode = (userData) => {
	
	const qrString = getQRString(userData.ID, userData.email, userData.firstName, userData.lastName);
	const urlString = encodeURIComponent(qrString);
	const qrCodeUri = `https://chart.googleapis.com/chart?cht=qr&chl=${urlString}&chs=300x300&chld=L|0`;
	
	return qrCodeUri;
	
}

// grabHereToCopyPaste
const toSearchKeyword = (name, minLen, maxLen, sep) => {
    const k = createKeywords(name, minLen||1, maxLen, sep);
	return k.pop();
}

// grabHereToCopyPaste
const createKeywords = (name, minLen, maxLen, sep) => {
    minLen = minLen||2
    maxLen = maxLen||100
    const keywords = [];
	let curName = '';
	name = name && name.toString ? name.toString() : '';
	name.split(sep||'').forEach((f,i) => {
		const fragment = ((f && f.toLowerCase()) || '')
		if (fragment) {
            curName += fragment;
			i>=(minLen-1) && keywords.push(removeSpaces(curName));
		}
	});
	return keywords.filter(k=>k.length<maxLen);
}

// grabHereToCopyPaste
const generateKeywords = (data, minLen, maxLen, substrKeys, substrLimit, minSubstrLen, sep) => {
    let keywords = [];
    substrLimit = Math.min(50,substrLimit||50);
    substrKeys = Array.isArray(substrKeys)?substrKeys:[];
    Object.keys(data).forEach((key,i)=>{
        let val = data[key];
        if ( ["number","string"].includes(typeof val) && val.toString ) {
            let k = createKeywords(val, minLen, maxLen, sep);
            if ( val.toString().length < substrLimit && substrKeys.includes(key) ) {
                k = generateSubstrKeywords(k,minSubstrLen);
            }
            keywords = keywords.concat(k);
		}
    })
	return [...new Set(keywords)];
}

// grabHereToCopyPaste
const createLongTextKeywords = (text, minLen, maxLen) => {
    minLen = minLen||2
    maxLen = maxLen||100
	text = text.toString ? text.toString() : '';
    let keywords = [];
	text.split(' ').forEach((w,i) => {
		const word = ((w && w.length>=minLen && w.toLowerCase()) || '')
		if (word) { 
            keywords = [...new Set([ ...keywords, ...createKeywords(word, minLen, maxLen)])]

		}
    })
    return [...new Set(keywords)].filter(k=>k.length<maxLen);
}

// grabHereToCopyPaste
const generateLongTextKeywords = (data, minLen, sep) => {
    let keywords = [];
    Object.keys(data).forEach((key,i)=>{
        let val = data[key];
        if ( ["number","string"].includes(typeof val) && val.toString ) {
            let k = createLongTextKeywords(val,minLen,sep);
            keywords = keywords.concat(k);
		}
    })
	return [...new Set(keywords)];
}

// grabHereToCopyPaste
const generateSubstrKeywords = (keywords, minSubstrLen) => {
    minSubstrLen = Math.max(2,minSubstrLen||2)
    let newkeywords = jsonClone(keywords);
    keywords.forEach(k=>{
        if (k.length > minSubstrLen) {
            let p = 0;
            do {
                p++;
                const substr = k.substr(p,k.length);
                newkeywords.push(substr)
            } while (p<k.length-minSubstrLen);
        }
    })
	return [...new Set(newkeywords)];
}

// grabHereToCopyPaste
const generateKeywordsDocs = keywordsSource => {

    const { KEYWORDS_FIELDS_MAP } = COLLECTIONS;

	let post = keywordsSource;
	let docId = post.ID;
	let { collection, owner, accountId } = post;

	if ( !KEYWORDS_FIELDS_MAP[collection] ) {
		console.log('No KEYWORDS_FIELDS_MAP', {collection, docId, owner, accountId})	
		return [];
	}

	const { allKeywordFields, inheritedFields, joinFields, limitedFields } = KEYWORDS_FIELDS_MAP[collection]

	const keywordDocs = [];
	
	const inheritedData = {
		// docRef,
		docId,
		owner,
		accountId
	};

	joinFields.forEach(field=>{
		if (typeof keywordsSource[field]!=="undefined") 
		inheritedData[field] = keywordsSource[field];
	})

	inheritedFields.forEach(field=>{
		if (typeof keywordsSource[field]!=="undefined") 
		inheritedData[field] = keywordsSource[field];
	})

	const relationalFields = CONSTANTS.RELATIONAL_FIELDS.filter(k=>{
		return (k in keywordsSource) && ["number","string"].includes(typeof keywordsSource[k])
	});
	
	relationalFields.forEach(field=>{
		let value = keywordsSource[field];
		let keywords = [value]
		keywordDocs.push({
			...inheritedData,
			field,
			value,
			[field]: value,
			[CONSTANTS.KEYWORDS_FIELD]: keywords,
			index: field,
		})
	})

	limitedFields
	.filter(k=>(k in keywordsSource) && (Array.isArray(keywordsSource[k]) || (["number","string"].includes(typeof keywordsSource[k]))))
	.forEach(field=>{
		let value = keywordsSource[field];
		let keywords = Array.isArray(value) ? value : [value]
		keywordDocs.push({
			...inheritedData,
			field,
			value,
			[field]: value,
			[CONSTANTS.KEYWORDS_FIELD]: keywords,
			index: field,
		})
	})

	allKeywordFields
	.filter(k=>(k in keywordsSource) && ["number","string"].includes(typeof keywordsSource[k]))
	.forEach(field=>{

		let value = keywordsSource[field];
		let keywords = (field==='description' || value.toString().split(' ').length > 10) ? 
			generateLongTextKeywords({ [field]: value }) : 
			generateKeywords({ [field]: value }, 2, 100, [field]);
		if ( utf8BytesSize(keywords) > CONSTANTS.FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE) {
			keywords = []
		}

		switch (field) {
			case 'cpf': case 'cnpj': case 'mobile': case 'phone': case 'postalCode':
				keywords = [...new Set([ ...keywords, ...keywords.map(k=>`${convertTonumOnly(k)}`)])];
			break;
			default:break;
		}
		
		keywordDocs.push({
			...inheritedData,
			field,
			value,
			[field]: value,
			[CONSTANTS.KEYWORDS_FIELD]: keywords,
			index: field,
		})
	})
    
    if ( isLoopableObject(post.customFields) ) {
		Object.keys(post.customFields)
        .filter(c=> {
			return Array.isArray(post.customFields[c]) ||
            (["number","string"].includes(typeof post.customFields[c]) && 
            Boolean(post.customFields[c].toString()))
        })
		.forEach((c,i)=>{
			let field = c; 
            let value = post.customFields[c];
            
            let keywords = Array.isArray(value) ? value : 
                (value && value.toString && value.toString().split(' ').length > 10) ? 
                generateLongTextKeywords({ [field]: value }) : 
                generateKeywords({ [field]: value }, 2, 100, [field]);

            if ( utf8BytesSize(keywords) > CONSTANTS.FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE) {
                keywords = []
            }
            
			keywordDocs.push({
				...inheritedData,
				group: CONSTANTS.CUSTOM_FIELDS_GROUP,
				field,
				value,
				[field]: value,
				[CONSTANTS.KEYWORDS_FIELD]: keywords,
				index: `${c.replace(`${CONSTANTS.CUSTOM_FIELDS_PATH}:`,`${CONSTANTS.CUSTOM_FIELDS_PATH}_`)}`,
			})
		})
	}

	if ( Array.isArray(post.tags) && post.tags.length ) {
		post.tags.filter(c=>Boolean(c))
		.forEach((t,i)=>{
			let field = 'tags'; 
			let value = t;
			let keywords = [value]
			keywordDocs.push({
				...inheritedData,
				field,
				value,
				[field]: value,
				[CONSTANTS.KEYWORDS_FIELD]: keywords,
				index: `${field}_${i}`,
			})
		})
	}

	if ( Array.isArray(post.comments) && post.comments.length ) {
		post.comments.filter(c=>Boolean(c.content))
		.forEach((c,i)=>{
			let field = 'comments'; 
			let value = c.content;
			let keywords = generateLongTextKeywords({ [field]: value });
			if (utf8BytesSize(keywords) > CONSTANTS.FIRESTORE_SAFE_DOCUMENT_FIELD_SIZE) {
				keywords = []
			}
			keywordDocs.push({
				...c,
				...inheritedData,
				field,
				value,
				[field]: value,
				[CONSTANTS.KEYWORDS_FIELD]: keywords,
				index: `${field}_${i}`,
			})
		})
	}
	
	return keywordDocs;

}

// grabHereToCopyPaste
function FirestoreBatchHandler(workRef, options, intents) {
	
	options = options||{}
	intents = intents||0

	let { limit, onTransaction, verbose, showHidden, depth } = options

	let refBatch = workRef.batch()
	let writeInterval = options.writeInterval||180

	this.transactionLimit = limit||500
	this.writeLimit = Math.max(this.transactionLimit,options.writeLimit||5000)

	this.commited = false
	this.finished = false
	this.counter = 0;
	this.partialCounter = options.partialCounter||0;
	this.totalCounter = 0;
	this.timeCount = 0;
	this.timerInterval = null;
	this.stack = [];
	this.handlerBatches = []
	this.pending=[]
	this.waiting=Boolean(options.waiting)

	this.transactions = {
		set: 0,
		update: 0,
		delete: 0,
		commits: 0,
		all: [],
	}

	let transactions = JSON.parse(JSON.stringify(this.transactions));
	
	this.log=(data)=>{
		if (verbose===false) return this 
		if (typeof log!=='function') var log='';
		let logger = (typeof log==='function') ? (obj)=>log(obj,showHidden,depth) : console.log;
		logger(data)
		return this
	}

	this.logTx=(tx,other)=>{
		if (verbose===false) return this 
		let res = {...other||{}}
		Object.keys(tx).forEach((k,i)=>{
			let l = Array.isArray(tx[k]) ? tx[k].length : tx[k];
			l && (res[k]=l)
		})
		this.log(res)
		return this
	}

	this.delete=(docRef,data,txCb)=>{
		this.onExecute('delete',docRef,data,txCb)
		return this
	}
	this.update=(docRef,data,txCb)=>{
		this.onExecute('update',docRef,data,txCb)
		return this
	}
	this.set=(docRef,data,txCb)=>{
		this.onExecute('set',docRef,data,txCb)
		return this
	}

	this.startTimer=(int)=>{
		this.timerInterval=setInterval(() => {
			this.timeCount++
			if (this.timeCount%30===0) this.log(`FirestoreBatchHandler: waiting at ${intents>0?`atempt ${intents}`:''} ${this.timeCount}/${writeInterval}s`)
			if (this.timeCount===writeInterval) this.timerEnd()
		}, int||1000 );
		return this
	}

	this.timer=()=>{
		this.log(`FirestoreBatchHandler: waiting at ${this.totalCounter} transactions`);
		this.waiting=true
		this.startTimer()
		return this
	}
	
	this.timerEnd=()=>{
		this.log(`FirestoreBatchHandler: timerEnd: restarted at ${this.totalCounter} transactions`);
		clearInterval(this.timerInterval)

		this.timeCount=0
		this.waiting=false
		
		this.pending.forEach(({ transaction, docRef, data, txCb }, i)=>{
			let r = !this.waiting && this.onExecute(transaction, docRef, data, txCb)
			if (r) this.pending[i] = { executed: true }
		})

		this.pending = this.pending.filter(p=>!p.executed)
		if ( this.commited && this.pending.length <= this.writeLimit ) {
			this.commit(this.onFinish,true)
		}
		return this
	}

	this.onExecute=(transaction,docRef,data,txCb)=>{
		txCb=txCb||onTransaction
		if (this.waiting) {
			this.pending.push({ transaction, docRef, data, txCb, executed: false })
			return false
		}
		if (this.partialCounter>=this.writeLimit) {
			this.partialCounter=0
			this.pending.push({ transaction, docRef, data, txCb, executed: false })
			this.timer()
			return false
		}
		switch (transaction) {
			case 'delete':
				refBatch.delete(docRef)
			break;
			case 'update':
				refBatch.update(docRef,data)
			break;
			case 'set':
				refBatch.set(docRef,data)
			break;
			default: break;
		}
		this.counter++
		this.totalCounter++
		this.partialCounter++
		this.transactions[transaction]++
		this.transactions.all.push({ transaction, docRef, data:data||null, txCb, index:this.transactions.commits })
		txCb && this.stack.push(()=>{ txCb(data) })
		
		if (this.counter>=this.transactionLimit) {
			this.partialCommit(transaction,docRef,data,txCb)
		}
		return true
	}
	
	this.handleCommitError=(err, commitIndex, onHandlerFinish, txArgs) => {
		const { transaction, docRef, data, txCb } = txArgs||{};
		let canFix = false
		let fixMsg = { msg: 'An error occurred. Trying to fix...', code: err.code }
		let commitTransactions = []
		let knownErrors = [
			'decrease transaction size',
			'transaction too big',
			'payload size exceeds',
			'transaction or write too big',
		]
		switch (err.code) {
			case 10:
				this.log('FirestoreBatchHandler: errCode 10 -Too much contention on these documents > toString >'+err.toString());
				if (txArgs && transaction) {
					this.pending.push({ transaction, docRef, data, txCb, executed: false })
					this.timer()
				}
			break;
			case 3:
			case 'invalid-argument':
				if ( err.toString && knownErrors.find(e=>err.toString().toLowerCase().indexOf(e)>-1) ) {
					canFix = this.transactionLimit > 0 && intents < 10
					if (canFix) {

						commitTransactions=this.transactions.all.filter((t)=>{
							return t.index === commitIndex
						})
						let reducedLimit = Math.floor(this.transactionLimit/2)
						let tempBatch = new FirestoreBatchHandler(workRef, {
							...options,
							waiting: this.waiting,
							partialCounter: this.partialCounter,
							limit: reducedLimit
						}, (intents+1))

						let txCount = {}
						commitTransactions
						.forEach(t=>{txCount[t.transaction]=(txCount[t.transaction]||0)+1});

						this.log({ 
							...fixMsg, 
							msg: `Trying to fix by decreasing payload size by half`,
							handler: `Using error handler at commit #${commitIndex+1}`, 
							limit: reducedLimit, 
							intents: (intents+1), 
							transactions: txCount 
						})

						commitTransactions
						.forEach(t=>{
							let { transaction, docRef, data } = t
							tempBatch[transaction](docRef,data)
						});	

						tempBatch.commit(onHandlerFinish)
						this.handlerBatches.push(tempBatch)
					}
				}
			break;
			default:break;
		}
		if (!canFix) {
			if (err.toString && err.toString()) {
				this.log('FirestoreBatchHandler: err: toString >'+err.toString())
			}else
			this.log(err)
		}
		return this
	}

	this.onCommit=(isFinalCommit, onFinish)=>{
		// console.log("--onCommit",this.counter,this.totalCounter);
		let stackLen = this.stack.length
		this.stack.forEach((cb,i)=>cb())
		/* In case any of the callbacks incremented the stack */
		if (this.stack.length>stackLen) {
			for (let i = stackLen; i < this.stack.length; i++) {
				this.stack[i]()
			}
		}
		this.stack = []
		/* In case any callbacks added new transactions */
		if ( isFinalCommit ){
			
			this.log(`FirestoreBatchHandler: final commit executed with ${this.totalCounter} transactions ${intents>0?'in error handler context':''}`)
			onFinish && onFinish(this.transactions, this.totalCounter)
			this.finished = true
			
			// this.reset();
		}
		return this
	}

	this.partialCommit=(transaction,docRef,data,txCb) => {
		// console.log("--partialCommit",this.counter,this.totalCounter);
		let txArgs = { transaction, docRef, data, txCb };
		let commitIndex = this.transactions.commits

		this.counter=0;
		this.transactions.commits++

		refBatch.commit()
		.then(()=>{
			return this.onCommit()
		}).catch(err=>{
			this.handleCommitError(err,commitIndex,(handlerTx)=>{
				this.logTx(handlerTx,{partial:true});
				this.onCommit()
				if (this.commited && !this.pending.length && 
					this.handlerBatches.filter(b=>b.finished).length 
					===this.handlerBatches.length
				) {
					this.onFinish && this.onFinish()
				}
			}, txArgs)
		})
		refBatch = workRef.batch()
		this.log(`FirestoreBatchHandler: partial batch commited at ${this.totalCounter} transactions ${intents>0?'in error handler context':''}`);
		return this
	}
	
	this.commit=(onFinish,innerCommit)=>{
		// console.log("--commit", this.counter, this.totalCounter, this.pending.length, this.handlerBatches.length );
		this.commited = true

		let commitIndex = this.transactions.commits
		let pendingBatches = this.handlerBatches.filter(b=>b.waiting)

		let end=()=>{
			let isFinalCommit = true
			this.onCommit(isFinalCommit, onFinish)
			return true
		}
		
		if (this.waiting) {
			this.log(`FirestoreBatchHandler: waiting: can't commit yet at ${this.totalCounter} transactions`);
			this.onFinish=end
			return false
		}

		if (pendingBatches.length) {
			this.log(`FirestoreBatchHandler: waiting: can't commit yet at ${this.totalCounter} transactions: ${pendingBatches.length} error handlers still active`);
			this.onFinish=end
			return false
		}

		if (!this.counter) {
			return end()
		}

		this.counter=0
		this.transactions.commits++
		
		refBatch.commit()
		.then(()=>{
			return end()
		}).catch(err=>{
			this.handleCommitError(err,commitIndex,(handlerTx)=>{
				this.logTx(handlerTx,{final:true});
				return end()
			})
		})
		refBatch = workRef.batch()
		
		return true
	}

	this.reset = (keys,hard) => {
		// console.log("--RESET")
		if (this.waiting) {
			this.stack.push(()=>{ this.reset(keys,hard) })
			return this
		}
		keys = keys||Object.keys(transactions)
		hard = hard===true
		Object.keys(transactions).forEach(key=>{
			if (keys.includes(key)) {
				this.transactions[key] = JSON.parse(JSON.stringify(transactions[key]));
			}
		})
		if (hard) {
			this.counter = 0;
			this.totalCounter = 0;
			this.stack = [];
			refBatch = workRef.batch()
		}
		return this
	}

	this.init=()=>{
		this.log(`FirestoreBatchHandler started @ ${new Date().toLocaleString()}`)
		if (this.waiting) this.startTimer()
		return this
	}

	this.init()

}

// grabHereToCopyPaste
let objectDebugger = (obj,keys,ignores) => {
	let o = {}
	if (isLoopable(obj) && isLoopable(keys)) {
		Object.keys(keys).forEach((k,i)=>{
			let key = keys[k];
			if (key in obj) o[key] = obj[key];
		})
		return o;
	}else
	if (isLoopable(obj) && Array.isArray(ignores)) {
		Object.keys(obj).forEach((key)=>{
			if (!ignores.includes(key)) o[key] = obj[key];
		})
		return o;
	}
	return obj;
}

// grabHereToCopyPaste
const sTrim = (string, charToRemove, returnFalse) => {
	string = stringfy(string);
	charToRemove = charToRemove || " ";
	var iniLength = string.length;
	returnFalse = returnFalse === true;	
	if (!Array.isArray(charToRemove)) charToRemove = [charToRemove];
	for (var i = charToRemove.length - 1; i >= 0; i--) {	
		if (Array.isArray(charToRemove[i]) ) {	
			if (charToRemove[i].length === 2) {	
				if (
					(string.match(new RegExp(charToRemove[i][0], 'g')) && !string.match(new RegExp(charToRemove[i][1], 'g')))
					||
					(string.match(new RegExp(charToRemove[i][1], 'g')) && !string.match(new RegExp(charToRemove[i][0], 'g')))
				) {	
					if (returnFalse) return false;
				} else
				if (string.match(new RegExp(charToRemove[i][0], 'g')) && string.match(new RegExp(charToRemove[i][1], 'g'))) {
					if (string.match(new RegExp(charToRemove[i][0], 'g')).length !== string.match(new RegExp(charToRemove[i][1], 'g')).length) {	
						if (returnFalse) return false;	
					} else
					if (!returnFalse) {	
						while (string.charAt(0).match(new RegExp(charToRemove[i][0])) && string.charAt(string.length - 1).match(new RegExp(charToRemove[i][1]))) {
							string = string.substring(1);
							string = string.substring(0, string.length - 1);
						}	
					}	
				}
			}	
			charToRemove.splice(i, 1);	
		}
	}	
	string = lTrim(string, charToRemove, returnFalse)
	if (string) string = rTrim(string, charToRemove, returnFalse);	
	return string;	
}

// grabHereToCopyPaste
const lTrim = (string, charToRemove, returnFalse) => {
	string = stringfy(string);
	charToRemove = charToRemove || " ";
	var iniLength = string.length;
	returnFalse = returnFalse === true;
	if (charToRemove === 'NaN') {
		while (Array.isArray(string.charAt(0).match(/[^\d]/g))) {
			string = string.substring(1);
		}
	} else {
		if (!Array.isArray(charToRemove)) charToRemove = [charToRemove];
		while (arrayHasVal(charToRemove, string.charAt(0))) {
			string = string.substring(1);
		}
	}
	if (returnFalse && string.length !== iniLength) {
		return false;
	} else {
		return string;
	}
}

// grabHereToCopyPaste
const rTrim = (string, charToRemove, returnFalse) => {
	string = stringfy(string);
	charToRemove = charToRemove || " ";
	var iniLength = string.length;
	returnFalse = returnFalse === true;
	if (charToRemove === 'NaN') {
		while (Array.isArray(string.charAt(string.length - 1).match(/[^\d]/g))) {
			string = string.substring(0, string.length - 1);
		}
	} else {
		if (!Array.isArray(charToRemove)) charToRemove = [charToRemove];
		while (arrayHasVal(charToRemove, string.charAt(string.length - 1))) {
			string = string.substring(0, string.length - 1);
		}
	}
	if (returnFalse && string.length !== iniLength) {
		return false;
	} else {
		return string;
	}
}

// grabHereToCopyPaste
const utf8BytesSize = str => {
    // Matches only the 10.. bytes that are non-initial characters in a multi-byte sequence.
	if (Array.isArray(str)) str = str.toString()
	if (typeof str==="number" && str.toString) str = str.toString();
	if (typeof str!=="string") return 0;
	try {
		var m = encodeURIComponent(str).match(/%[89ABab]/g);
		return str.length + (m ? m.length : 0);
	} catch (error) {
		return str.length
	}
}

// grabHereToCopyPaste
const removeSpaces = (string) => (string).replace(/ /g,'')

// grabHereToCopyPaste
const jsonClone = (obj) => {
    if (!obj || typeof obj !== 'object') return obj
    try {
        var clone = JSON.parse(JSON.stringify(obj));
        Object.keys(clone).forEach((key, i, keys)=>{
            if (typeof clone[key] === 'function') {
                delete clone[key];
            }
        });
        return clone;
    } catch (err) {
        return obj;        
    }
};

// grabHereToCopyPaste
function isDefined(variable, acceptNull, acceptEmpty, acceptZero, acceptNaN) {

    if (acceptNull===true && variable === null) return true;
    if (acceptEmpty===true && variable === '') return true;
    if (acceptZero===true && variable === 0) return true;

    if (isNaN(variable) && typeof variable==='number' && acceptNaN!==true) return false // NaN returns number from typeof

    return (variable!==undefined && variable!==null && variable!=='' && variable!==0);

}

// grabHereToCopyPaste
const stringfy = (target, separator, returnEmptyifNotValid) => {
	let val;
	if (target === null || target === undefined) {
		val = "";
	} else 
	if (Array.isArray(target)) {
		if (!isDefined(separator)) separator = ',';
		val = target.join(separator);
	} else
	if (typeof target === 'object' && Boolean(target)) {
		val = JSON.stringify(target);
	} else {
		try {
			val = String(target);
		} catch (err) {
			console.log(err);
			if (returnEmptyifNotValid === true) {
				val = '';
			}else return target
		}
	}
	return val;
}

// grabHereToCopyPaste
const hasStr = (string, subString, matchAll) => {

    if (!isDefined(subString) || !isDefined(string)) return false;

    matchAll = matchAll === true;
    string = stringfy(string);

    if (!Array.isArray(subString)) {
        subString = subString.split('[SEP]');
    }

    var strCheck = '';

    for (var i = 0; i < subString.length; i++) {

        var thisSubString = stringfy(subString[i]);

        if (isDefined(thisSubString) && string.indexOf(thisSubString) >= 0) {
            if (!matchAll) return true;
            strCheck += 'true';
        } else {
            if (matchAll) return false;
            strCheck += 'false';
        }

    }

    return (strCheck.indexOf('true') >= 0);

}

// grabHereToCopyPaste
const arrayHasVal = (array, value, exactMatch) => {
    if ( !isDefined(value, true, false) || !Array.isArray(array) ) { return false; }
	exactMatch = exactMatch !== false;
	var returnVal = false;
	array.forEach(child=>{
		if ( typeof child === 'object' && Object.keys(child).length ) {
			Object.keys(child).forEach(key=>{
				if ( (exactMatch && child[key] === value) || (!exactMatch && hasStr(child[key], value)) ) {
					returnVal = true;
				}
			})
		} else
		if ((exactMatch && child === value) || (!exactMatch && hasStr(child, value))) {
			returnVal = true;
		}
	})	
	return returnVal;
}

// grabHereToCopyPaste
const escapeRegExp = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
}

// grabHereToCopyPaste
const replaceAll = function (searchStr, replaceStr, contentStr) {
    if (isDefined(searchStr, true, true) && isDefined(replaceStr, true, true)) {
        let newStr  = contentStr;
        let searchArr = Array.isArray(searchStr) ? searchStr : [searchStr];
        let replaceArr = Array.isArray(replaceStr) ? replaceStr : [replaceStr];
        if (replaceArr.length < searchArr.length) {
            for (let i = 0; i < searchArr.length; i++) {
                replaceArr[i] = replaceArr.length === 1 ? replaceArr[0] : (
                    i>replaceArr.length-1 ? '' : replaceArr[i]
                )
            }
        }
        for (let i = 0; i < searchArr.length; i++) {
            let sStr = searchArr[i];
            let rStr = replaceArr[i];
            if (sStr === '.') {
                newStr = newStr.replace(/\./g, rStr);
            } else {
                let regexStr = escapeRegExp(sStr);
                newStr = newStr.replace(new RegExp(regexStr, 'g'), rStr);
            }
        }
        return newStr
    } else {
        return contentStr.toString();
    }
}

// grabHereToCopyPaste
const extractEmailAddress=email=>((email.split('>')[0].split('<')[1])||email).replace(/ /g,'');

// grabHereToCopyPaste
const validateEmail=email=>(/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,10})+$/.test(email))

// grabHereToCopyPaste
const diffObjects = (leftObj,rightObj,maxDepth,matchPos,matchString,level,key) => {
    level = level||0
    maxDepth = maxDepth||1
    if (level>maxDepth) return leftObj
    if (areEqualObjects(leftObj,rightObj,key)) {
        // console.log('diffObjects > areEqualObjects > case: 1', jsonClone({key,leftObj,rightObj}));
        return null
    }
    if (typeof leftObj !== typeof rightObj) {
        // console.log('diffObjects > no match case: 2', jsonClone({key,leftObj,rightObj}));
		if (matchString===false && ![typeof leftObj, typeof rightObj].find(t=>t!=="number"&&t!=="string")) {
            if (isDefined(leftObj,false,false,true) && isDefined(rightObj,false,false,true) && parseFloat(leftObj)===parseFloat(rightObj)) {
                return null
            }
        }
        return isDefined(leftObj,true,true,true) ? leftObj : null
    }
    if ([typeof leftObj, typeof rightObj].includes('function')) {
        // console.log('diffObjects > function > case: 3', jsonClone({key,leftObj,rightObj}));
        return typeof leftObj === "function" ? leftObj : null
    }
    if (isEmptyObject(leftObj) && isEmptyObject(rightObj)) {
        // console.log('diffObjects > emptyObjects  > case: 4', jsonClone({key,leftObj,rightObj}));
        return {}
    }
    if (!isLoopable(leftObj)||!isLoopable(rightObj)) {
        // console.log('diffObjects > !loopable > case: 5', jsonClone({key,leftObj,rightObj}));
        return isDefined(leftObj,true,true,true) ? leftObj : null
    }
    let diffObject = Array.isArray(leftObj) ? [] : {};
    [...new Set([...Object.keys(leftObj), ...Object.keys(rightObj)])]
    .forEach(k=>{
        if (!isCyclic(leftObj[k]) && !isCyclic(rightObj[k])) {
            let res = diffObjects(leftObj[k],rightObj[k],maxDepth,matchPos,matchString,(level+1),k)
			let posLeft = Object.keys(leftObj).indexOf(k);
			let posRight = Object.keys(rightObj).indexOf(k);
            if (res!==null || (matchPos===true && posLeft!==posRight)) {
                Array.isArray(diffObject) ? (diffObject.push(res)) : (diffObject[k]=res);
                // console.log('diffObjects > no match case: 6', jsonClone({key,k,res,leftObj:leftObj[k], rightObj:rightObj[k]}));
            }
        }else{
            // console.log('diffObjects > isCyclic', jsonClone({key,k,leftObj:leftObj[k], rightObj:rightObj[k]}))
        }
    });
    return isLoopable(diffObject) ? diffObject : null
}

// grabHereToCopyPaste
const areEqualObjects = (obj1,obj2,key) => {
    if (typeof obj1 !== typeof obj2) {
        return false
    }
    if ([typeof obj1, typeof obj2].includes('function')) {
        return typeof obj1 === typeof obj2
    }
    if (isEmptyObject(obj1) && isEmptyObject(obj2)) {
        return true
    }
    if (!isLoopable(obj1)||!isLoopable(obj2)) {
        return obj1===obj2
    }
    return [...new Set([...Object.keys(obj1), ...Object.keys(obj2)])]
    .reduce((r,k)=>{
        if (isCyclic(obj1[k])||isCyclic(obj2[k])) {
            return r
        }
        let match = areEqualObjects(obj1[k],obj2[k],k)
        return r && match
    },true);
}

// grabHereToCopyPaste
const filterRecursively = (arr,finder,keys) => {
    keys = Array.isArray(keys) ? keys : null
	let all = arr.filter(finder);
	arr.forEach(a=>isLoopable(a) && (keys||Object.keys(a)).filter(c=>Array.isArray(a[c])).forEach(c=>{
		all = [...all,...filterRecursively(a[c],finder,keys)]
	}));
	return all
}

// grabHereToCopyPaste
const getDeepValue = (obj,keys) => {
    let v;
    if ( isset(obj,keys) ) {
        let test = JSON.parse(JSON.stringify(obj));
        keys.forEach(k => {
            test = test[k];
        });        
        v = test;
    }
    return v
}

// grabHereToCopyPaste
const isset = (obj,keys) =>{
	let failed = false;
	if ( isLoopable(obj) ) {
		try {
			let test = JSON.parse(JSON.stringify(obj));
			keys.forEach(k => {
				if (!failed && (k in test)) {
					test = test[k];
				}else
				failed = true;
			});
			return !failed
		} catch (error) {
			return false
		}
	}
	return false
}

// grabHereToCopyPaste
const uniqueTimestamp = milis => (milis&&!isNaN(Number(milis))?Math.round(milis):momentNow().valueOf())+randomInt(1111,9999);

// grabHereToCopyPaste
const randomInt = (min, max) =>{
    min = min || 1;
    max = max || 999999999999999999999;
    return Math.floor(Math.random() * (max - min + 1) + min);
}

// grabHereToCopyPaste
const getViewLink = (post, append) => {
    const { collection } = post;
    let viewLink = '';
    switch (collection) {
        case COLLECTIONS.LANDING_PAGES_COLLECTION_NAME:
            if ( (post.config||{}).customDomain && (post.config||{}).domain ) {
                let { config: { domain } } = post;
                viewLink = `https://${domain.replace('https://','').replace('http://','')}`;
            }else
            if ( post.permalink && post.ID ) {
                viewLink = `${CONSTANTS.REMOTE_URL}/l/${post.ID}/`;
            }
        break;
        case COLLECTIONS.FORMS_COLLECTION_NAME:
            if ( post.ID ) {
                viewLink = `${CONSTANTS.REMOTE_URL}/${collection}/${post.ID}/`;
            }
        break;
        default:break;
    }

    if ( viewLink && Boolean(append) && viewLink.indexOf(append)===-1 ) {
        viewLink += `${viewLink.split('').includes('?')?'&':'?'}${append}`;
    }

    return viewLink;
}

// grabHereToCopyPaste
const createShortlink = (longUrl) => {
	
	return new Promise((resolve,reject)=>{

		let token  = CONSTANTS.BITLY_TOKEN;
		let config = {
			headers: {
				'Authorization': `Bearer ${token}`,
				'Content-Type': 'application/json',
			}	
		}
		let data = {"domain":"bit.ly","long_url":longUrl};
		
		Axios.post('https://api-ssl.bitly.com/v4/shorten', JSON.stringify(data), config )
		.then((res) => {
			if ((res.status===200||res.status===201) && (res.data||{}).link) {
				let { data, data: { link } } = res;
				console.log(`bitly > shortlink: ${link}`)
				return resolve(data);
			}
			return resolve({ error: 'unknown' });
			// console.log(res.data)
		})
		.catch((error) => {
			console.log(error)
			return resolve({ error });
		})

	})

}

// grabHereToCopyPaste
const compileLogsStats = (logs, collection, dateFormats, counters, accumulators) => {
	counters = counters||[]
	accumulators = accumulators||[]
	dateFormats = Array.isArray(dateFormats) ? dateFormats : [dateFormats]

	let dataModel = {
		axis: {},
		aData: {},
		cData: {},
		rData: {},
		counters: {},
		accumulators: {},
		combined: {},
		contactIds: []
	};

	let collectionData = JSON.parse(JSON.stringify(dataModel))
	let collectionsData = {};
	
	logs.forEach((log,i) => {
		
		const logCollection = log.collection
		
		let { trigger, contactId, createdAt, date, data, context } = log;

		let logData = { ...data||{} }

		if (contactId && !logData.contactId) {
			logData.contactId = contactId
		}

		if (isLoopableObject(context)) {
			Object.keys(context)
			.filter(field=>isDefined(context[field],false,false,true) && 
				!logData[field] && !['collection','id'].includes(field)
			).forEach(field=>{
				logData[field] = context[field]
			})
		}

		let targetData = collectionData;

		if (logCollection!==collection) {
			collectionsData[logCollection] = collectionsData[logCollection]||JSON.parse(JSON.stringify(dataModel))
			targetData = collectionsData[logCollection]
		}

        targetData.counters[trigger] = targetData.counters[trigger]||{}
        targetData.accumulators[trigger] = targetData.accumulators[trigger]||{}
        targetData.combined[trigger] = targetData.combined[trigger]||{}
        
		counters.forEach(field => {
			targetData.counters[trigger][field]=targetData.counters[trigger][field]||[]
			if ((field in logData) && logData[field]) {
				let value = logData[field];
				let counterIndex = targetData.counters[trigger][field].findIndex(c=>c.value===value)
				if (counterIndex===-1) {
					targetData.counters[trigger][field].push({ key: field, value, count: 1 })
				}else{
					targetData.counters[trigger][field][counterIndex].count++;
				}
			}
		})

		accumulators.forEach(key => {
			targetData.accumulators[trigger][key] = targetData.accumulators[trigger][key]||0
			if ((key in logData) && isNumeric(logData[key]) ) {
				let value = Number(logData[key])
				targetData.accumulators[trigger][key] += value
				counters.forEach(field=>{
                    targetData.combined[trigger][field] = targetData.combined[trigger][field]||{}
					if ( logData[field] ) {
						let rId = logData[field]
                        targetData.combined[trigger][field][rId] = targetData.combined[trigger][field][rId]||{}
						targetData.combined[trigger][field][rId][key]=targetData.combined[trigger][field][rId][key]||0
						targetData.combined[trigger][field][rId][key]+=value
					}
				})
			}
		});

		contactId && (targetData.contactIds = [...new Set([...targetData.contactIds,contactId])])
		
		// axisData
		dateFormats.forEach(f=>{
			let ddd = moment(date).format(f);
			
			// axis
			!targetData.axis[f] && (targetData.axis[f] = []);
			targetData.axis[f] = [...new Set([...targetData.axis[f], ddd])];

			// aData
			!targetData.aData[trigger] && (targetData.aData[trigger]={});
			!targetData.aData[trigger][f] && (targetData.aData[trigger][f]={});
			!targetData.aData[trigger][f][ddd] && (targetData.aData[trigger][f][ddd]=0);
			targetData.aData[trigger][f][ddd]++;

			// cData
			!targetData.cData[trigger] && (targetData.cData[trigger]={});
			!targetData.cData[trigger][f] && (targetData.cData[trigger][f]={});
			!targetData.cData[trigger][f][ddd] && (targetData.cData[trigger][f][ddd]=[]);
			contactId && targetData.cData[trigger][f][ddd].push(contactId);

			counters.forEach(field=>{
				!targetData.rData[trigger] && (targetData.rData[trigger]={});
				!targetData.rData[trigger][field] && (targetData.rData[trigger][field]={});
				!targetData.rData[trigger][field][f] && (targetData.rData[trigger][field][f]={});
				!targetData.rData[trigger][field][f][ddd] && (targetData.rData[trigger][field][f][ddd]=[]);
				if ( logData[field] ) {
					let rId = logData[field]
					targetData.rData[trigger][field][f][ddd].push(rId);
				}
			})
		})
	
	});
	
	Object.keys(collectionsData).forEach((col,i)=>{
		let { axis, aData, cData, rData } = collectionsData[col];
		collectionsData[col].axisData = { axis, aData, cData, rData }
	})
	
	let size = logs.length;
	let { axis, aData, cData, rData } = collectionData;
	let stats = { ...collectionData, size, axisData: { axis, aData, cData, rData }, collectionsData };

	return stats

}

let delayLogTimeout, delayLogStack = []
// grabHereToCopyPaste
const delayLog = (obj,key,timeout) => {
    let index = delayLogStack.findIndex(o=>o.key===key&&(!key||areEqualObjects(o.obj,obj)))
    if (index===-1) delayLogStack.push({ obj, key })
    else delayLogStack[index]={ obj, key }
    clearTimeout(delayLogTimeout)
    delayLogTimeout=setTimeout(() => {
        delayLogStack.forEach(d=>console.log(d.key||'',d.obj));
        delayLogStack = []
    }, timeout||1000 );
}

module.exports = {
	momentNow,
	nowToISO,
	sortByDate,
	sortByUpdateDate,
	asyncForLoop,
	paginateArray,
	extractShortcodeFields,
	extractShortcodes,
	extractShortcodePaths,
	getShortcodePath,
	insertPixel,
	insertSubscriptionLinks,
	insertTrackingLinks,
	insertTrackingParams,
	getFetchedValue,
	calcFieldValue,
	getFieldValue,
	matchIfNumber,
	findUndefined,
	replaceUndefined,
	replaceKeysRecursively,
	deleteRecursively,
	sanitizeFieldTypes,
	isEmptyObject,
	isLoopableObject,
	isLoopable,
	isNumeric,
	isCyclic,
	numberFormat,
	convertTonumOnly,
	convertToBoolean,
	numericBoolean,
	capitalizeStr,
	mapOrphans,
	QR_STRING,
	getQRString,
	createQRCode,
	toSearchKeyword,
	createKeywords,
	generateKeywords,
	createLongTextKeywords,
	generateLongTextKeywords,
	generateSubstrKeywords,
	generateKeywordsDocs,
	FirestoreBatchHandler,
	objectDebugger,
	sTrim,
	lTrim,
	rTrim,
	removeSpaces,
	utf8BytesSize,
	jsonClone,
	isDefined,
	stringfy,
	arrayHasVal,
	escapeRegExp,
	replaceAll,
	extractEmailAddress,
	validateEmail,
	isset,
	diffObjects,
	areEqualObjects,
	filterRecursively,
	getDeepValue,
	randomInt,
	uniqueTimestamp,
	getViewLink,
	createShortlink,
	filterFields,
	sanitizeVal,
	calculate,
	hasStr,
	compileLogsStats,
	delayLog
}