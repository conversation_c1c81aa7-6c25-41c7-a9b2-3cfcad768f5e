const { ROLES, CONSTANTS, COLLECTIONS,  FirestoreRef, helpers, moment, momentNow, langMessages } = require('../init');
const { sanitizeFieldTypes, jsonClone } = helpers;
const { fetchPost, updateRef } = require('../post');

// grabHereToCopyPaste
const sanitizeAccount = async (sanitizedData, change, context) => {

    // console.log(`sanitizeAccount global > funcCounter:`, global.funcCounter);

    const { before, after } = change;
	const { params } = context;

	let promises = [];

	const accountId = params.docId;
	const oldData = (before.exists && before.data()) || {};
	const docRef = after.exists && change.after.ref;

	// Exit when the data is deleted.
	if (!change.after.exists) {
		return [{}];
	}
	
	const newData = sanitizedData;
	const updatedData = {};

	const { planId, config, modules, pagarme } = newData;

	if (newData) {

		// console.log('Editing Account:', accountId);
		// console.log('Editing newData:', newData);
		if ( planId && (oldData.config||{}).custom_plan===true && config.custom_plan===false ) {
			promises.push(new Promise(resPost=>fetchPost(COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME, planId).then(resPost).catch(err=>console.log('sanitizeAccount > fetchPost > err',{ planId, err})||resPost(null))))
		}		

		let cloneKeys = ['status', 'payment_method'];
		cloneKeys.forEach(k=>{
			if ( ((pagarme||{}).subscription||{})[k] && pagarme.subscription[k] !== pagarme[k] ) {
				updatedData.pagarme = { ...pagarme, ...updatedData.pagarme||{}, [k]: pagarme.subscription[k] }
			}
		})

	}	
	
	// merge changes
	const mergedData = { ...newData, ...updatedData }

	// sanitize Field Types
	sanitizedData = sanitizeFieldTypes(mergedData);
	
	let hasUpdates = false;
	const updateKeys = [];

	Object.keys(sanitizedData).forEach(k=>{
		const updatedValue = sanitizedData[k];
		const newValue = newData[k];
		if ( helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue) ) return;
		if ( typeof newValue !== typeof updatedValue || 
			((k in updatedData) && JSON.stringify(newValue) !== JSON.stringify(updatedValue))
		) {
			hasUpdates = true;
			updateKeys.push(k)
		}
	})
	
	let updateObj = {};
	if ( hasUpdates && change.after.exists ) {
		updateObj = { systemUpdate: true };
		updateKeys.forEach(k=>{
			updateObj[k] = sanitizedData[k];
		})
		console.log('sanitizeAccount > hasUpdates', { updateKeys, updateObj });
	}
	
	// console.log('mergedData', mergedData);
	return [updateObj, async () => {
		if ( promises.length ) {
			await Promise.all(promises).then(results=>{
				results.forEach((post,r)=>{
					if ( post && post.collection===COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME ) {
						if ( planId && (oldData.config||{}).custom_plan===true && config.custom_plan===false ) {
							let { modules } = post;
							updateRef(docRef, { modules })
						}
					}
					return null;
				})			
				return null;
			}).catch(err=>{
				console.error(err);
				return null
			})
		}
		return null;
	}];	

};

module.exports = {
    sanitizeAccount
}