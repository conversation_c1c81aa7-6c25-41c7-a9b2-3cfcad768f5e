const { ROLES, CONSTANTS, COLLECTIONS } = require('../init');
const { FirestoreRef, helpers, moment, momentNow, langMessages, FieldValue } = require('../init');
const { fetchPost, updatePost, fetchCollection, updateRef } = require('../post');
const { validateEmailAddress } = require('../mailing');

const {
	sTrim,
	createQRCode,
	sanitizeFieldTypes,
	jsonClone,
} = helpers;

// grabHereToCopyPaste
const sanitizeQIUserData = async (sanitizedData, change, context) => {

	const { before, after } = change;
	const { params } = context;

	const userId = params.docId;
	
	const oldData = (before.exists && before.data()) || {};
	const newData = sanitizedData;
	const docRef = after.exists && change.after.ref;
	const updatedData = {};

	let promises = [];
	let deleteKeys = [];

	// Exit when the data is deleted.
	if (!change.after.exists) {
		return [{}];
	}

	if (newData) {

		// console.log('Editing QIUser:', userId);
		// console.log('Editing newData:', newData);

		// // Get the first and last names
		const firstName = newData.firstName || '';
		const lastName = newData.lastName || '';
		const displayName = `${firstName}${(lastName && ' ' + lastName) || ''}`;

		// Clean password
		if (newData.password) {
			updatedData.password = "";
		}

		// Update Display Name
		if (!newData.displayName) {
			updatedData.displayName = displayName;
		}
		
		// Update First Name
		if (!newData.firstName) {
			updatedData.firstName = updatedData.displayName ? updatedData.displayName : (updatedData.email && updatedData.email.split('@')[0])||'';
		}

		// Validate Email Address
		if (newData.email !== oldData.email || !('mailbox_verified' in newData) ) {
			if (newData.email) {
				try {
					let emailValidation = await validateEmailAddress(newData.email);
					if (emailValidation && emailValidation.address) {
						updatedData.mailbox = emailValidation;
						updatedData.is_valid = Boolean(emailValidation.is_valid);
						updatedData.mailbox_verified = ['true',true].includes(emailValidation.mailbox_verification);
					}
				} catch (error) {
					console.error(error);
					updatedData.mailbox_verified = null;
				}
			}else{
				updatedData.mailbox_verified = null;
				updatedData.is_valid = false;
			}
		}

		// Update Birthday
		if (newData.birthday !== oldData.birthday || !('birthday_verified' in newData)) {
			
			let bday = sTrim(newData.birthday).replace(/ /g,'');
			if (bday.split('/').length===3) {
				let birthday = bday.split('/').reverse().map((v,i)=>{
					let val = v;
					switch (i) {
						case 0:							
							if (val.toString().length<4) val = Number('20'+val)-1>=new Date().getFullYear() ? '19'+val : '20'+val;
						break;					
						default:
							while (val.toString().length<2) val = '0'+val;
						break;
					}
					return val;
				}).join('-')
				updatedData.birthday = birthday;
			}
			
			let momentBday = moment(updatedData.birthday||bday);
			let isValidDate = momentBday.isValid();

			updatedData.birthday_verified = isValidDate;
			
			if (isValidDate) {
				updatedData.birth = {
					year: momentBday.format('YYYY'),
					month: momentBday.format('MM'),
					day: momentBday.format('DD'),
				}
			}
		}
		
		// Update Address
		CONSTANTS.LOCATION_FIELDS_MAP.forEach(k=>{
            if (newData.address && (k in newData.address) && newData.address[k] !== newData[k]) {
                updatedData[k] = newData.address[k];
            }
        })
        
        CONSTANTS.ADDRESS_FIELDS_MAP.concat(CONSTANTS.ADDRESS_SHORTCODES_FIELDS_MAP).forEach(k=>{
            if ( (k in newData) && CONSTANTS.LOCATION_FIELDS_MAP.indexOf(k)===-1 ) {
				
				let dbKey = k.replace(`${CONSTANTS.ADDRESS_FIELDS_GROUP}:`,'');
				
				if (!updatedData.address) updatedData.address = {}
				updatedData.address[dbKey] = newData[k];
				
				delete newData[k];
				deleteKeys.push(k);
				
            }
        })

		// Set QR Code
		if (!newData.qrcode) {
			updatedData.qrcode = createQRCode(newData);
		}

		// Add Role if it doesn't already exist
		let minLevel = parseInt(Object.keys(ROLES.QIPLUS_ROLES_HERARCHY).pop());
		if (!Array.isArray(newData.roles) || !newData.roles.length) {
			updatedData.roles = [ROLES.QIPLUS_ROLES_HERARCHY[minLevel]];
		}
		
		// Update To QIPlus Roles
		Array.isArray(newData.roles) && (
			updatedData.roles = newData.roles.filter(role=>(role in ROLES))
		);
			
		// Level
		let newLevel = minLevel;
		const roles = updatedData.roles || newData.roles;
		Array.isArray(roles) && roles.forEach(role=>{ 
			if(ROLES.QIPLUS_ROLES_LEVELS[role] < newLevel) 
				newLevel = ROLES.QIPLUS_ROLES_LEVELS[role];
		})

		if (!newData.level || newLevel !== newData.level ) {
			updatedData.level = newLevel;
		}
	
		if (!updatedData.level && typeof newData.level !== 'number' ) {
			updatedData.level = parseInt(newLevel);
		}
		
		newLevel = updatedData.level || newData.level;
		const currentOwnerId = updatedData.owner||newData.owner;
		const ownerId = newLevel <= ROLES.OWNER_LEVEL ? userId : currentOwnerId;
		
		if ( !before.exists && ownerId !== newData.owner ) {
			updatedData.owner = ownerId;
		}

	}	
	
	// merge changes
	const mergedData = { ...newData, ...updatedData }

	// sanitize Field Types
	sanitizedData = sanitizeFieldTypes(mergedData);
	
	let hasUpdates = false;
	const updateKeys = [];

	Object.keys(sanitizedData).forEach(k=>{
		const updatedValue = sanitizedData[k];
		const newValue = newData[k];
		if ( helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue) ) return;
		if ( typeof newValue !== typeof updatedValue || 
			((k in updatedData) && JSON.stringify(newValue) !== JSON.stringify(updatedValue))
		) {
			hasUpdates = true;
			updateKeys.push(k)
		}
	})
	
	hasUpdates = hasUpdates || Boolean(deleteKeys.length);

	let updateObj = {};
	if (hasUpdates && change.after.exists) {
		updateObj = { systemUpdate: true };
		updateKeys.forEach(k=>{
			updateObj[k] = sanitizedData[k];
		})
		deleteKeys.forEach(k=>{
			updateObj[k] = FieldValue.delete();
		})
		console.log('sanitizeQIUserData > hasUpdates', { updateKeys, deleteKeys, updateObj });
	}
	
	return [updateObj, async () => {
		if( promises.length ) {
			await Promise.all(promises).catch(console.error)
		}
		return null;
	}];
};

// grabHereToCopyPaste
const setTeamMembers = async ({ lead, deal, team, funnel, accountId, mode, overwrite = false }) => {
	
	let hasUpdate = false
	let target = deal ? deal : lead;
	let teamData = {};
	
	try {
		let teamMembers = await getTeamMembers({ lead, deal, team, funnel, accountId, mode, overwrite })
		let teamFields = [CONSTANTS.TEAM_FIELD, CONSTANTS.SELLER_FIELD, CONSTANTS.MANAGER_FIELD]
		
		teamFields.forEach(f=>{
			if ( teamMembers[f] && (!target[f] || overwrite===true) ) {
				teamData[f] = teamMembers[f]
				hasUpdate = true
			}
		})
	
	} catch (error) {
		console.log('error',error);
		return null
	}

	return Boolean(target.ID) && hasUpdate ? updatePost(target.collection, { ...teamData }, target.ID) : null;

}

// grabHereToCopyPaste
const getTeamMembers = async ({ lead, deal, team, funnel, accountId, mode, overwrite = false }) => {
	
	team = team||{}
	funnel = funnel||{}
	
	const isDeal = !lead && Boolean((deal||{})[CONSTANTS.FUNNEL_ID_FIELD]);
	const target = isDeal ? deal : lead;
	const teamId = team[CONSTANTS.ID_FIELD] || funnel[CONSTANTS.TEAM_FIELD] || target[CONSTANTS.TEAM_FIELD] || "";
	const config = (funnel[CONSTANTS.TEAM_FIELD] && funnel.config) || team.config || {};

	// console.log('getTeamMembers > 1',{ isDeal, target, teamId, config });

	const sources = { lead, deal, team, funnel, accountId }
	if (!accountId) {
		Object.keys(sources).forEach((postType,i)=>{
			const postData = sources[postType] || {};
			if (!accountId && postData[CONSTANTS.ACCOUNT_FIELD] && postData[CONSTANTS.ACCOUNT_FIELD]!==CONSTANTS.ORPHANS_ACCOUNT) {
				accountId = postData[CONSTANTS.ACCOUNT_FIELD]
			}
		})
	}
	
	// console.log('getTeamMembers > 2',{ accountId });
	if ( !team.ID && teamId ) {
		try {
			team = await fetchPost(COLLECTIONS.TEAMS_COLLECTION_NAME, teamId, true); 
		} catch (error) {
			console.log('error',error);
		}
	}

	// console.log('getTeamMembers > 3',{ teamId });

	let teamSource = {
		sellers: team.sellers || [],
		managers: team.managers || [],
	}

	const teamFields = [{
		targetField: CONSTANTS.SELLER_FIELD,
		teamField: 'sellers',
	},{
		targetField: CONSTANTS.MANAGER_FIELD,
		teamField: 'managers',
	}]

	const newTeam = {
		[CONSTANTS.TEAM_FIELD]: teamId,
		[CONSTANTS.SELLER_FIELD]: target[CONSTANTS.SELLER_FIELD]||"",
		[CONSTANTS.MANAGER_FIELD]: target[CONSTANTS.MANAGER_FIELD]||"",
	}

	if ( teamId && !target[CONSTANTS.TEAM_FIELD] ) {
		newTeam[CONSTANTS.TEAM_FIELD] = teamId;
	}

	// console.log('getTeamMembers > 4',{ teamSource, newTeam });

	let promises = teamFields.map(f=>new Promise(async res=>{
	
		let { targetField, teamField } = f

		if ( teamSource[teamField].length || (!target[targetField] || overwrite===true) ) {

			const distMode = mode || config[`${teamField}_mode`] || config.sellers_mode || CONSTANTS.RANDOM_MODE;
		
			// console.log('getTeamMembers > 5',{ mode });
			
			const members = teamSource[teamField]
				
			if (members.length === 1) {
				
				newTeam[targetField] = members[0]
				
			}else{

				// console.log('getTeamMembers > 6',{ members });
				switch (distMode) {
					
					case CONSTANTS.EQUAL_MODE:
						
						var { collection } = target;
						var memberId = target[targetField] || "";
						var membersCounter = {}
						
						var where = []

						if (members.length <= 10) {
							where.push([targetField,"in",members])
						}
						if ( isDeal && deal[CONSTANTS.FUNNEL_ID_FIELD] ) {
							where.push([CONSTANTS.FUNNEL_ID_FIELD,"==",deal[CONSTANTS.FUNNEL_ID_FIELD]])
						}else{
							where.push([CONSTANTS.ACCOUNT_FIELD,"==",accountId])
						}
	
						// console.log('getTeamMembers > 7',{ where });

						var siblingTargets = await fetchCollection(collection, where)

						if ( siblingTargets && siblingTargets.length ) {
							siblingTargets.forEach(t => {
								let currentMember = t[targetField];
								if (!currentMember || !members.includes(currentMember)) return;
								if (membersCounter[currentMember]) {
									membersCounter[currentMember]++;
								}else{
									membersCounter[currentMember] = 1;
								}
							})
						}

						// console.log('getTeamMembers > 8',{ membersCounter });
	
						if ( Object.keys(membersCounter).length ) {
							memberId = Object.keys(membersCounter).reduce((a,b,i)=>{
								return i===0 ? b: (membersCounter[b] < membersCounter[a]||0 ? b : a);
							},"")||target[targetField];
						}else{
							var randMember = Math.floor(Math.random() * members.length) + 1;
							memberId = members[randMember-1];
						}
	
						console.log('getTeamMembers',{ [targetField]: memberId, membersCounter });
						
						newTeam[targetField] = memberId;
						
					break;
					
					case CONSTANTS.RANDOM_MODE:
					default:
						var randIndex = Math.floor(Math.random() * members.length) + 1;
						newTeam[targetField] = members[randIndex-1];
					break;
	
				}
			}

		}

		res(newTeam)
	}))
	
	return Promise.all(promises)
	.then((results)=>{
		// console.log('getTeamMembers > results',{ results });
		return newTeam;
	}).catch(error=>{
		console.log('getTeamMembers > error',{ error });
		return newTeam
	})

};

module.exports = {
    sanitizeQIUserData,
	setTeamMembers,
	getTeamMembers
}