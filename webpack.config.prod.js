'use strict';

// Webpack uses `publicPath` to determine where the app is being served from.
// In development, we always serve from the root. This makes config easier.
const publicPath = '/app/';

const webpackCommonConfig = require('./webpack.config.common')

module.exports = {
    ...webpackCommonConfig,
    output: {
        ...webpackCommonConfig.output,
        // We inferred the "public path" (such as / or /my-project) from homepage.
        publicPath,
    }
}