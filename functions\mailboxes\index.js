const { ROLES, CONSTANTS, COLLECTIONS,  FirestoreRef, helpers, cors, admin, moment, momentNow, langMessages, ERROR_TYPES } = require('../init');
const { DEFAULT_FROM_EMAIL, EMAIL_APP_DOMAIN, getDomainMailboxes, createDomainMailbox, qiplusDomains } = require('../mailgun');
const { fetchCollection } = require('../post');
const inspect = require('util').inspect;

const { validateEmail, sTrim, uniqueTimestamp, jsonClone, extractEmailAddress, isLoopable } = helpers;

const storage = admin.storage();

// grabHereToCopyPaste
const getStoredFileUrl = (filePath) => {

	const file = storage.bucket().file(filePath);

	return file.exists()
	.then( data => {
		const exists = Boolean(data[0]);
		if( exists ) 
			return file.getSignedUrl({ action: 'read', expires: '2100-01-01T00:00:00'})
		else return ['']
	})
	.then(urls => {
		const url = urls[0];
		return url
	})
	.catch(error => {
		console.log(`Unable to get stored file ${error}`)
		return {error}
	})
}

// grabHereToCopyPaste
const uploadBase64File = async (base64Str, filePath, metadata, checkPath) => {
	
	if ( checkPath===true ) {
		let fileUrl = await getStoredFileUrl(filePath);
		if (fileUrl) return new Promise(r=>r(fileUrl));
	}

	const buffer = Buffer.from(base64Str, 'base64')
	const bytesArray = new Uint8Array(buffer);
	const file = storage.bucket().file(filePath);
	const options = { resumable: false, metadata: { ...metadata||{filePath} } };

	//options may not be necessary
	return file.save(bytesArray, options)
	.then(stuff => {
		return file.getSignedUrl({ action: 'read', expires: '2100-01-01T00:00:00'})
	})
	.then(urls => {
		const url = urls[0];
		return url
	})
	.catch(error => {
		console.log(`Unable to upload file ${error}`)
		return {error}
	})
	
}

// grabHereToCopyPaste
const getImapAttachments = (data, uploadAttachments) => {

	return new Promise(res=>{

		let uploads = [];
		let attachments = [];
		let { raw, seqno, folder, user } = data, matches = /--[^\n\r]*\r?\nContent-Type:[\s\S]*?\r?\n\r?\n/g.exec(raw);
	
		while (matches) {

			let info = matches[0];

			try {

				let part = (info.match(/--[^\n\r]*\r?/g)||[])[0]
				let contentType = (/Content-Type:(.*)/.exec(info)||[''])[0].split(';')[0].replace('Content-Type: ','');
				let contentId = (/Content-ID: (.*)/.exec(info)||[''])[0].replace(/Content-ID: |<|>/g,'')||'';
				let filename = (/filename=(.*)/.exec(info)||[''])[0].replace(/filename=|"/g,'');
				let isBase64 = /Content-Transfer-Encoding: base64(.*)/.exec(info);
				let isApplication = contentType.match(/application\//);

				let content = raw.split(info)[1];
				let nextInfo = (/--[^\n\r]*\r?\nContent-Type[\s\S]*?\r?\n\r?\n/g.exec(content)||[])[0]

				if ( part && (content.match(part)||[])[0] ) {
					content = content.split(part)[0];
				}else
				if ( nextInfo ) {						
					content = content.split(nextInfo)[0];
				}else
				if ( content.split('--')[0] ) {
					content = content.split('--')[0];
				}else{
					content = content.split('=')[0]+'=';
				}

				content = content.split('--')[0];

				console.log(`(#${seqno}) getImapAttachments > info`,{ info, contentType, isBase64 });

				if ( isApplication ) {

					const userPath = user.replace('@','-at-');
					const folderPath = folder.toLowerCase().replace(/ /g,'');
					const filePath = `attachments/${userPath}/${folderPath}/${seqno}/${filename}`;
					const metadata = { contentType, customMetadata: { seqno, folder, filePath, user } };

					let attachment = {
						attachment_url: '',
						preview_url: '',
						attachment_id: contentId,
						name: filename,
						mimeType: contentType,
						contentBytes: '',
						metadata
					}

					uploads.push({ content, filePath, metadata, attachment })
					
					raw = raw.split(content)[1];

				}else
				if ( isBase64 ) {
					
					let attachment = {
						attachment_url: '',
						preview_url: '',
						attachment_id: contentId,
						name: filename,
						mimeType: contentType,
						contentBytes: content
					}
		
					attachments.push(attachment)
					
					raw = raw.split(content)[1];

				}else{
					raw = raw.replace(info,'');
				}
				
				matches = /--[^\n\r]*\r?\nContent-Type:[\s\S]*?\r?\n\r?\n/g.exec(raw)
				
			} catch (error) {
				
				console.log(`(#${seqno}) getImapAttachments > error`,error);			
				raw = raw.replace(info,'');
				matches = /--[^\n\r]*\r?\nContent-Type:[\s\S]*?\r?\n\r?\n/g.exec(raw)

			}
		
		}

		const promises = uploads.map(({ content, filePath, metadata, attachment })=>
			new Promise(async res=>{
				let fileUrl = await getStoredFileUrl(filePath);
				console.log(`(#${seqno}) fileUrl`,fileUrl);				
				if (fileUrl && typeof fileUrl === 'string') {
					return res({ attachment: {
						...attachment,
						attachment_url: fileUrl,
					}})
				}else
				if ( uploadAttachments===true ) {
					return uploadBase64File(content, filePath, metadata, true)
					.then(url=>{
						console.log(`(#${seqno}) uploadBase64File > url`,url);
						if (typeof url === 'string') {
							return res({ attachment: {
								...attachment,
								attachment_url: url,
							}})
						}
						return res({ error: url })						
					})
					.catch(error=>{
						console.log(`(#${seqno}) uploadBase64File > error`,error);		
						return res({ error })
					})
				}else
				return res({ attachment })
			})
		)
		
		return Promise.all(promises)
		.then(results=>{
			results.forEach(({ attachment, error })=>{
				if (attachment) attachments.push(attachment)
			})
			console.log(`(#${seqno}) getImapAttachments > attachments`,{ attachments });
			return res({ attachments })
		})
		.catch(error=>{
			console.log(`(#${seqno}) uploadBase64File > error`,error);		
			return res({ error, attachments: [] })
		})
	})

}

// grabHereToCopyPaste
const handleMailboxesUpdate = (change, context) => {

	const { before, after } = change;
	const { params } = context;

	const mailboxId = params.mailboxId;
	
	const oldData = (before.exists && before.data()) || {};
	const newData = (after.exists && after.data()) || {};
	
	if ( after.exists ) {
	
		let { from, fromName } = newData;
		
		if ( from && validateEmail(sTrim(from))) {

			from = sTrim(from);
			
			let updatedData = {
				ID: mailboxId,
				mailboxId,
				mailbox: from,
				from,
				fromName: fromName||from.split('@')[0],
				validated: true,
			}
			
			// updatedAt
			if (!newData.updatedAt || oldData.updatedAt === newData.updatedAt) {
				updatedData.updatedAt = uniqueTimestamp(after.updateTime.toMillis());
			}

			// updatedAt
			if (!newData.title || oldData.title !== from) {
				updatedData.title = from;
			}

			let hasUpdates = false;
			const updateKeys = [];
		
			Object.keys(updatedData).forEach(k=>{
				const updatedValue = updatedData[k];
				const newValue = newData[k];
				if ( helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue) ) return;
				if ( typeof newValue !== typeof updatedValue || 
					((k in updatedData) && JSON.stringify(newValue) !== JSON.stringify(updatedValue))
				) {
					hasUpdates = true;
					updateKeys.push(k)
				}
			})
			
			if (hasUpdates) {
				let updateObj = { systemUpdate: true };
				updateKeys.forEach(k=>{
					updateObj[k] = updatedData[k];
				})
				console.log('handleMailboxes > hasUpdates', { updateKeys, updateObj });
				return change.after.ref.update(updateObj);
			}

			return null;

		}else
		if ( newData.validated ) {
			return change.after.ref.update({ validated: false });			
		}
		
	}
	
	return null;

}

// grabHereToCopyPaste
const handleMailboxesCreate = (doc, context) => {

	const { params } = context;

	const mailboxId = params.mailboxId;
	
	const data = doc.data();
	
	let { from, pass, fromName } = data;

	if ( from && validateEmail(sTrim(from))) {

		from = sTrim(from);
		pass = pass ? sTrim(pass) : null;
		
		const domain = from.split('@')[1];

		let updatedData = {
			ID: mailboxId,
			mailboxId,
			collection: COLLECTIONS.MAILBOXES_COLLECTION_NAME,
			fromName: fromName||from.split('@')[0],
			createdAt: uniqueTimestamp(doc.createTime.toMillis()),
			updatedAt: uniqueTimestamp(doc.createTime.toMillis()),
		}

		return new Promise((res,rej)=>{

			if ( qiplusDomains.includes(domain) ) {
				return getDomainMailboxes(({error, body})=>{
					if ( body && body.items ) {
						let mailboxes = body.items;
						if (mailboxes.find(m=>m.mailbox===from)) {
							return res({
								...updatedData,
								error: langMessages["mailboxes.unavailableEmailAddress"],
								mailbox: '',
								available: false, 
								validated: false,
							})
						}else
						return createDomainMailbox(from,pass,({ error, body })=>{
							if (error) return rej(new Error(error));
							return res({
								...updatedData,
								mailbox: from,
								pass: null,
								available: true,
								validated: true,
							})
						},domain)
					}else
					return rej(new Error('Failed to get mailboxes'));
				}, domain)					
			}else
			return res({
				...updatedData,
				mailbox: from,
				validated: true,
			})
			
		})
		.then(updateData=>{
			return doc.ref.update(updateData)
		})
		.catch(err=>{
			console.error(err);
			return doc.ref.update({
				error: err,
				mailbox: '',
				validated: false,
			})
		})
		
	}

	return doc.ref.update({ validated: false });

}

// grabHereToCopyPaste
const handleMailboxesEmails = (doc, context) => {

	const { params } = context;

	const mailId = params.mailId;
	const mailboxId = params.mailboxId;
	const folder = params.folder;

	const data = doc.data();
	
	if ( mailboxId === COLLECTIONS.INDEX_ID ) {
		return null;
	}

	let { accountId, owner, authorId, to, html, contacts, segmentations, sent, sending, error } = data;

	if ( !error && !sending && !sent ) {

		let updatedData = {
			ID: mailId,
			id: mailId,
			mailId,
		}
		
		// In case the email is being moved from another folder
		if (!data.createdAt) {
			updatedData.createdAt = doc.createTime.toMillis();
		}
		
		if (folder==='trash') {
			updatedData.trashedAt = uniqueTimestamp(doc.createTime.toMillis());
		}

		return doc.ref.update(updatedData)
		.then(()=>{
			if (folder==='sent') {
				return FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME).doc(mailId).set({
					...data,
					...updatedData,
					prepared: to && to.split(',').map(t=>validateEmail(sTrim(t))).reduce((a,b)=>a&&b,Boolean(to)) && !(contacts && contacts.length) && !(segmentations && segmentations.length),
					accountId,
					context: {
						id: mailId,
						collection: COLLECTIONS.MAILBOXES_COLLECTION_NAME,
						operator_id: authorId||'',
						accountId,
						owner,
						folder,
						mailboxId
					}
				})
			}
			return null;
		})
		.catch(err=>{
			console.error(err);
		})

	}

	return null;

}

// grabHereToCopyPaste
const checkMailboxAvailability = async (request, response) => {
	cors(request, response, async () => { 

		const data = request.body.data;

		if (!data || !data.mailboxData || !data.mailbox || !validateEmail(data.mailbox)) {
			console.log('onRequest > request', request);
			return null;
		}

		const user = data.user || {};
		const mailbox = data.mailbox;
		const domain = mailbox.split('@')[1];
		const mailboxData = data.mailboxData;
		const collection = COLLECTIONS.MAILBOXES_COLLECTION_NAME;

		try {
			
			const accountId = mailboxData.accountId;	
			const accountDoc = await FirestoreRef.collection(COLLECTIONS.ACCOUNTS_COLLECTION_NAME).doc(accountId).get();
			const account = accountDoc.exists ? accountDoc.data() : {};
			
			if ( (account||{}).modules ) {
				
				const accountRef = accountDoc.ref;
				const counterDoc = await accountRef.collection(COLLECTIONS.INDEX_ID).doc(COLLECTIONS.COUNTER_ID).get();
				const counterData = counterDoc.exists ?  counterDoc.data() : {};

				const { modules, config } = account;
				console.log('modules', { modules, config });
				if ( (collection in modules) && !modules[collection] ) {
					return response.send({ data: { available: false, error: 'MODULE_NOT_AVAILABLE' } });
				}
				if ( (`${collection}_included` in config) && config[`${collection}_included`] ) {
					let limit = Number(config[`${collection}_included`]);
					let posts = await fetchCollection(collection, [[CONSTANTS.ACCOUNT_FIELD,'==',accountId],['provider','==',domain]]);
					console.log('posts', { posts: posts.length, limit });				
					if (posts && limit && posts.length >= limit) {
						let current = posts.length;
						const errorType = ERROR_TYPES.MODULE_LIMIT_REACHED;
						const errorMessage = 'O limite de [%collection] da conta foi atingido'.replace('[%collection]',`caixas de entrada`);
						return response.send({ data: { error: errorType, errorMessage, errorType, debug: {}, available: false, limit, current }})
					}
				}
			}else{
				console.error('checkMailboxAvailability > No account found');
				const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
				const errorMessage = 'Ocorreu um erro na requisição. Conta não encontrada.';
				return response.send({ data: { error: errorType, errorMessage, errorType, debug: {}, available: false }})
			}
			
			return getDomainMailboxes(({error, body}) => {
				if ( body && body.items ) {
					
					let suggestionsKeys = ['firstName','lastName','displayName','email'];
					let suggestions = [];
					let mailboxes = body.items;

					if (mailboxes.find(m=>m.mailbox===mailbox)) {
						if (user) {					
							suggestions = suggestionsKeys.map(key=>{
								var test = user[key] && sTrim(user[key].split('@')[0]);
								test = test && test.split(' ').join('_').toLowerCase();
								test = test && `${test}@${domain}`;
								if (test && validateEmail(test) && !mailboxes.find(m=>m.mailbox===test)) {
									return test;
								}
								return false;
							}).filter(s=>s);
						}
						return response.send({ data: { available: false, suggestions, error: langMessages["mailboxes.unavailableEmailAddress"] }});
					}
					return response.send({ data: { available: true }});
				}
				return response.send({ data: { available: false, error }});
				
			}, domain);
		
		}
		catch (error) {
			return response.send({ data: { available: false, error } });		
		}
		
	});
	return null;

}

// grabHereToCopyPaste
const getCloudMailboxes = async (request, response) => {
	cors(request, response, async () => { 

		const data = request.body.data;

		if (!data) {
			console.log('onRequest > request', request);
		}

		const domain = (data||{}).domain || EMAIL_APP_DOMAIN;

		getDomainMailboxes(({error, body}) => {
			if ( body && body.items ) {
				response.send({ data: { mailboxes: body.items, error }});
			}
			response.send({ data: { mailboxes: [], error }});
		}, domain);

	});
	return null;

}

// grabHereToCopyPaste
const getIMAPFolders = async (request, response) => {
	cors(request, response, async () => { 

		const results = {};
		const data = request.body.data;

		if (!data || !data.config) {
			console.log('onRequest > request', request);
		}

		if (!data || !data.config || !data.config.user || !data.config.password || !data.config.host) {
			return response.send({ data: { ...data, error: 'MISSING_REQUIRED_FIELDS' } });
		}

		let { config } = data;

		const Imap = require('imap');
		let imap = new Imap(config);
		
		results.errors = []
		
		function imapNestedFolders(folders) {
			var FOLDERS = [];
			var folder  = {};		

			try {
				console.log('getIMAPFolders > folders', inspect(folders, true, 10, true));			
			} catch (error) {
				console.log('getIMAPFolders > error', { error });
			}
			
			if (isLoopable(folders)) 
				for (var key in folders) {
					let f = folders[key];
					// console.log('getIMAPFolders > folder',f);
					if ((f.attribs||'').indexOf('\\HasChildren') > -1) {		
						var children = imapNestedFolders(f.children);
						folder = {
							key,
							name: key,
							children,
							data: { ...f, key, parent: null, children: null }
						};		
					} else {
						folder = {
							key,
							name: key,
							children: null,
							data: { ...f, key, parent: null, children: null }
						};
					}
					FOLDERS.push(folder);		
				}

			return FOLDERS;
		}

		imap.once('ready', () => {
			
			imap.getBoxes((err, boxes) => {

				if (err) {
					console.log('getIMAPFolders > err', { err });
					const errorType = ERROR_TYPES.ERROR_GETTING_MAILBOX_FOLDERS;
					const errorMessage = 'Ocorreu um erro ao obter as pastas desta caixa de entradas';
					return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
				}
			
				try {
				
					let folders = imapNestedFolders(boxes);
					results.folders = folders;				
					// console.log('getBoxes > folders',inspect(folders, true, 10, true));
					return response.send({ data: { results }});

				} catch (error) {
					console.log('getIMAPFolders > catch > err', { error });
					const errorType = ERROR_TYPES.ERROR_GETTING_MAILBOX_FOLDERS;
					const errorMessage = 'Ocorreu um erro ao obter as pastas desta caixa de entradas';
					return response.send({ data: { error: errorType, errorMessage, errorType, debug: error, results }})
				}

			});
				
		});
		
		imap.once('error', (err) => {
			console.log('getIMAPFolders > once > error', {err});
			const errorType = (err && err.type==='no' && err.source==='authentication') ? ERROR_TYPES.AUTH_ERROR : ERROR_TYPES.ERROR_GETTING_MAILBOX_FOLDERS;
			const errorMessage = 'Ocorreu um erro ao salvar a mensagem';
			return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
		});
		
		imap.once('end', () => {
			console.log('Connection ended');
			return response.send({ data: { results }});
		});
		
		return imap.connect();

	});
	return null;

}

// grabHereToCopyPaste
const moveIMAPMail = async (request, response) => {
	cors(request, response, async () => { 

		const results = {};
		const data = request.body.data;

		if (!data || !data.config) {
			// console.log('onRequest > request', request);
			return null;
		}

		if (!data.config.user || !data.config.password || !data.config.host) {
			return response.send({ data: { ...data, error: 'MISSING_REQUIRED_FIELDS' } });
		}

		console.log('moveIMAPMail > data',{ data });

		let { config, folder, uid, destination } = data;

		const Imap = require('imap');
		let imap = new Imap(config);
		
		results.errors = []
		
		imap.once('ready', () => {
			
			imap.openBox(folder, false, (err, box) => {
				
				if (err) {
					console.log('moveIMAPMail > err', { err });
					const errorType = ERROR_TYPES.ERROR_OPENING_MAILBOX;
					const errorMessage = 'Ocorreu um erro ao obter as pastas desta caixa de entradas';
					return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
				}

				try {
					
					imap.move(`${uid}`, destination, (err)=>{
		
						if (err) {
							const errorType = ERROR_TYPES.ERROR_MOVING_MESSAGE;
							const errorMessage = 'Ocorreu um erro ao mover a mensagem';
							return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
						}
						
						results.status = true;
						results.end = true;
						imap.end();
						
						console.log('Done moving message');
						// console.log('Done fetching all message > results', results);
						return response.send({ data: { results }});
						
					})
					
				} catch (error) {
					const errorType = ERROR_TYPES.ERROR_MOVING_MESSAGE;
					const errorMessage = 'Ocorreu um erro ao mover a mensagem';
					return response.send({ data: { error: errorType, errorMessage, errorType, debug: error, results }})
				}

				return null;

			});

		});
		
		imap.once('error', (err) => {
			console.log('imap > once > error', {err});
			const errorType = (err && err.type==='no' && err.source==='authentication') ? ERROR_TYPES.AUTH_ERROR : ERROR_TYPES.ERROR_ON_MOVING_MESSAGE;
			const errorMessage = 'Ocorreu um erro ao salvar a mensagem';
			return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
		});
		
		imap.once('end', () => {
			console.log('Connection ended');
			return response.send({ data: { results }});
		});
		
		return imap.connect();

	});
	return null;

}

// grabHereToCopyPaste
const saveIMAPMail = async (request, response) => {
	cors(request, response, async () => { 

		const results = {};
		const data = request.body.data;

		if (!data || !data.config) {
			// console.log('onRequest > request', request);
			return null;
		}

		if (!data.config.user || !data.config.password || !data.config.host) {
			return response.send({ data: { ...data, error: 'MISSING_REQUIRED_FIELDS' } });
		}

		console.log('saveIMAPMail > data',{ data });

		let { config, folder, uid, message, options } = data;

		const Imap = require('imap');
		let imap = new Imap(config);
		
		results.errors = []
		
		imap.once('ready', () => {
			
			imap.openBox(folder, false, (err, box) => {
				
				if (err) {
					console.log('saveIMAPMail > err', { err });
					const errorType = ERROR_TYPES.ERROR_OPENING_MAILBOX;
					const errorMessage = 'Ocorreu um erro ao obter as pastas desta caixa de entradas';
					return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
				}

				let date = moment(options.date).isValid() ? new Date(options.date) : new Date(momentNow().format());
				
				imap.append(message, { ...options, date }, (err)=>{

					if (err) {
						const errorType = ERROR_TYPES.ERROR_APPENDING_TO_MAILBOX;
						const errorMessage = 'Ocorreu um erro ao salvar a mensagem';
						return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
					}
					
					results.status = true;
					results.end = true;
					imap.end();
					
					console.log('Done appending message');
					// console.log('Done fetching all message > results', results);
					return response.send({ data: { results }});
					
				})

				return null;

			});

		});
		
		imap.once('error', (err) => {
			console.log('imap > once > error', {err});
			const errorType = (err && err.type==='no' && err.source==='authentication') ? ERROR_TYPES.AUTH_ERROR : ERROR_TYPES.ERROR_ON_APPENDING_TO_MAILBOX;
			const errorMessage = 'Ocorreu um erro ao salvar a mensagem';
			return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
		});
		
		imap.once('end', () => {
			console.log('Connection ended');
			return response.send({ data: { results }});
		});
		
		return imap.connect();

	});
	return null;

}

// grabHereToCopyPaste
const getIMAPMail = async (request, response) => {
	cors(request, response, async () => { 

		const results = {};
		const data = request.body.data;

		if (!data || !data.config) {
			// console.log('onRequest > request', request);
			return null;
		}

		if (!data.config.user || !data.config.password || !data.config.host) {
			return response.send({ data: { ...data, error: 'MISSING_REQUIRED_FIELDS' } });
		}

		console.log('getIMAPMail > data',{ data });

		let { config, config: { user }, folder, range, action } = data;

		let page = data.page||'';
		let perpage = data.perpage||'';

		const simpleParser = require('mailparser').simpleParser;
		const quotedPrintable = require('quoted-printable');
		const utf8 = require('utf8');
		const Imap = require('imap');
		let imap = new Imap(config);
		
		results.errors = []
		
		imap.once('ready', () => {
			
			imap.openBox(folder, true, (err, box) => {
				
				if (err) {
					console.log('getIMAPMail > err', { err });
					const errorType = ERROR_TYPES.ERROR_OPENING_MAILBOX;
					const errorMessage = 'Ocorreu um erro ao obter as pastas desta caixa de entradas';
					return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
				}
				
				const { total, unseen } = box.messages;

				results.total = total;
				results.unseen = unseen||0;
				results.new = box.messages.new||0;

				results.messages = {}

				let lastMsg = `${total}:${total}`;
				let lastRange = perpage ? `${total}:${Math.max(1,total-perpage)}` : lastMsg;
				if ( page && perpage ) {
					let i = perpage*(page-1);
					lastRange = `${Math.max(1,total-i)}:${Math.max(1,total-perpage-i)}`;
				}
				
				let paged = range==='last'?lastRange:range;
				
				console.log('getIMAPMail > paged',{ range, paged });
				
				let f;
				switch (action) {
					case 'read':
						f = imap.seq.fetch(paged, { bodies: ['HEADER.FIELDS (FROM TO CC BCC SUBJECT DATE)','TEXT'] });
					break;			
					case 'list':
					default:
						// f = imap.seq.fetch(paged, { bodies: 'HEADER.FIELDS (FROM TO CC BCC SUBJECT DATE)', struct: true });
						f = imap.seq.fetch(paged, { bodies: ['HEADER.FIELDS (FROM TO CC BCC SUBJECT DATE)','TEXT'] });
					break;
				}

				f.on('message', async (msg, seqno) => {

					console.log('Message #%d', seqno);
					var prefix = '(#' + seqno + ') ';
					
					results.messages[seqno] = {
						seqno,
						info: {},
						buffers: [],
						parsers: []
					};
					
					var raw = '';
					msg.on('body', async (stream, info) => {
						
						const { which, size } = info;
						
						let isBody = which === 'TEXT';

						results.messages[seqno].info[which.split(' ')[0]] = info;

						if ( isBody ) {
							console.log(prefix + 'Body [%s] found, %d total bytes', inspect(info.which), info.size);
							results.messages[seqno].size = size;
						}

						var buffer = '', count = 0;
						stream.on('data', (chunk) => {
							count += chunk.length;
							buffer += chunk.toString('utf8');
							raw += chunk;
							if ( !isBody ) {
								console.log(prefix + 'Header count/size: (%d/%d)', count, info.size);
							}else{
								console.log(prefix + 'Body count/size: (%d/%d)', count, info.size);
							}
						});

						stream.once('end', async () => {
							if ( !isBody ) {
								console.log(prefix + 'Parsed header: %s', inspect(Imap.parseHeader(buffer)));
								results.messages[seqno].header = Imap.parseHeader(buffer);
								results.messages[seqno].rawHeader = buffer;
							} else {
								console.log(prefix + 'Body [%s] Finished', inspect(info.which));
								let body = buffer;
								if ( size < 1000000 ) {
									results.messages[seqno].body = body;
								}
							}
							// results.messages[seqno].buffers.push(buffer);
						});

					});

					msg.once('attributes', (attrs) => {
						// console.log(prefix + 'Attributes: %s', attrs);
						results.messages[seqno].uid = attrs.uid||'';
						results.messages[seqno].attributes = attrs;
					});

					msg.once('end', async () => {
						console.log(prefix + 'Finished');
						results.messages[seqno].raw = raw;
						results.messages[seqno].end = true;
					});

				});
				
				f.once('error', (err) => {
					console.log('getIMAPMail > Fetch > error', {err});

					results.errors.push(err)
					results.error = true;
					
					const errorType = ERROR_TYPES.ERROR_FETCHING_IMAP_EMAILS;
					const errorMessage = 'Ocorreu um erro ao obter as mensagens';
					return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
				})

				f.once('end', async () => {
					
					let promises = isLoopable(results.messages) ? 
						Object.keys(results.messages).map((seqno,i)=>{
							return new Promise(async res=>{
								console.log(`(#${seqno}) simpleParser > rawEmail`);
								// let rawEmail = results.messages[seqno].buffers.reduce((a,b)=>a+b,'');
								const { raw, size } = results.messages[seqno];
								let parsed = await simpleParser(raw);
								
								const shouldUpload = action==='read';
								const { attachments, error } = await getImapAttachments({ raw, seqno, folder, user }, shouldUpload);
								
								if ( size > 1000000 ) results.messages[seqno].raw = '';

								return res({ seqno, parsed, attachments })
							})
						})
						: []
					
					return Promise.all(promises)
					.then(r=>{
						
						r.forEach(p=>{

							let { seqno, parsed, attachments } = p, { text } = parsed, html = text;

							let htmlContent;
							try {
								htmlContent = utf8.decode(quotedPrintable.decode(html));
							} catch (error) {
								htmlContent = quotedPrintable.decode(html);
							}
						
							[/--[^\n\r]*\r?\nContent-Type: text\/html[\s\S]*?\r?\n\r?\n/g, /--[^\n\r]*\r?\nContent-Type: text\/plain[\s\S]*?\r?\n\r?\n/g]
							.forEach(regEx=>{
								let info = regEx.exec(htmlContent)        
								console.log(`(#${seqno}) regEx > info`,info);
								if ( info ) {
									let part = (info[0].match(/--[^\n\r]*\r?/g)||[])[0]
									let content = htmlContent.split(info)[1];
									if ( part && (content.match(part)||[])[0] ) {
										htmlContent = content.split(part)[0];    
									}else{
										let nextInfo = /\n--[^\n\r]*\r?\nContent-Type[\s\S]*?\r?\n\r?\n/g.exec(content)
										htmlContent = content.split(nextInfo[0])[0];
									}
									html = htmlContent;
								}
							})
													
							// results.messages[seqno].parsed = parsed;
							results.messages[seqno].html = html;
							results.messages[seqno].attachments = attachments;
							// results.messages[seqno].html = parsed.textAsHtml;

							// console.log(`(#${seqno}) simpleParser > rawEmail`,inspect(parsed, true, 10, true));
						})
						
						results.end = true;
						imap.end();
						
						console.log('Done fetching all messages');
						// console.log('Done fetching all messages > results', results);
						return response.send({ data: { results }});
						
					})
					
				});

				return null;
			});

		});
		
		imap.once('error', (err) => {
			console.log('imap > once > error', {err});
			const errorType = (err && err.type==='no' && err.source==='authentication') ? ERROR_TYPES.AUTH_ERROR : ERROR_TYPES.ERROR_RETRIEVING_EMAIL;
			const errorMessage = 'Ocorreu um erro ao salvar a mensagem';
			return response.send({ data: { error: errorType, errorMessage, errorType, debug: err, results }})
		});
		
		imap.once('end', () => {
			console.log('Connection ended');
			return response.send({ data: { results }});
		});
		
		return imap.connect();

	});
	return null;

}

// grabHereToCopyPaste
const sendSMTPMail = async (request, response) => {
	cors(request, response, async () => { 

		const data = request.body.data;

		if (!data || !data.config || !data.message) {
			console.log('onRequest > request', request);
			return null;
		}

		console.log('sendSMTPMail > data', { data });

		const { config: { user, pass, name, service, host, port, encryption }, message: { to, cc, bcc, from, subject, html } } = data;

		try {

            if ( !pass || !subject || !html || (!service && !(host && port)) || Boolean(to.split(',').find(e=>!validateEmail(extractEmailAddress(e)))) || !validateEmail(extractEmailAddress(user)) ) {
				return response.send({ data: { ...data, error: 'MISSING_REQUIRED_FIELDS' } });
			}
		
			// nodemailer
			const nodemailer = require('nodemailer');
		
			let formattedFrom = from.match(/>/g) ? from : (name ? `${name} <${user}>` : user);
		
			let message = { to, cc, bcc, from: formattedFrom, subject, html };
		
			if (!cc) delete message.cc;
			if (!bcc) delete message.bcc;

			console.log('sendSMTPMail > message', message);
		
			let config = { 
				// secure: Boolean(encryption.match(/ssl|tls/)), // use SSL
				tls: {
					// do not fail on invalid certs
					rejectUnauthorized: false
				},
				auth: { user, pass }
			};
			
			if ( service ) {
				config = { ...config, service };
			}else{
				config = { ...config, host, port };
			}
		
			console.log('sendSMTPMail > config', config);

			let transporter = nodemailer.createTransport(config);
		
			return transporter.sendMail(message, (err, info) => {
				if (err) {
				console.log('sendSMTPMail > err', { err })
				return response.send({ data: { ...data, error: 'ERROR_SENDING_EMAIL', err, status: false } });
				}
				console.log('sendSMTPMail > info', { info });
				return response.send({ data: { ...info, status: true }});
			});

		} catch (error) {
			console.log('sendSMTPMail > error', { error })
			return response.send({ data: { ...data, error: 'ERROR_SENDING_EMAIL', err: error, status: false } });		
		}


	});
	return null;

}

module.exports = {
    handleMailboxesUpdate, 
    handleMailboxesCreate, 
    handleMailboxesEmails, 
    checkMailboxAvailability, 
    getCloudMailboxes, 
    getIMAPFolders, 
    moveIMAPMail, 
    saveIMAPMail, 
    getIMAPMail, 
    sendSMTPMail, 
}