const {
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
} = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const moment = require("moment");
const axios = require("axios");
const dotenv = require("dotenv");

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Função principal para processar e enviar mensagens do Redis para o chat-api
 * @param {Object} options - Opções de processamento
 * @returns {Promise<Array>} - Lista de mensagens processadas
 */
const shotxSendMessages = async (options = {}) => {
  try {
    // Processar as mensagens agendadas
    const processedMessages = await processScheduledMessages(options);

    console.log(
      `SHOTXCRON > SHOTX SEND MESSAGES > Processadas ${processedMessages.length} mensagens`
    );

    return processedMessages;
  } catch (error) {
    console.error(
      "SHOTXCRON > SHOTX SEND MESSAGES > Erro no processamento:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Busca e imprime as mensagens agendadas do Redis
 * @returns {Promise<Array>} Lista de mensagens
 */

/**
 * Envia mensagens para o chat-api usando axios
 * @param {Array} messages - Array de mensagens a serem enviadas
 * @returns {Promise<Object>} - Resultado do envio contendo status, dados e informações de sucesso/erro
 */
const sendMessageToChatApi = async (messages) => {
  try {
    // Usar a variável de ambiente CHAT_API_URL ou um valor padrão
    const chatApiUrl = `${process.env.CHAT_API_URL}/shotx/broadcast/send`;

    console.log(`SHOTXCRON > SEND MESSAGE > Using Chat API URL: ${chatApiUrl}`);

    const payload = {
      messages: messages,
    };

    console.log(
      `SHOTXCRON > PREPARANDO ENVIO DE MENSAGENS > SENDING ${messages.length} MESSAGES`
    );

    try {
      // Enviar a mensagem usando axios
      console.log(
        `SHOTXCRON > SEND MESSAGE > Enviando mensagem para ${chatApiUrl}`
      );

      const response = await axios.post(chatApiUrl, payload, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 30000, // 30 segundos de timeout
      });

      console.log(
        `SHOTXCRON > SEND MESSAGE > Resposta recebida: Response`,
        response
      );

      return {
        success: true,
        status: response,
        data: response,
      };
    } catch (axiosError) {
      console.error(
        `SHOTXCRON > SEND MESSAGE > Erro ao enviar mensagem:`,
        axiosError.message
      );

      // Verificar se há resposta de erro do servidor
      if (axiosError.response) {
        console.error(
          `SHOTXCRON > SEND MESSAGE > Status de erro: ${axiosError.response.status}`
        );
        console.error(
          `SHOTXCRON > SEND MESSAGE > Dados de erro: ${JSON.stringify(axiosError.response.data, null, 2)}`
        );
      }

      return {
        success: false,
      };
    }
  } catch (error) {
    console.error(
      `SHOTXCRON > SEND MESSAGE > Error in message simulation:`,
      error.message
    );

    return {
      success: false,
      error: error.message,
      simulated: true,
      message_id: messages,
    };
  }
};

/**
 * Processa mensagens agendadas do Redis e envia para o chat-api
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Número máximo de mensagens a processar por vez (default: 50)
 * @param {boolean} options.dryRun - Se true, não envia as mensagens, apenas simula (default: true)
 * @param {boolean} options.simulateOnly - Se true, apenas simula o envio com logs detalhados (default: true)
 * @returns {Promise<Array>} Lista de mensagens processadas
 */
const processScheduledMessages = async (options = {}) => {
  // Definir opções padrão - simulação ativada por padrão
  const {
    batchSize = 50,
    dryRun = false, // Alterado para true por padrão
    simulateOnly = false, // Nova opção para apenas simular o envio
  } = options;

  console.log(
    `SHOTXCRON > PROCESS MESSAGES > START > Modo: ${simulateOnly ? "SIMULAÇÃO" : "PRODUÇÃO"}, DryRun: ${dryRun ? "SIM" : "NÃO"}`
  );

  try {
    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "shotx:scheduled_messages";

    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();

    //Obtem as mensagens agendadas no Redis
    const messages = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false,
      }
    );

    if (messages.length === 0) {
      console.log("SHOTXCRON > PROCESS MESSAGES > NO MESSAGES");
      return [];
    }

    const totalMessages = messages.length;
    console.log(
      `SHOTXCRON > PROCESS MESSAGES > TOTAL MESSAGES ${totalMessages} TO PROCESS`
    );

    let messageIndex = 0;
    // Processar mensagens em paralelo para melhor performance
    let messagesProcesseds = [];
    const messagesProcess = messages.map(async (message) => {
      let results = {};
      try {
        const messageId =
          message.id ||
          `msg_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

        // Registrar a mensagem no Firestore antes de enviar
        await FirestoreRef.collection("shotx_messages_sent")
          .doc(messageId)
          .set({
            ...message,
            _processing_started_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            status: "processing",
            _dry_run: dryRun,
          });

        // Se for modo de simulação, não enviar realmente
        if (dryRun) {
          console.log(
            `SHOTXCRON > PROCESS MESSAGES > [DRY RUN] Would send message ${messageId}`
          );

          // Atualizar status no Firestore
          await FirestoreRef.collection("shotx_messages_sent")
            .doc(messageId)
            .update({
              status: "simulated",
              _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
            });

          // Usar a redis_key da mensagem para remoção correta
          if (message.redis_key) {
            await removeMessage(message.redis_key, "shotx:scheduled_messages");
          }
          return { ...message, id: messageId, status: "simulated" };
        }

        messagesProcesseds.push(message);

        messageIndex += 1;
        console.log(
          `SHOTXCRON > PROCESS MESSAGES > BEFORE >PROCESSING MESSAGE ${messageIndex} OF ${totalMessages}`
        );
        if (messageIndex === totalMessages) {
          console.log(
            `SHOTXCRON > PROCESS MESSAGES > IF COMPARE MATCHED - SENDING MESSAGES`
          );
          results = await sendMessageToChatApi(messagesProcesseds);

          if (results && results.success) {
            console.log(`SHOTXCRON > PROCESS MESSAGES > ENVIO BEM-SUCEDIDO`);

            // Remover cada mensagem do Redis usando a redis_key
            // for (const msg of messagesProcesseds) {
            //   if (msg.redis_key) {
            //     const success = await removeMessage(
            //       msg.redis_key,
            //       "shotx:scheduled_messages"
            //     );
            //     if (success) {
            //       console.log(
            //         `SHOTXCRON > PROCESS MESSAGES > MENSAGEM ${msg.redis_key} REMOVIDA DO REDIS`
            //       );
            //     } else {
            //       console.error(
            //         `SHOTXCRON > PROCESS MESSAGES > ERRO AO REMOVER MENSAGEM ${msg.redis_key} DO REDIS`
            //       );
            //     }
            //   }
            // }
          } else {
            console.warn(
              `SHOTXCRON > PROCESS MESSAGES > FALHA NO ENVIO - MENSAGENS MANTIDAS NO REDIS`
            );
          }
        }
        return results;
      } catch (error) {
        console.error(
          `SHOTXCRON > ERROR TO SEND MESSAGES > ERROR:`,
          error.message
        );
        return null;
      }
    });

    return messagesProcess;
  } catch (error) {
    console.error(
      "SHOTXCRON > PROCESS MESSAGES > Fatal error during processing:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Limpa todas as mensagens agendadas do Redis
 * @returns {Promise<number>} - Número de mensagens removidas
 */
const clearAllMessages = async () => {
  try {
    console.log(
      "SHOTXCRON > CLEAR ALL MESSAGES > Iniciando limpeza de mensagens"
    );

    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "shotx:scheduled_messages";

    // Remover todas as mensagens
    const removedCount = await removeAllMessages(scheduledListKey);

    console.log(
      `SHOTXCRON > CLEAR ALL MESSAGES > Removidas ${removedCount} mensagens`
    );

    return removedCount;
  } catch (error) {
    console.error("SHOTXCRON > CLEAR ALL MESSAGES > ERROR:", error);
    return 0;
  }
};

/**
 * Remove uma mensagem específica pelo ID
 * @param {string} messageId - ID da mensagem a ser removida
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const deleteMessage = async (messageId) => {
  try {
    if (!messageId) {
      console.error(
        "SHOTXCRON > DELETE MESSAGE > ID da mensagem é obrigatório"
      );
      return false;
    }

    // Formatar a chave da mensagem
    const messageKey = messageId.startsWith("shotx:")
      ? messageId
      : `shotx:message:${messageId}`;

    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "shotx:scheduled_messages";

    // Remover a mensagem
    const success = await removeMessage(messageKey, scheduledListKey);

    if (success) {
      console.log(
        `SHOTXCRON > DELETE MESSAGE > Mensagem ${messageId} removida com sucesso`
      );
    } else {
      console.error(
        `SHOTXCRON > DELETE MESSAGE > Falha ao remover mensagem ${messageId}`
      );
    }

    return success;
  } catch (error) {
    console.error(`SHOTXCRON > DELETE MESSAGE > ERROR:`, error);
    return false;
  }
};

module.exports = {
  shotxSendMessages,
  processScheduledMessages,
  sendMessageToChatApi,
  clearAllMessages,
  deleteMessage,
};
