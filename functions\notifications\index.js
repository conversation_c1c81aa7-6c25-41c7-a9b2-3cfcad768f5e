const { areEqualObjects, replaceUndefined } = require('../helpers');
const { functions, debug, ROLES, CONSTANTS, COLLECTIONS, FirestoreRef, helpers, moment, momentNow, langMessages, models } = require('../init');
const { isLoopableObject } = helpers;
const { fetchPost, addNewPost, deletePost, deleteCollectionPosts, updateRef } = require('../post');

const {
	CRONJOB_TYPES,
	MOMENT_ISO,
} = CONSTANTS;


let notificationsHandler = {};
let handleNotificationsTimeout;

let deadlineHandler = {};
let handleDeadlinesTimeout;

// grabHereToCopyPaste
const handleNotifications = ((sanitizedData, change, context) => {
	console.log(`handleNotifications`);

    const { before, after } = change;
    const { params } = context;

	const oldData = (before.exists && before.data()) || {};
	const newData = sanitizedData || {};
    
	const collection = params.collection;
	const docId = params.docId;
    const docRef = after.exists && change.after.ref;
	
	if ( !Array.isArray(newData.notifications) && !Array.isArray(oldData.notifications)) {
        return null;
    }
	
	return new Promise((resolve,reject)=>{
		
		if (notificationsHandler[`${collection}/${docId}`]) {
			clearTimeout(handleNotificationsTimeout)
		}

		handleNotificationsTimeout= setTimeout(() => {

			notificationsHandler[`${collection}/${docId}`] = true;
            
            /* -------------------------------------------------------------
                - removedNotifs works for deleted posts
                - deletedNotifs works for deleted notifications in existing posts
                - changedNotifs deletes prev cronjob and creates a new one
                - addedNotifs creates a new cronjob
            // ------------------------------------------------------------- */ 

			let oldNotifs = (Array.isArray(oldData.notifications) && oldData.notifications.map((n,index)=>({ ...n, index }))) || [];
			let newNotifs = (Array.isArray(newData.notifications) && newData.notifications.map((n,index)=>({ ...n, index }))) || [];
            let removedNotifs = oldNotifs.filter(o=>Boolean(o.cronId) && !newNotifs.find(n=>n.cronId===o.cronId));
			let notifications = (Array.isArray(newData.notifications) && newData.notifications.filter(n=>!n.deleted).map((n,index)=>({ ...n, index }))) || [];
            
            let SaoPauloISO = momentNow().format(MOMENT_ISO)
            let SaoPauloTime = moment(SaoPauloISO).valueOf();
            
            const deletedNotifs = newNotifs.filter(n=>{
                let newMoment = getScheduledMoment(n, newData)
				let newTime = newMoment && newMoment.valueOf()
                return n.cronId && (n.deleted||newTime<SaoPauloTime)
            });
			const changedNotifs = notifications.filter(n=>{
				let old = Boolean(n.cronId) && oldNotifs.find(o=>o.cronId===n.cronId )
				let oldMoment = getScheduledMoment(old, oldData)
				let newMoment = getScheduledMoment(n, newData)
				let oldTime = oldMoment && oldMoment.valueOf()
				let newTime = newMoment && newMoment.valueOf()
				return !n.deleted && old && oldTime!==newTime && !deletedNotifs.find(d=>d.cronId===n.cronId);
            });
            const addedNotifs = notifications.filter(n=>{
                let newMoment = getScheduledMoment(n, newData)
                // debug("********MOMENT*********", { SaoPauloISO, SaoPauloTime, newMoment: newMoment.valueOf() })
                return !n.deleted && !n.cronId && n.qiuser && newMoment && newMoment.valueOf()>SaoPauloTime
            });

            // debug('******** NOTIFICATIONS *********', { removedNotifs, deletedNotifs, addedNotifs, changedNotifs });
            
            const deleteCronJobs = () => {
				return new Promise((res,rej)=>{
                    let cronIds = [ ...new Set([...changedNotifs, ...deletedNotifs, ...removedNotifs].map(n=>n.cronId)) ];
                    if ( cronIds.length ) {
                        return Promise.all( cronIds.map(cronId=>deletePost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, cronId)) )
                        .then(r=>res({ notifications }))
                        .catch(err=>console.error(err)||rej(err))
                        
                    }else return res({ notifications })
				})
			}

			const addCronJobs = () => {
				return new Promise((res,rej)=>{
					const newJobs = [...changedNotifs, ...addedNotifs]
					.filter(n=>Boolean(getScheduledMoment(n, newData)))
					.map(n=>{
						let scheduledMoment = getScheduledMoment(n, newData)
						return {
							executed: false,
							execution_date: "",
							scheduled_date: scheduledMoment.format(MOMENT_ISO),
							date: momentNow().format(MOMENT_ISO),
							modified: momentNow().format(MOMENT_ISO),
							type: CRONJOB_TYPES.DESKTOP_NOTIFICATION,
							data: {
								scheduled_date: scheduledMoment.format(MOMENT_ISO),
								index: n.index,
								qiuser: n.qiuser,
								title: n.title || newData.title || langMessages["notifications.title"],
								message: n.message||'',
								url: n.url||`/${collection}/${docId}`
							},
							context: {
								collection,
								id: docId,
								operator_id: n.qiuser,
							}
						}
					})

					if (newJobs.length) {
                        return Promise.all(newJobs.map(cronData=>addNewPost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, cronData)))
                        .then((cronJobs)=>{
                            cronJobs.forEach((newCron,i)=>{
                                const { data: { index } } = newCron;
                                notifications[index].cronId = newCron.ID
                            })
                            // console.log('newJobs > notifications',notifications);				
                            return res({ cronJobs, notifications })
                        })
                        .catch(err=>console.error(err)||rej(err))

                    }else return res({ cronJobs: [], notifications })

				})
			}
	
            return deleteCronJobs()
			.then(({ notifications })=>{
                return addCronJobs()
			})
			.then(({ cronJobs, notifications })=>{
                if ( JSON.stringify(notifications)!==JSON.stringify(newNotifs) ) {
                    console.log('newNotifs', newNotifs);
                    console.log('notifications', notifications);
					return resolve(updateRef(docRef, { notifications }))
				}
				return resolve(null);
			})
			.catch(err=>{
				console.error(err)
				return resolve(err)
			})

		}, 1000 );	
	})

})

// grabHereToCopyPaste
const handleDeadlines = ((sanitizedData, change, context) => {
	console.log(`handleDeadlines`);

    const { before, after } = change;
    const { params } = context;

	const oldData = (before.exists && before.data()) || {};
	const newData = sanitizedData || {};

	const collection = params.collection;
	const docId = params.docId;
	const docRef = after.exists ? after.ref : null;
	
	if ( !isLoopableObject(newData.deadline) && !isLoopableObject(oldData.deadline) ) {
        return null;
	}
	
	let deadline = (isLoopableObject(newData.deadline) && newData.deadline) || {};
	let oldDeadline = (isLoopableObject(oldData.deadline) && oldData.deadline) || null;
    
	if ( areEqualObjects(newData.deadline, oldData.deadline) ) {
		console.log('no updates to deadline');
		return null;
	}

    const { owner, accountId } = { ...oldData, ...newData };

	return new Promise((resolve,reject)=>{
			
        let newMoment = getScheduledMoment(deadline, newData)
        let oldMoment = getScheduledMoment(oldDeadline, oldData)
        let nTime = newMoment && newMoment.valueOf()
        let oTime = oldMoment && oldMoment.valueOf()

        let addCron = Boolean(!deadline.deleted && getScheduledMoment(deadline, newData) && (!deadline.cronId || nTime!==oTime));
        let removeCron = Boolean(oldDeadline && oldDeadline.cronId && (deadline.deleted || nTime!==oTime));
        
        console.log('handleDeadlines > addCron', addCron);
        console.log('handleDeadlines > removeCron', removeCron);
        console.log('handleDeadlines > nTime', nTime);
        console.log('handleDeadlines > oTime', oTime);
        console.log('handleDeadlines >  -------------- ');	
        
        const addCronJob = () => {
            return new Promise((res,rej)=>{
                let scheduledMoment = newMoment;

                if ( addCron ) {                        
                    const newJob = replaceUndefined({
                        executed: false,
                        execution_date: "",
                        scheduled_date: scheduledMoment.format(MOMENT_ISO),
                        date: momentNow().format(MOMENT_ISO),
                        modified: momentNow().format(MOMENT_ISO),
                        type: CRONJOB_TYPES.DESKTOP_NOTIFICATION,
                        data: {
                            scheduled_date: scheduledMoment.format(MOMENT_ISO),
                            qiuser: deadline.qiuser,
                            title: deadline.title || newData.title || langMessages["notifications.title"],
                            message: deadline.message||'',
                            url: deadline.url||`/${collection}/${docId}`
                        },
                        context: {
                            collection,
                            id: docId,
                            operator_id: deadline.qiuser,
                        }
                    })
                    
                    return addNewPost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, newJob)
                    .then(async (cronJob)=>{
                        console.log('handleDeadlines > cronJob',cronJob);
                        try {
                            const eventModel = models.calendar;
                            const newEvent = replaceUndefined({
                                ...eventModel,
                                title: `${langMessages["texts.deadline"]}: ${deadline.title || newData.title || langMessages["notifications.title"]}`,
                                content: deadline.message||'',
                                start: scheduledMoment.format(MOMENT_ISO),
                                end: scheduledMoment.format(MOMENT_ISO),
                                date: momentNow().format(MOMENT_ISO),
                                modified: momentNow().format(MOMENT_ISO),
                                locale: newData.locale || CONSTANTS.DEFAULT_LOCALE,
                                author: deadline.qiuser,
                                qiuserId: deadline.qiuser,
                                deal: collection===COLLECTIONS.DEALS_COLLECTION_NAME ? docId : "",
                                owner,
                                accountId,
                                contactId: newData.contactId||"",
                                contact: newData.contact||eventModel.contact,
                                config: {
                                    ...eventModel.config,
                                    removable: false,
                                    editable: false
                                },
                                context: {
                                    ...eventModel.context,
                                    collection,
                                    id: docId,
                                    operator_id: deadline.qiuser,
                                    cronId: cronJob.ID
                                }
                            })
                                                        
                            const event = await addNewPost(COLLECTIONS.CALENDAR_COLLECTION_NAME, newEvent);
                            console.log('handleDeadlines > event',event);
                        } catch (error) {
                            console.error(error);
                        }
                        deadline.cronId = cronJob.ID
                        return res({ deadline })
                    })
                    .catch(err=>console.error(err)||rej(err))

                } else return res({ deadline })

            })
        }
        
        const deleteCronJob = () => {
            return new Promise((res,rej)=>{

                if (removeCron) {
                    let newDeadLine = { ...deadline, cronId: "" };
                    let { cronId } = oldDeadline;
                    return deletePost(COLLECTIONS.CRONJOBS_COLLECTION_NAME, cronId)
                    .then(r=>{
                        const where = []
                        where.push(["context.cronId","==",cronId])
                        return deleteCollectionPosts(COLLECTIONS.CALENDAR_COLLECTION_NAME, where)
                    })
                    .then(r=>{
                        return res({ deadline: newDeadLine });
                    })
                    .catch(err=>{
                        console.error(err)
                        return res({ deadline: newDeadLine });
                    })
                }
                
                return res({ deadline })
            })
        }

        return deleteCronJob()
        .then(({ deadline })=>{
            if (addCron) return addCronJob();
            return { deadline };
        })
        .then(({ deadline })=>{
            if (addCron || removeCron) {
                console.log('handleDeadlines > updated deadline', deadline);
                return updateRef(docRef, { deadline })
            }
            return null
        })
        .then(r=>{
            return resolve(null);
        })
        .catch(err=>{
            console.error(err)
            return resolve(err)
        })

	})

})

// grabHereToCopyPaste
const notificationCron = () => {

    const fetchJobs = () => {
        return new Promise((res)=>{
            let now = momentNow().format(MOMENT_ISO)
            return FirestoreRef.collection(COLLECTIONS.CRONJOBS_COLLECTION_NAME).where("type","==",CRONJOB_TYPES.DESKTOP_NOTIFICATION).where("executed","==",false).where("scheduled_date","<=",now).get().then(snapshot=>{
                let jobs = []
                snapshot.forEach(doc=>{
                    const job = doc.data();
                    if ((job.data||{}).qiuser) jobs.push(job)
                    doc.ref.update({ executed: true, execution_date: momentNow().format(MOMENT_ISO) })
                })
                return res({ jobs })
            })
        });
    }

    const fetchUsers = ({ jobs, qiuserIds }) => {
        return new Promise((res,rej)=>{
            const promises = []
            
            qiuserIds.forEach(qId=>{
                promises.push(new Promise(resPost=>fetchPost(COLLECTIONS.QIUSERS_COLLECTION_NAME, qId).then(resPost).catch(err=>console.log('notificationCron > fetchPost > err',{ qId, err})||resPost(null))))
            })
            
            if ( promises.length ) {				
                return Promise.all(promises).then(results=>{					
                    return res({ 
                        jobs, 
                        qiusers: results.filter(r=>r && r.collection===COLLECTIONS.QIUSERS_COLLECTION_NAME)
                    })
                })
            }

            return res({ jobs, qiusers: [] })
            
        })
    }

    const fireActions = ({ jobs, qiusers }) => {

        return new Promise((res,rej)=>{

            let promises = []
            
            jobs.forEach(job=>{
                const { data } = job;
                const qiuser = qiusers.find(c=>c.ID===data.qiuser);
                const { title, message, url, scheduled_date, context } = data;
                if ( qiuser ) {
                    const newPost = {
                        owner: qiuser.ID,
                        qiuser: qiuser.ID,
                        accountId: qiuser.accountId,
                        title,
                        message,
                        url,
                        context,
                        viewed: false,
                        scheduled_date,
                        date: momentNow().format(MOMENT_ISO),
                        modified: momentNow().format(MOMENT_ISO),
                    }
                    promises.push(addNewPost(COLLECTIONS.DESKTOP_NOTIFICATIONS_COLLECTION_NAME, newPost))
                }
            })

            if ( promises.length ) {
                return Promise.all(promises)
                .then(results=>{
                    return res({ jobs, qiusers, results });
                })
                .catch(err=>{
                    console.error(err)
                    return res({ jobs, qiusers, err });
                })
            }
            
            return res(null)
        })

    }

    return fetchJobs()
    .then(({ jobs })=>{
        // console.log('fetchJobs > jobs',jobs && jobs.length);
        if (jobs && jobs.length) {
            let qiuserIds = [...new Set(jobs.filter(j=>j.data.qiuser).map(j=>j.data.qiuser))]
            return fetchUsers({ jobs, qiuserIds })
        }
        return { jobs, qiusers: [] }
    })
    .then(({ jobs, qiusers })=>{
        // console.log('fetchAutomations > qiusers'.length);
        if ( qiusers.length ) {
            return fireActions({ jobs, qiusers })			
        }
        return { jobs, qiusers }
    })
    .then(({ jobs, qiusers })=>{
        // console.log('fetchAutomations > qiusers'.length);
        return null;
    })
    .catch(error=>{
        console.error(error);
        return null;
    })

}

// grabHereToCopyPaste
const getScheduledMoment = (n, data) => {
    data = data||{}
    let scheduled_date;
    let { qty, interval } = (n||{});
    if ( n && n.type) {
        switch (n.type) {
            case 'timestamp':
                scheduled_date = n.time && moment(n.time).isValid() ? moment(n.time) : "";
                break;
            case 'before':
                var start = data.start || data.date;
                scheduled_date = qty && start && moment(start).isValid() ? moment(start) : "";
                if ( !isNaN(parseInt(qty,10)) && scheduled_date) {
                    switch (interval) {
                        case 'dd':
                            scheduled_date.subtract(parseInt(qty,10),'days')
                            break;
                        case 'hh':
                            scheduled_date.subtract(parseInt(qty,10),'hours')
                            break;
                        case 'mm':
                            scheduled_date.subtract(parseInt(qty,10),'minutes')
                            break;
                        default:
                            break;
                    }
                }
                break;
            case 'oncreate':
                var date = data.date;
                scheduled_date = qty && date && moment(date).isValid() ? moment(date) : "";
                if ( !isNaN(parseInt(qty,10)) && scheduled_date) {
                    switch (interval) {
                        case 'dd':
                            scheduled_date.add(parseInt(qty,10),'days')
                            break;
                        case 'hh':
                            scheduled_date.add(parseInt(qty,10),'hours')
                            break;
                        case 'mm':
                            scheduled_date.add(parseInt(qty,10),'minutes')
                            break;
                        default:
                            break;
                    }
                }
                break;
            default:
                break;
        }        
    }
    return scheduled_date;
}

module.exports = { 
    handleNotifications,
    handleDeadlines,
    notificationCron,
    getScheduledMoment
}