const { COLLECTIONS } = require("../init");
const { fetchCollection } = require("../post");

const getQuickMessages = async (accountId, messageId) => {
  const messageCollection = `${COLLECTIONS.SHOTX_COLLECTION_NAME}/${accountId}/${COLLECTIONS.SHOTX_QUICK_MESSAGES_COLLECTION_NAME}`;

  return await fetchCollection(messageCollection, [
    ["id", "==", messageId],
  ]).then((res) => res[0]);
};

module.exports = {
  getQuickMessages,
};
