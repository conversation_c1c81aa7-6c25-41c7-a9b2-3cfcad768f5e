/**
 * App Strings
 */

let userLang = 'pt';

const strings = {
	pt: {
		"items.price": "Valor",
		"items.product": "Produto",
		"items.qty": "Unidades",
		"forms.maritalStatus": "Estado Civil",
		"forms.maritalStatus.single": "Solteiro/a",
		"forms.maritalStatus.married": "Casado/a",
		"forms.maritalStatus.widowed": "Viúvo/a",
		"forms.maritalStatus.divorced": "Divorciado/a",
		"forms.maritalStatus.other": "Outro",
		"forms.gender.masc": "Masculino",
		"forms.gender.fem": "Feminino",
		"forms.type.individual": "Pessoa Física",
    	"forms.type.corporation": "Empresa",
		"contracts.defaultSubject": "QIPlus || Contrato",
    	"email.defaultSubject": "QIPlus || Email",
		"errors.invalidRecipients": "Destinatário inválido",
		"mail.errors.emptyRecipients": "Email sem Destinatários",
		"mail.errors.invalidRecipients": "Destinatário inválido",
		"mail.errors.invalidBody": "O conteúdo do email não pode ser nulo",
		"mail.errors.invalidFromName": "O nome do remetente não pode ser nulo",
		"notifications.title": "Alerta programado",
		"texts.deadline": "Deadline",
		"mailboxes.unavailableEmailAddress": "O endereço de email não está disponível",
		"nf.apiError": "Ocorreu um erro ao gerar a Nota Fiscal",
		"payment.subscription": "Assinatura",
    	"payment.implementation": "Implementação",
	}
}

const i18nLabels = {
	items: {
		price: strings[userLang]["items.price"],
		product: strings[userLang]["items.product"],
		qty: strings[userLang]["items.qty"],
	},
	maritalStatus: {
		single: strings[userLang]["forms.maritalStatus.single"],
		married: strings[userLang]["forms.maritalStatus.married"],
		widowed: strings[userLang]["forms.maritalStatus.widowed"],
		divorced: strings[userLang]["forms.maritalStatus.divorced"],
		other: strings[userLang]["forms.maritalStatus.other"],
	},
	gender: {
		masc: strings[userLang]["forms.gender.masc"],
		fem: strings[userLang]["forms.gender.fem"]
	},
	type: {
		individual: strings[userLang]["forms.type.individual"],
		corporation: strings[userLang]["forms.type.corporation"],
	}
}

module.exports = {
    strings,
    i18nLabels
}