ENV="default";
ONLY="functions";

while getopts e:o: option
do
case "${option}"
in
e) ENV=${OPTARG};;
o) ONLY=${OPTARG};;
esac
done

if [ $ENV == "p" ] || [ $ENV == "prod" ] || [ $ENV == "production" ]
then
    ENV="production";
else
    ENV="default";
fi

echo "ENV: $ENV";
echo "ONLY: $ONLY";

cd functions;
firebase login;
firebase use $ENV;
firebase deploy --only $ONLY;

# if [ $2 ]
# then
# echo "Deploying: $2";
# firebase deploy --only $2;
# else
# echo "Deploying functions";
# firebase deploy --only functions;
# fi
