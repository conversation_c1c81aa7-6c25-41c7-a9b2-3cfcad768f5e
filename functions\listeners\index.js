const {
  ROLES,
  CONSTANTS,
  COLLECTIONS,
  FirestoreRef,
  helpers,
  moment,
  momentNow,
  langMessages,
} = require("../init");
const { fetchSubCollection, addNewLog, fetchPost } = require("../post");
const { isLoopable, isLoopableObject } = helpers;
const { doActions, evaluateAutomationTriggers } = require("../automations");
const DATA_ORIGINS = require("../constants/origins");

const { APP_TRIGGERS, MOMENT_ISO } = CONSTANTS;

const { areEqualObjects, diffObjects } = helpers;

let funcCounter = 0;

// grabHereToCopyPaste
const handleChecklists = (sanitizedData, change, context) => {
  console.log(`handleChecklists`);

  const { before, after } = change;
  const { params } = context;

  const oldData = (before.exists && before.data()) || {};
  const newData = sanitizedData || {};

  const collection = params.collection;
  const contactId = newData.contactId || oldData.contactId;
  const docId = params.docId;

  if (!contactId || !isLoopable(newData.tasks)) {
    return null;
  }

  let newTasklists, oldTasklists;
  if (collection === COLLECTIONS.TASKLISTS_COLLECTION_NAME) {
    newTasklists = {
      [docId]: newData.tasks,
    };
    oldTasklists = {
      [docId]: oldData.tasks || [],
    };
  } else if (isLoopableObject(newData.tasks)) {
    newTasklists = newData.tasks;
    oldTasklists = oldData.tasks || {};
  }

  // console.log('newTasklists',newTasklists);
  // console.log('oldTasklists',oldTasklists);

  let promises = [];

  if (isLoopableObject(newTasklists))
    Object.keys(newTasklists).forEach((checklistId, i) => {
      const oldTasks = oldTasklists[checklistId] || [];
      const tasks = newTasklists[checklistId];

      if (Array.isArray(tasks)) {
        let completedBefore =
          oldTasks.filter((l) => l.completed).length === oldTasks.length;
        let completedAfter =
          tasks.filter((l) => l.completed).length === tasks.length;

        console.log("completedBefore", completedBefore);
        console.log("completedAfter", completedAfter);

        if (
          completedAfter &&
          (!completedBefore || oldTasks.length !== tasks.length)
        ) {
          let triggerName = APP_TRIGGERS.APP_TRIGGER_COMPLETED;

          // LOGS
          const logKeys = [
            CONSTANTS.DATE_FIELD,
            CONSTANTS.MODIFIED_FIELD,
            ...CONSTANTS.RELATIONAL_FIELDS,
          ];
          const logData = {};

          logKeys.forEach((key) => {
            if (key in newData) {
              logData[key] = newData[key];
            } else if (!(key in oldData)) {
              logData[key] = oldData[key];
            }
          });

          promises.push(
            addNewLog({
              contactId,
              user_id: contactId,
              operator_id: 0,
              id: checklistId,
              collection: COLLECTIONS.TASKLISTS_COLLECTION_NAME,
              trigger: triggerName,
              date: momentNow().format(MOMENT_ISO),
              owner: logData.owner,
              accountId: logData.accountId,
              data: logData,
              context: {
                id: docId,
                collection,
                operator_id: 0,
              },
            })
          );
        }
      }
    });

  if (promises.length) {
    return Promise.all(promises);
  }

  return null;
};

// grabHereToCopyPaste
const evaluateListeners = async (
  accountId,
  listeners,
  docId,
  docCollection,
  contactId,
  id,
  collection,
  trigger,
  logUpdated
) => {
  console.log(`EVALUATE_LISTENERS > START:`, {
    accountId,
    listeners,
    docId,
    docCollection,
    contactId,
    id,
    collection,
    trigger,
    logUpdated,
  });

  let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);

  let activeListeners = listeners.filter((l) => {
    if (
      l.start &&
      moment(l.start).isValid() &&
      new Date(l.start).getTime() > new Date(momentNowISO).getTime()
    ) {
      return false;
    }
    if (
      l.end &&
      moment(l.end).isValid() &&
      new Date(l.end).getTime() < new Date(momentNowISO).getTime()
    ) {
      return false;
    }
    return true;
  });

  const fetchAutomations = async () => {
    return new Promise((res, rej) => {
      if (activeListeners.length) {
        let promises = [];
        const queries = [];

        activeListeners.forEach((l) => {
          const { context } = l;
          if (
            !queries.find(
              (q) => q.collection === context.collection && q.id === context.id
            )
          ) {
            queries.push({ collection: context.collection, id: context.id });
          }
        });

        promises = queries.map((q) => fetchPost(q.collection, q.id));

        return Promise.all(promises).then((results) => {
          console.log(`FETCHAUTOMATIONS > FETCH_POST:`, {
            results,
          });
          let automations = results.filter((a) => {
            let isExpired = false;
            if (
              a.start &&
              moment(a.start).isValid() &&
              new Date(a.start).getTime() > new Date(momentNowISO).getTime()
            ) {
              isExpired = true;
            }
            if (
              a.end &&
              moment(a.end).isValid() &&
              new Date(a.end).getTime() < new Date(momentNowISO).getTime()
            ) {
              isExpired = true;
            }
            return (
              !isExpired &&
              Array.isArray(a.actions) &&
              a.actions.length &&
              a.status === CONSTANTS.PUBLISH_STATUS
            );
          });
          console.log(`FETCHAUTOMATIONS > AUTOMATIONS:`, {
            automations,
          });
          return res(automations);
        });
      }
      return res([]);
    });
  };

  const evaluateAutomations = ({ automations, deal }) =>
    new Promise(async (resolveAll) => {
      console.log(`EVALUATE_LISTENERS > EVALUATE_AUTOMATIONS:`);
      let results = await Promise.all(
        automations.map(
          (automation) =>
            new Promise(async (res) => {
              try {
                let result = await evaluateAutomationTriggers(
                  automation,
                  contactId,
                  deal,
                  logUpdated
                );
                console.log(
                  `EVALUATE_AUTOMATIONS > EVALUATE_AUTOMATION_TRIGGERS > RESULT`,
                  { result }
                );
                return res(result ? automation : null);
              } catch (err) {
                console.log(
                  "EVALUATE_AUTOMATIONS > EVALUATE_AUTOMATION_TRIGGERS > ERROR",
                  err
                );
                return res(null);
              }
            })
        )
      );
      return resolveAll(results.filter((r) => r));
    });

  const fireActions = ({ contact, deal, evaluatedAutomations }) => {
    console.log(`FIREACTIONS > START:`);
    return new Promise(async (res, rej) => {
      let promises = evaluatedAutomations.map((automation) =>
        doActions({ contact, deal, automation })
      );

      if (promises.length) {
        return Promise.all(promises)
          .then((results) => {
            return res({ contact, evaluatedAutomations, results });
          })
          .catch((err) => {
            console.error(err);
            return res({ contact, evaluatedAutomations, err });
          });
      }

      return res({ contact, evaluatedAutomations, err: true });
    });
  };

  const logs = ({ contact, evaluatedAutomations, results, err }) => {
    console.log(`EVALUATE_LISTENERS > LOGS > RESULTS:`, { results });

    return new Promise((res, rej) => {
      let promises = evaluatedAutomations.map((automation) =>
        addNewLog({
          contactId,
          user_id: contactId,
          operator_id: 0,
          id: automation.ID,
          collection: COLLECTIONS.AUTOMATIONS_COLLECTION_NAME,
          trigger: APP_TRIGGERS.APP_TRIGGER_FIRED,
          date: momentNow().format(MOMENT_ISO),
          owner: automation.owner || CONSTANTS.ORPHANS_OWNER,
          accountId: automation.accountId || CONSTANTS.ORPHANS_ACCOUNT,
          data: {
            listeners,
            results: Boolean(results),
            err: Boolean(err),
          },
        })
      );

      if (promises.length) {
        return Promise.all(promises)
          .then((logs) => {
            return res({ contact, evaluatedAutomations, logs });
          })
          .catch((err) => {
            console.error(err);
            return res({ contact, evaluatedAutomations, err });
          });
      }

      return res({ contact, evaluatedAutomations, err: Boolean(err) });
    });
  };

  let contact, automations, evaluatedAutomations, deal;

  try {
    console.log("EVALUATE_LISTENERS > INICIO > CONTACTID", contactId);
    contact = await fetchPost(COLLECTIONS.LEADS_COLLECTION_NAME, contactId);
    console.log(`EVALUATE_LISTENERS > FETCH_POST > CONTACT:`, { contact });
  } catch (err) {
    console.log(`EVALUATE_LISTENERS > FETCH_POST > ERROR:`, {
      err,
    });
    return null;
  }

  console.log(`EVALUATE_LISTENERS > DEPOISIFTRIGGER:`, {
    contact,
  });
  if (!contact) return null;

  /*
	If it's a new contact and has no ID yet
	*/
  if (!contact.ID) contact.ID = contactId;

  try {
    automations = await fetchAutomations();

    console.log(`EVALUATE_LISTENERS > FETCH_AUTOMATIONS > RESULT:`, {
      automations,
    });
  } catch (err) {
    console.log(`EVALUATE_LISTENERS > FETCH_AUTOMATIONS > ERROR:`, { err });
    return null;
  }

  if (!automations || !automations.length) return null;

  deal =
    docCollection === COLLECTIONS.DEALS_COLLECTION_NAME
      ? await fetchPost(docCollection, docId)
      : undefined;

  try {
    evaluatedAutomations = await evaluateAutomations({ automations, deal });
    console.log(`EVALUATELISTENERS > EVALUATED_AUTOMATIONS > RESULT > DEALS:`, {
      evaluatedAutomations,
      deal,
    });
  } catch (err) {
    console.log(
      `EVALUATELISTENERS > EVALUATED_AUTOMATIONS > DEALS > ERROR:`,
      err
    );
    return null;
  }
  if (!evaluatedAutomations.length) return null;

  return fireActions({ contact, evaluatedAutomations })
    .then(({ results, err }) => {
      if (results)
        console.log(` EVALUATED_AUTOMATIONS > FIRE_ACTIONS > RESULT:`, {
          results,
        });
      if (err)
        console.log(` EVALUATED_AUTOMATIONS > FIRE_ACTIONS > ERR:`, {
          results,
        });
      console.log(`🚀 > FIREACTIONS:`, {
        contact,
        evaluatedAutomations,
        results,
        err,
      });
      return logs({ contact, evaluatedAutomations, results, err });
    })
    .catch((error) => {
      console.error("FIREACTIONS > ERROR", error);
      return null;
    });
};

// grabHereToCopyPaste
const createListener = (listener, accountId, context) => {
  return {
    ...listener,
    context: context || {},
    accountId: accountId || null,
    createdAt: listener.createdAt || momentNow().valueOf(),
  };
};

// grabHereToCopyPaste
const addListener = (listener) => {
  const { collection, name, id } = listener;

  return new Promise((res, rej) => {
    if (!name || (!collection && !id)) return res(listener);

    let docId = id || name;

    return FirestoreRef.collection(COLLECTIONS.LISTENERS_COLLECTION_NAME)
      .doc(collection)
      .collection(docId)
      .add(listener)
      .then((result) => res(listener))
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const getListeners = (
  { collection, id, trigger, name, origin, logContext },
  accountId,
  context
) => {
  //TODO INSIDE GET LISTENERS
  console.log(`GETLISTENERS > START: `, {
    collection,
    id,
    trigger,
    name,
    context,
    accountId,
    origin,
    logContext,
  });
  if (!name) name = trigger;

  return new Promise((res, rej) => {
    if (!name || !collection || (!id && !accountId)) return res([]);

    let promises = [];

    if (
      origin === DATA_ORIGINS.SHOTX.WHATSAPP ||
      logContext.collection === COLLECTIONS.SHOTX_COLLECTION_NAME
    ) {
      const { instanceId } = logContext;
      console.log(`GETLISTENERS > ORIGIN: SHOTX`, {
        accountId,
        name,
        collection,
        id,
        trigger,
        context,
        origin,
        instanceId,
      });
      let listenerRef = FirestoreRef.collection(
        COLLECTIONS.LISTENERS_COLLECTION_NAME
      )
        .doc(COLLECTIONS.SHOTX_COLLECTION_NAME)
        .collection(instanceId)
        .where("name", "==", name)
        .where("triggerType", "==", COLLECTIONS.SHOTX_COLLECTION_NAME);
      console.log(`GETLISTENERS > ORIGIN: SHOTX > listenerRef`, {
        listenerRef,
      });
      promises.push(listenerRef.get().then((s) => s.docs.map((d) => d.data())));
    } else {
      console.log(
        `GETLISTENERS > ORIGIN: FORASHOTX > COLLECTION`,
        "LISTENERCOLLECTION",
        COLLECTIONS.LISTENERS_COLLECTION_NAME,
        "COLLECTION",
        name
      );
      console.log(`GETLISTENERS > ORIGIN: FORASHOTX > COLLECTION`, {
        accountId,
        name,
        collection,
        id,
        trigger,
        context,
        origin,
      });
      let listenerRef = FirestoreRef.collection(
        COLLECTIONS.LISTENERS_COLLECTION_NAME
      )
        .doc(collection)
        .collection(name)
        .where("accountId", "==", accountId)
        .where("name", "==", name);

      console.log(`GETLISTENERS > ORIGIN: FORASHOTX > listenerRef`, {
        listenerRef,
      });

      if ((context || {}).id)
        listenerRef = listenerRef.where("context.id", "==", context.id);
      if ((context || {}).collection)
        listenerRef = listenerRef.where(
          "context.collection",
          "==",
          context.collection
        );

      promises.push(listenerRef.get().then((s) => s.docs.map((d) => d.data())));
    }

    if (id) {
      let listenerRef = FirestoreRef.collection(
        COLLECTIONS.LISTENERS_COLLECTION_NAME
      )
        .doc(collection)
        .collection(id)
        .where("name", "==", name);

      if ((context || {}).id)
        listenerRef = listenerRef.where("context.id", "==", context.id);
      if ((context || {}).collection)
        listenerRef = listenerRef.where(
          "context.collection",
          "==",
          context.collection
        );

      promises.push(listenerRef.get().then((s) => s.docs.map((d) => d.data())));
    }

    return Promise.all(promises)
      .then((results) => {
        const listeners = results.reduce((all, r) => [...all, ...r], []);
        return res(listeners);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const removeListener = ({ collection, name, id }, accountId, context) => {
  return new Promise((res, rej) => {
    if (!name || !collection) return res([]);

    let listenerRef = FirestoreRef.collection(
      COLLECTIONS.LISTENERS_COLLECTION_NAME
    ).doc(collection);

    if (id) {
      listenerRef = listenerRef.collection(id);
    } else {
      listenerRef = listenerRef.collection(name);
    }

    if (accountId)
      listenerRef = listenerRef.where("accountId", "==", accountId);

    listenerRef = listenerRef
      .where("name", "==", name)
      .where("context.id", "==", context.id)
      .where("context.collection", "==", context.collection);

    return listenerRef
      .get()
      .then((snapshot) => {
        snapshot.forEach((doc) => {
          doc.ref.delete();
        });
        return res(true);
      })
      .catch((error) => {
        console.error(error);
        return rej(error);
      });
  });
};

// grabHereToCopyPaste
const handleListeners = async (change, context) => {
  funcCounter++;
  console.log("handleListeners funcCounter:", funcCounter);
  if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
    console.error(
      "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
        funcCounter
    );
    return null;
  }

  const { before, after } = change;
  const { params } = context;

  const docCollection = params.collection;
  const docId = params.docId;
  const oldData = (before.exists && before.data()) || {};
  const newData = (after.exists && after.data()) || {};
  const accountId =
    newData[CONSTANTS.ACCOUNT_FIELD] ||
    oldData[CONSTANTS.ACCOUNT_FIELD] ||
    null;
  // const docRef = change.after.ref;

  if (oldData.systemUpdate || newData.systemUpdate) {
    console.log(
      `systemUpdate on > ${
        oldData.systemUpdate ? "oldData" : "newData"
      } > funcCounter` + funcCounter
    );
    return null;
  }

  // console.log('Editing docId:', docId);
  // console.log('Editing data:', newData);
  // Boolean(oldData.triggers) && console.log('handleListeners > oldData > triggers', {triggers: oldData.triggers} );
  // Boolean(newData.triggers) && console.log('handleListeners > newData > triggers', {triggers: newData.triggers} );

  if (!newData.triggers && !oldData.triggers) {
    return null;
  }

  // ------------------------------------------------------------
  const documentContext = {
    collection: docCollection,
    id: docId,
  };

  let oldTriggers = oldData.triggers || [];
  let newTriggers = newData.triggers || [];

  /* ------------------------------------------------------------
	// Remover Listeners se automação não está ativa
	// ---------------------------------------------------------- */
  let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
  let wasExpired =
    oldData.end &&
    moment(oldData.end).isValid() &&
    new Date(oldData.end).getTime() < new Date(momentNowISO).getTime();
  let isExpired =
    newData.end &&
    moment(newData.end).isValid() &&
    new Date(newData.end).getTime() < new Date(momentNowISO).getTime();

  let wasPublished =
    oldData.status === CONSTANTS.PUBLISH_STATUS ||
    oldData.status === CONSTANTS.ACTIVE_STATUS;
  let isPublished =
    newData.status === CONSTANTS.PUBLISH_STATUS ||
    newData.status === CONSTANTS.ACTIVE_STATUS;

  let wasActive = wasPublished && !wasExpired;
  let isActive = isPublished && !isExpired;

  /*
	console.log('isExpired',isExpired);
	console.log('wasExpired',wasExpired);
	console.log('isPublished',isPublished);
	console.log('wasPublished',wasPublished);
	console.log('isActive',isActive);
	console.log('wasActive',wasActive);
	*/

  if (!isActive) {
    newTriggers = [];
  } else if (!wasActive) {
    oldTriggers = [];
  }

  const reduceTriggers = (a) =>
    !Array.isArray(a)
      ? []
      : a
          .filter((t) => Array.isArray(t.entrances))
          .reduce((all, t) => {
            return [
              ...all,
              ...t.entrances
                .filter((e) => e.name)
                .filter((e) => !all.find((a) => areEqualObjects(a, e))),
            ];
          }, []);

  const fetchListeners = (currentEntrances) => {
    const promises = currentEntrances.map((e) =>
      getListeners(e, accountId, documentContext)
    );
    return Promise.all(promises).then((results) =>
      results.reduce((all, r) => [...all, ...r], [])
    );
  };

  const addDocumentListeners = (diffListeners) => {
    const promises = diffListeners
      .filter((e) => e.name)
      .map((e) => addListener(e));
    return Promise.all(promises);
  };

  const removeDocumentListeners = (diffListeners) => {
    const promises = diffListeners.map((e) =>
      removeListener(e, accountId, documentContext)
    );
    return Promise.all(promises);
  };

  let newEntrances = reduceTriggers(newTriggers);
  let oldEntrances = reduceTriggers(oldTriggers);
  let addedEntrances = diffObjects(newEntrances, oldEntrances);
  let removedEntrances = diffObjects(oldEntrances, newEntrances);

  let newListeners = newEntrances.map((e) =>
    createListener(e, accountId, documentContext)
  );
  let oldListeners = oldEntrances.length
    ? await fetchListeners(oldEntrances)
    : [];

  let addedListeners = diffObjects(newListeners, oldListeners) || [];
  let removedListeners = diffObjects(oldListeners, newListeners) || [];

  console.log("handleListeners > data", {
    newEntrances,
    oldEntrances,
    addedEntrances,
    removedEntrances,
    newListeners,
    oldListeners,
    addedListeners,
    removedListeners,
  });

  return removeDocumentListeners(removedListeners)
    .then((results) => {
      console.log("removeDocumentListeners > results", results);
      return addDocumentListeners(addedListeners);
    })
    .then((results) => {
      console.log("addDocumentListeners > results", results);
      return null;
    })
    .catch((error) => {
      console.log("handleListeners > error in chained promises");
      console.error(error);
    });
};

module.exports = {
  handleChecklists,
  evaluateListeners,
  createListener,
  addListener,
  getListeners,
  removeListener,
  handleListeners,
};
