/* 
Firebase Auth - Documentation
https://firebase.google.com/docs/auth/admin/manage-users
*/

const { adminAuth } = require('../init');

// grabHereToCopyPaste
const getAuthUser = async (data) => {	
	try {
		const userRecord = await adminAuth.getUserByEmail(data.email);
		// See the UserRecord reference doc for the contents of userRecord.
		console.log('Successfully fetched user data:', userRecord.toJSON());
		return userRecord;
	}
	catch (error) {
		console.log('Error fetching user data:', error);
		return { error };
	}
}

// grabHereToCopyPaste
const createAuthUser = async (data) => {	
	try {
		let userData = {
			email: data.email || '',
			emailVerified: data.emailVerified || false,
			password: data.password || '',
			displayName: data.displayName || (data.firstName && data.lastName ? `${data.firstName } ${data.lastName}` : data.firstName || ''),
			disabled: false
		};
		if ( data.phoneNumber || data.mobile || data.phone ) {
			if (data.mobile && data.mobileCC) {
				userData.phoneNumber = `+${data.mobileCC}${data.mobile}`.replace(/ /g,'');
			}else
			if (data.phone && data.phoneCC) {
				userData.phoneNumber = `+${data.phoneCC}${data.phone}`.replace(/ /g,'');
			}else
			if (data.phoneNumber) {
				userData.phoneNumber = data.phoneNumber;
			}
		}
		if ( data.photoURL || data.avatar ) {
			if ((data.avatar||'').indexOf('http')>=0) {
				userData.photoURL = data.avatar;
			}else
			if (data.photoURL) {
				userData.photoURL = data.photoURL;
			}
		}
		const userRecord = await adminAuth.createUser(userData)
		// See the UserRecord reference doc for the contents of userRecord.
		console.log('Successfully created new user:', userRecord.uid);
		return userRecord;
	}
	catch (error) {
		console.log('Error creating new user:', error);
		return { error };
	}
}

const deleteAuthUser = async (uid) => {	
	try {
		const res = await adminAuth.deleteUser(uid)
		console.log('Successfully deleted user:', uid);
		return res;
	}
	catch (error) {
		console.log('Error deleting user:', error);
		return { error };
	}
}

module.exports = {
    getAuthUser,
    createAuthUser,
	deleteAuthUser
}