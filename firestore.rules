// rules_version = '2';

// Allow read/write access on all documents to any user signed in to the application
service cloud.firestore {
  match /databases/{database}/documents {

    function signedIn() {
      return request.auth != null && "uid" in request.auth;
    }

    function isPublic() {
      return "visibility" in resource.data && resource.data.visibility == 'public';
    }

    function signedInOrPublic() {
      return signedIn() || isPublic();
    }

    match /{collection}/{document=**} {
      allow read: if signedInOrPublic() || collection == 'qiusers' || collection == 'qiplus-plans';
    }

    match /{document=**} {
      allow read, write: if signedIn();
    }
   
  }
}