/**
 * App Errors
 *
 * @note
 * This file should be synced between the qiplus-frontend and the qiplus-firebase repos.
 *
 * Before making any changes to this file, please make sure the workspace.js script is
 * running on your environment.
 *
 * qiplus-frontend/src/constants/AppErrors.js > qiplus-firebase/functions/constants/errors.js
 */
const AUTH_ERROR = 'AUTH_ERROR'
const MISSING_REQUIRED_FIELDS = 'MISSING_REQUIRED_FIELDS'
const EMAIL_NOT_FOUND = 'EMAIL_NOT_FOUND'
const EMAIL_NOT_FOUND_GENERIC_ERROR = 'EMAIL_NOT_FOUND_GENERIC_ERROR'
const MODULE_NOT_AVAILABLE = 'MODULE_NOT_AVAILABLE'
const NO_ACCOUNT_FOUND = 'NO_ACCOUNT_FOUND'
const MODULE_LIMIT_REACHED = 'MODULE_LIMIT_REACHED'
const GENERIC_ERROR = 'GENERIC_ERROR'
const EMAIL_EXISTS_ERROR = 'EMAIL_EXISTS_ERROR'
const NEW_AUTH_USER_ERROR = 'NEW_AUTH_USER_ERROR'
const ERROR_ADDING_POST = 'ERROR_ADDING_POST'
const PREPARE_EMAIL_ERROR = 'PREPARE_EMAIL_ERROR'
const PREPARE_MAILING_ERROR = 'PREPARE_MAILING_ERROR'
const PREPARE_EMAIL_GENERIC_ERROR = 'PREPARE_EMAIL_GENERIC_ERROR'
const FETCH_CONTACTIDS_FROM_TRIGGERS = 'FETCH_CONTACTIDS_FROM_TRIGGERS'
const ERROR_GETTING_MAILBOX_FOLDERS = 'ERROR_GETTING_MAILBOX_FOLDERS'
const ERROR_OPENING_MAILBOX = 'ERROR_OPENING_MAILBOX'
const ERROR_MOVING_MESSAGE = 'ERROR_MOVING_MESSAGE'
const ERROR_ON_MOVING_MESSAGE = 'ERROR_ON_MOVING_MESSAGE'
const ERROR_APPENDING_TO_MAILBOX = 'ERROR_APPENDING_TO_MAILBOX'
const ERROR_ON_APPENDING_TO_MAILBOX = 'ERROR_ON_APPENDING_TO_MAILBOX'
const ERROR_FETCHING_IMAP_EMAILS = 'ERROR_FETCHING_IMAP_EMAILS'
const ERROR_RETRIEVING_EMAIL = 'ERROR_RETRIEVING_EMAIL'
const EMAIL_ANALYTICS_GENERIC_ERROR = 'EMAIL_ANALYTICS_GENERIC_ERROR'
const POST_STATS_GENERIC_ERROR = 'POST_STATS_GENERIC_ERROR'
const ERROR_ADDING_TRANSACTION = 'ERROR_ADDING_TRANSACTION'
const ERROR_ADDING_SUBSCRIPTION = 'ERROR_ADDING_SUBSCRIPTION'
const ERROR_GETTING_SUBSCRIPTION = 'ERROR_GETTING_SUBSCRIPTION'
const ERROR_CANCELING_SUBSCRIPTION = 'ERROR_CANCELING_SUBSCRIPTION'
const ERROR_GETTING_TRANSACTION = 'ERROR_GETTING_TRANSACTION'
const PAGARME_CREATE_RECIPIENT_ERROR = 'PAGARME_CREATE_RECIPIENT_ERROR'
const PAGARME_UPDATE_RECIPIENT_ERROR = 'PAGARME_UPDATE_RECIPIENT_ERROR'
const ERROR_GETTING_GEOIP_DATA = 'ERROR_GETTING_GEOIP_DATA'

module.exports = {
  AUTH_ERROR,
  MISSING_REQUIRED_FIELDS,
  EMAIL_NOT_FOUND,
  EMAIL_NOT_FOUND_GENERIC_ERROR,
  MODULE_NOT_AVAILABLE,
  NO_ACCOUNT_FOUND,
  MODULE_LIMIT_REACHED,
  GENERIC_ERROR,
  EMAIL_EXISTS_ERROR,
  NEW_AUTH_USER_ERROR,
  ERROR_ADDING_POST,
  PREPARE_EMAIL_ERROR,
  PREPARE_MAILING_ERROR,
  PREPARE_EMAIL_GENERIC_ERROR,
  FETCH_CONTACTIDS_FROM_TRIGGERS,
  ERROR_GETTING_MAILBOX_FOLDERS,
  ERROR_OPENING_MAILBOX,
  ERROR_MOVING_MESSAGE,
  ERROR_ON_MOVING_MESSAGE,
  ERROR_APPENDING_TO_MAILBOX,
  ERROR_ON_APPENDING_TO_MAILBOX,
  ERROR_FETCHING_IMAP_EMAILS,
  ERROR_RETRIEVING_EMAIL,
  EMAIL_ANALYTICS_GENERIC_ERROR,
  POST_STATS_GENERIC_ERROR,
  ERROR_ADDING_TRANSACTION,
  ERROR_ADDING_SUBSCRIPTION,
  ERROR_GETTING_SUBSCRIPTION,
  ERROR_CANCELING_SUBSCRIPTION,
  ERROR_GETTING_TRANSACTION,
  PAGARME_CREATE_RECIPIENT_ERROR,
  PAGARME_UPDATE_RECIPIENT_ERROR,
  ERROR_GETTING_GEOIP_DATA,
}
