/* eslint-disable promise/no-nesting */
const {
  ROLES,
  CONSTANTS,
  FIRESTORE_EVENTS,
  COLLECTIONS,
  KEYWORDS_SUBCOLLECTIONS_MAP,
  KEYWORDS_SUBCOLLECTION_NAME,
  KEYWORDS_FIELDS_MAP,
  ERROR_TYPES,
} = require("./init");
const {
  admin,
  FieldValue,
  FieldPath,
  functions,
  upload,
  cors,
  FirestoreRef,
  adminAuth,
} = require("./init");
const { helpers, moment, langMessages } = require("./init");
const { DEFAULT_FROM_EMAIL, EMAIL_APP_DOMAIN } = require("./mailgun");
const { automationCron, fetchContactsFromTriggers } = require("./automations");
const {
  handleChecklists,
  evaluateListeners,
  getListeners,
  handleListeners,
} = require("./listeners");
const {
  handleNotifications,
  handleDeadlines,
  notificationCron,
} = require("./notifications");
const { sanitizeLeadData } = require("./leads");
const { sanitizeQIUserData } = require("./qiusers");
const { sanitizeAccount } = require("./accounts");
const { sanitizeDealData, updateFunnelStats } = require("./deals");
const { sanitizeLandingPageData } = require("./landing-pages");
const {
  $GLOBALS,
  addNewLog,
  internalLog,
  buildPages,
  getAccountByOwner,
  updateKeywordsSubcollection,
} = require("./post");
const {
  addNewPost,
  updateRef,
  updatePost,
  deleteCollectionPosts,
  trashPost,
  fetchPost,
  fetchCollection,
  fetchTaxonomy,
  updateCache,
  migrateSubCollections,
  deleteSubCollections,
} = require("./post");
const {
  emailCron,
  adminMail,
  createEmail,
  prepareEmail,
} = require("./mailing");

const { shotxCron } = require("./shotx");
const { shotxSendMessages } = require("./shotxSendMessages");
const { pagarmeClient, billingCron, pagarmeApi } = require("./pagarme");
const { getAuthUser, createAuthUser, deleteAuthUser } = require("./auth");
const { ticketsListener } = require("./tickets");
const {
  accountId,
  instanceId,
  contactId,
  messageId,
} = require("./models/childnodes/shotxMessages");
const mailboxes = require("./mailboxes");
const {
  removeBrazilNineDigit,
  insertBrazilNineDigit,
} = require("./utils/phoneUtils");
const DATA_ORIGINS = require("./constants/origins");
const { CPASAdCreationTemplate } = require("facebook-nodejs-business-sdk");

const { keywordsSubcollections } = COLLECTIONS;

const APP_ENVIROINMENT = functions.config().qiplus.env;
const APP_ID =
  APP_ENVIROINMENT === "development" ? "br-com-qiplus" : "qiplus-50eee";
const storageBucket = `${APP_ID}.appspot.com`;
const storage = admin.storage();

console.log = function () {
  if (CONSTANTS.APP_ENVIROINMENT === CONSTANTS.PRODUCTION_ENV) {
    // return null
  }
  let msg = "QIPlus log",
    obj = arguments;
  if (arguments.length === 1) {
    obj = arguments[0];
    if (Array.isArray(obj)) obj = { Array: obj };
    return functions.logger.log(obj);
  }
  if (arguments.length === 2 && typeof arguments[0] === "string") {
    msg = arguments[0];
    obj = arguments[1];
    if (Array.isArray(obj)) obj = { Array: obj };
    return functions.logger.log(msg, obj);
  }
  return functions.logger.log(...arguments);
};
console.error = functions.logger.error;

// console.log('APP_ENV',{
// 	APP_ENV: CONSTANTS.APP_ENV,
// 	APP_ENVIROINMENT,
// 	GCP_PROJECT: `${process.env.GCP_PROJECT}`||'NOPE',
// 	NODE_ENV: `${process.env.NODE_ENV}`||'NOPE',
// 	FIREBASE_CONFIG: process.env.FIREBASE_CONFIG||'NOPE',
// 	ENV: process.env||{},
// });

const {
  momentNow,
  nowToISO,
  isNumeric,
  sortByUpdateDate,
  sanitizeFieldTypes,
  generateKeywords,
  jsonClone,
  isLoopableObject,
  uniqueTimestamp,
  compileLogsStats,
  diffObjects,
  paginateArray,
} = helpers;

const { APP_TRIGGERS, DEFAULT_LANG, DEFAULT_LOCALE, MOMENT_SHORT, MOMENT_ISO } =
  CONSTANTS;

const { COLLECTION_TRIGGERS } = COLLECTIONS;

let funcCounter = 0;

const getChangeType = (eventType) =>
  (eventType || "").replace("google.firestore.document.", ""); // write | create | update | delete

const functionsLog = (fnName, error, docData) => {
  return internalLog(fnName, error, docData)
    .then((r) => {
      const gcpPath = `https://console.firebase.google.com/u/0/project/${APP_ID}/firestore/data~2F${COLLECTIONS.INTERNAL_LOGS_COLLECTION_NAME}~2Ffunctions~2F${fnName}~2F${r.id}`;
      const emailData = {
        message: {
          to: CONSTANTS.WEBMASTER_EMAIL,
          subject: "Error log",
          html: `<div>Ocorreu um erro na função: <b>${fnName}</b><br><br>
					Log id: <b>${r.id}</b><br><br>
					Log path: <b>${COLLECTIONS.INTERNAL_LOGS_COLLECTION_NAME}/functions/${fnName}/${
            r.id
          }</b><br><br>
					Console path: <b><a href="${gcpPath}">${gcpPath}</a></b><br><br>
					<hr>
					At: ${nowToISO()}
				</div>`,
        },
      };
      return adminMail(emailData);
    })
    .then((result) => {
      console.log(`${fnName} > adminMail > result`, result);
      return null;
    })
    .catch((err) => {
      console.error(`${fnName} > catch > error`, { err });
    });
};

const backupFirestore = (collectionIds, path, bucketName) => {
  collectionIds = collectionIds || [];
  bucketName = bucketName || storage.bucket().name;

  const client = new admin.firestore.v1.FirestoreAdminClient();
  const databaseName = client.databasePath(APP_ID, "(default)");

  const d = new Date();
  const timeStampPath = `${momentNow().format("YYYYMMDD_HHmm")}-${d.getTime()}`;
  const backupDir = `backups/${path || timeStampPath}`;

  return client
    .exportDocuments({
      name: databaseName,
      // Add your bucket name here
      outputUriPrefix: `gs://${bucketName}/${backupDir}`,
      // Empty array == all collections
      collectionIds,
    })
    .then(([response]) => {
      console.log(`backupFirestore > Operation Name: ${response.name}`);
      return response;
    })
    .catch((err) => {
      console.error(err);
      throw new Error("backupFirestore > Export operation failed");
    });
};

const importFirestoreBackup = (collectionIds, path, bucketName) => {
  collectionIds = collectionIds || [];
  bucketName = bucketName || storage.bucket().name;

  const client = new admin.firestore.v1.FirestoreAdminClient();
  const databaseName = client.databasePath(APP_ID, "(default)");
  const backupDir = `backups/${path || ""}`;

  return client
    .importDocuments({
      name: databaseName,
      // Add your bucket name here
      inputUriPrefix: `gs://${bucketName}/${backupDir}`,
      // Empty array == all collections
      collectionIds,
    })
    .then(([response]) => {
      console.log(`importFirestoreBackup > Operation Name: ${response.name}`);
      return response;
    })
    .catch((err) => {
      console.error(err);
      throw new Error("importFirestoreBackup > Export operation failed");
    });
};

// ----------------------------------------------------------------------------------------------------
// upload
// ----------------------------------------------------------------------------------------------------
// without authentication
exports.upload = functions.https.onRequest(upload.handler);
// with authentication
exports.authUpload = functions.https.onRequest(upload.authHandler);
// ----------------------------------------------------------------------------------------------------

// ----------------------------------------------------------------------------------------------------
// imports
// ----------------------------------------------------------------------------------------------------
exports.updateCache = functions.firestore
  .document("{collection}/{docId}")
  .onWrite(updateCache);

// ----------------------------------------------------------------------------------------------------
// auth
// ----------------------------------------------------------------------------------------------------
/*
exports.deleteUsersOnAuthDelete = functions.auth.user().onDelete((event) => {
	// const uid = event.data.uid;
	// return FirestoreRef.collection('users').doc(`${uid}`).delete();
	return null;
});
*/
exports.getAuthUser = functions.https.onRequest(async (request, response) => {
  cors(request, response, async () => {
    const data = request.body.data;
    console.log("getAuthUser > data", data);

    if (!data) {
      // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
      console.log("failed-precondition No data sent", request.body);
      return null;
    }

    let { accountId, email } = data;

    if (!email) {
      // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(newUser));
      console.log("failed-precondition No data sent", data);
      const errorType = ERROR_TYPES.MISSING_REQUIRED_FIELDS;
      const errorMessage = "Ocorreu um erro na requisição. Dados incompletos";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: data },
      });
    }

    try {
      const authUserQuery = await getAuthUser(data);
      if (authUserQuery.email) {
        return response.send({ data: { authUser: authUserQuery } });
      }
      if (authUserQuery.error) {
        let {
          error,
          error: { code },
        } = authUserQuery;
        const errorType = ERROR_TYPES.EMAIL_NOT_FOUND;
        const errorMessage =
          typeof error === "string"
            ? error
            : error.message
              ? error.message
              : "";
        return response.send({
          data: {
            error: errorType,
            errorMessage,
            errorType,
            debug: { ...error, code, authUserQuery },
          },
        });
      }
      const errorType = ERROR_TYPES.EMAIL_NOT_FOUND;
      const errorMessage =
        "Não foi encontrado nenhum usuário com o email informado";
      return response.send({
        data: {
          error: errorType,
          errorMessage,
          errorType,
          debug: { authUserQuery },
        },
      });
    } catch (error) {
      console.error(error);
      const errorType = ERROR_TYPES.EMAIL_NOT_FOUND_GENERIC_ERROR;
      const errorMessage =
        typeof error === "string" ? error : error.message ? error.message : "";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: error },
      });
    }
  });
  return null;
});
// ----------------------------------------------------------------------------------------------------

// ----------------------------------------------------------------------------------------------------
// COLLECTIONS BACKUP
// ----------------------------------------------------------------------------------------------------
exports.doFirestoreBackup = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .https.onRequest(async (request, response) => {
    cors(request, response, async () => {
      const data = request.body.data;

      if (!data || !Array.isArray(data.collectionIds)) {
        console.log("failed-precondition No data sent", request.body);
        return null;
      }

      try {
        const { collectionIds, path, bucketName } = data;
        console.log("doFirestoreBackup > collectionIds", collectionIds);

        const result = await backupFirestore(collectionIds, path, bucketName);
        // console.log('doFirestoreBackup > result', result);

        return response.send({
          data: { ok: true, result: typeof result, request: request.body.data },
        });
      } catch (error) {
        console.log("doFirestoreBackup > error", { error });
        return response.send({
          data: {
            ...data,
            error: "ERROR_DOING_BACKUP",
            err: error,
            status: false,
          },
        });
      }
    });
    return null;
  });
// ----------------------------------------------------------------------------------------------------

// ----------------------------------------------------------------------------------------------------
// COLLECTION COUNT
// ----------------------------------------------------------------------------------------------------
/*
 * Cloud Firestore collection count
 * https://stackoverflow.com/questions/46554091/cloud-firestore-collection-count
 */
exports.collectionCounter = functions.firestore
  .document("{collection}/{docId}")
  .onWrite(async (change, context) => {
    const { before, after } = change;
    const { params } = context;
    const { collection, docId } = params;

    if (
      docId === COLLECTIONS.INDEX_ID ||
      !COLLECTIONS.moduledCollections.includes(collection)
    ) {
      return null;
    }

    const docRef = after.exists ? change.after.ref : null;
    const oldData = (before.exists && before.data()) || {};
    const newData = (after.exists && after.data()) || {};

    if (after.exists && (oldData.systemUpdate || newData.systemUpdate)) {
      console.log(
        `systemUpdate on > ${
          oldData.systemUpdate ? "oldData" : "newData"
        } > funcCounter` + funcCounter
      );
      return null;
    }

    let { accountId, owner } = { ...oldData, ...newData };

    console.log(`${collection} > ${docId} > #1`, {
      accountId,
      owner,
      collection,
      docId,
      before: before.exists,
      after: after.exists,
    });

    if (owner && (!accountId || accountId === CONSTANTS.ORPHANS_ACCOUNT)) {
      const accountData = await getAccountByOwner(owner);
      accountId = (accountData || {}).ID || "";
    }

    console.log(`${collection} > ${docId} > #2`, { accountId });

    if (!accountId) return null;

    const accountDoc = await FirestoreRef.collection(
      COLLECTIONS.ACCOUNTS_COLLECTION_NAME
    )
      .doc(accountId)
      .get();

    console.log(`${collection} > ${docId} > #3`, { exists: accountDoc.exists });

    if (!accountDoc.exists) return null;

    const promises = [];
    const accountRef = accountDoc.ref;

    // index
    const counterDoc = await accountRef
      .collection(COLLECTIONS.INDEX_ID)
      .doc(COLLECTIONS.COUNTER_ID)
      .get();
    const counterData = counterDoc.exists ? counterDoc.data() : {};
    const counterRef = counterDoc.exists
      ? counterDoc.ref
      : accountRef.collection(COLLECTIONS.INDEX_ID).doc(COLLECTIONS.COUNTER_ID);

    console.log(`${collection} > ${docId} > #4`, {
      counterDoc: counterDoc.exists,
      inData: collection in counterData,
    });

    if (collection in counterData) {
      const count = counterData[collection] || 0;
      const newCount = Math.max(
        0,
        !before.exists ? count + 1 : !after.exists ? count - 1 : count
      );

      console.log(
        `${collection} > ${docId} > #5 > count > ${count} > newCount > ${newCount}`
      );

      if (count !== newCount)
        promises.push(counterRef.update({ [collection]: newCount }));
    } else {
      const docsCount = await FirestoreRef.collection(collection)
        .where(CONSTANTS.ACCOUNT_FIELD, "==", accountId)
        .get();
      const size = docsCount.size;

      console.log(`${collection} > ${docId} > #6`, {
        size,
        counterData,
        hasCollection: collection in counterData,
      });

      if (counterDoc.exists) {
        promises.push(counterRef.update({ [collection]: size }));
      } else {
        promises.push(counterRef.set({ [collection]: size }));
      }
    }

    switch (collection) {
      case COLLECTIONS.FUNNELS_COLLECTION_NAME:
        if (
          !before.exists &&
          "stats" in newData &&
          isLoopableObject(newData.stats)
        ) {
          promises.push(updateRef(docRef, { stats: FieldValue.delete() }));
        }
        break;
      case COLLECTIONS.DEALS_COLLECTION_NAME:
        if (CONSTANTS.FUNNEL_ID_FIELD in { ...oldData, ...newData }) {
          const oldFunnelId = oldData[CONSTANTS.FUNNEL_ID_FIELD];
          const funnelId = { ...oldData, ...newData }[
            CONSTANTS.FUNNEL_ID_FIELD
          ];
          const funnelDoc = await FirestoreRef.collection(
            COLLECTIONS.FUNNELS_COLLECTION_NAME
          )
            .doc(funnelId)
            .get();

          if (oldFunnelId && oldFunnelId !== funnelId) {
            const oldFunnelDoc = await FirestoreRef.collection(
              COLLECTIONS.FUNNELS_COLLECTION_NAME
            )
              .doc(oldFunnelId)
              .get();
            if (oldFunnelDoc.exists) {
              const oldFunnelData = oldFunnelDoc.data();
              const oldFunnelStages = Object.keys(
                oldFunnelData.pipeline || {}
              ).map((k) => k.toString());
              const oldStage = `${oldData.stage || ""}`;

              let oldFunnelStats = oldFunnelData.stats || {};
              if (oldFunnelStages.includes(oldStage.toString())) {
                promises.push(
                  updateRef(oldFunnelDoc.ref, {
                    stats: {
                      ...oldFunnelStats,
                      deals: (oldFunnelStats.deals || 1) - 1,
                      [oldStage]: (oldFunnelStats[oldStage] || 1) - 1,
                    },
                  })
                );
              }
            }
          }

          if (!funnelDoc.exists) break;

          const funnelData = funnelDoc.data();
          const pipeline = funnelData.pipeline || {};
          const stages = Object.keys(pipeline).map((k) => k.toString());
          const currentStage = `${newData.stage || ""}`;
          const prevStage = `${oldData.stage || ""}`;

          if ("stats" in funnelData) {
            let stats = funnelData.stats;

            stages.forEach((k) => {
              if (!(k in stats)) stats[k] = 0;
            });

            console.log(`${collection} > ${docId} > currentStage`, {
              stages,
              currentStage,
              includes: stages.includes(currentStage.toString()),
            });
            if (
              currentStage !== prevStage &&
              (!oldFunnelId || oldFunnelId === funnelId) &&
              stages.includes(currentStage.toString())
            ) {
              stats = {
                ...stats,
                [currentStage]: (stats[currentStage] || 0) + 1,
              };
            }

            console.log(`${collection} > ${docId} > prevStage`, {
              stages,
              prevStage,
              includes: stages.includes(prevStage.toString()),
            });
            if (
              (currentStage !== prevStage ||
                (oldFunnelId && oldFunnelId !== funnelId)) &&
              stages.includes(prevStage.toString())
            ) {
              stats = {
                ...stats,
                [prevStage]: (stats[prevStage] || 1) - 1,
              };
            }

            if (oldFunnelId && funnelId && oldFunnelId !== funnelId) {
              stats.deals = (stats.deals || 0) + 1;
            } else if (!before.exists || !after.exists) {
              console.log(`${collection} > ${docId} > !exists`);
              stats.deals = Math.max(
                0,
                (stats.deals || 0) + (!before.exists ? 1 : -1)
              );
            }

            if (
              (!oldFunnelId || oldFunnelId === funnelId) &&
              currentStage !== prevStage &&
              currentStage === CONSTANTS.WON_STAGE
            ) {
              const days = moment(nowToISO()).diff(
                moment(newData.date),
                "days"
              );
              const seconds = moment(nowToISO()).diff(
                moment(newData.date),
                "seconds"
              );
              stats.wonCycleDays = (stats.wonCycleDays || 0) + days;
              stats.wonCycleSeconds = (stats.wonCycleSeconds || 0) + seconds;
            }

            promises.push(updateRef(funnelDoc.ref, { stats }));
          } else {
            const dealsCount = await FirestoreRef.collection(collection)
              .where(CONSTANTS.FUNNEL_ID_FIELD, "==", funnelId)
              .get();
            const deals = dealsCount.docs.map((d) => d.data());

            let stats = {
              deals: deals.length,
            };

            Object.keys(pipeline).forEach((k) => {
              stats[k] = deals.filter(
                (d) => d.stage.toString() === k.toString()
              ).length;
            });

            let wonCycleDays = 0;
            let wonCycleSeconds = 0;
            deals
              .filter((d) => d.stage === CONSTANTS.WON_STAGE)
              .forEach((deal, i) => {
                if (isNumeric((deal.stats || {}).wonCycleDays)) {
                  wonCycleDays += Number(deal.stats.wonCycleDays);
                }
                if (isNumeric((deal.stats || {}).wonCycleSeconds)) {
                  wonCycleSeconds += Number(deal.stats.wonCycleSeconds);
                } else if (isNumeric((deal.stats || {}).wonCycleDays)) {
                  wonCycleSeconds +=
                    Number(deal.stats.wonCycleDays) * 24 * 60 * 60;
                }
              });

            stats.wonCycleDays = wonCycleDays;
            stats.wonCycleSeconds = wonCycleSeconds;

            promises.push(updateRef(funnelDoc.ref, { stats }));
          }
        }
        break;
      default:
        break;
    }

    if (
      after.exists &&
      accountId &&
      accountId !== CONSTANTS.ORPHANS_ACCOUNT &&
      newData.accountId !== accountId
    ) {
      console.log(`${collection} > ${docId} > #7`, { accountId, newData });
      promises.push(updatePost(collection, { ...newData, accountId }));
    }

    return Promise.all(promises)
      .then((r) => {
        console.log("collectionCounter > r", r.length);
        return null;
      })
      .catch((err) => {
        console.error("collectionCounter > err", err);
      });
  });

exports.taxonomiesCounter = functions.firestore
  .document("{collection}/{docId}")
  .onWrite(async (change, context) => {
    const { before, after } = change;
    const { params } = context;
    const { collection, docId } = params;

    if (
      docId === COLLECTIONS.INDEX_ID ||
      !COLLECTIONS.moduledCollections.includes(collection)
    ) {
      return null;
    }

    const oldData = (before.exists && before.data()) || {};
    const newData = (after.exists && after.data()) || {};

    if (oldData.systemUpdate || newData.systemUpdate) {
      console.log(
        `systemUpdate on > ${
          oldData.systemUpdate ? "oldData" : "newData"
        } > funcCounter` + funcCounter
      );
      return null;
    }

    let oldTags = ((oldData && oldData.tags) || []).filter((l) => Boolean(l));
    let newTags = (newData.tags || []).filter((l) => Boolean(l));
    let addedTags = newTags.filter((l) => Boolean(l) && !oldTags.includes(l));
    let removedTags = oldTags.filter((l) => Boolean(l) && !newTags.includes(l));

    const taxName = COLLECTIONS.TAGS_TAXONOMY_NAME;

    let promises = [];

    promises = promises.concat(
      addedTags.map((tagId) => {
        return FirestoreRef.collection(
          `${COLLECTIONS.TAXONOMIES_COLLECTION_NAME}/${collection}/${taxName}`
        )
          .doc(`${tagId}`)
          .update({ [collection]: FieldValue.increment(1) });
      })
    );

    promises = promises.concat(
      removedTags.map((tagId) => {
        return FirestoreRef.collection(
          `${COLLECTIONS.TAXONOMIES_COLLECTION_NAME}/${collection}/${taxName}`
        )
          .doc(`${tagId}`)
          .update({ [collection]: FieldValue.increment(-1) });
      })
    );

    return Promise.all(promises);
  });
// ----------------------------------------------------------------------------------------------------

// ----------------------------------------------------------------------------------------------------
// SCORE
// ----------------------------------------------------------------------------------------------------
exports.scoreCounter = functions.firestore
  .document(
    `{collection}/{docId}/${COLLECTIONS.SCORES_SUBCOLLECTION_NAME}/{scoreId}`
  )
  .onCreate((doc, context) => {
    const { params } = context;
    const docData = doc.data();

    const collection = params.collection;
    const docId = params.docId;

    let { score } = docData;

    score = !isNaN(parseFloat(score)) ? parseFloat(score) : 0;

    console.log("scoreCounter", { collection, docId, score });

    return updateRef(doc.ref, {
      createdAt: uniqueTimestamp(doc.createTime.toMillis()),
      updatedAt: uniqueTimestamp(doc.createTime.toMillis()),
    }).then((r) => {
      if (score)
        return FirestoreRef.collection(collection)
          .doc(docId)
          .update({ score: FieldValue.increment(score) });
      return null;
    });
  });
// ----------------------------------------------------------------------------------------------------

// ----------------------------------------------------------------------------------------------------
// AFFILIATES
// ----------------------------------------------------------------------------------------------------
exports.affiliatesCounter = functions.firestore
  .document(`${COLLECTIONS.ACCOUNTS_COLLECTION_NAME}/{docId}`)
  .onWrite(async (change, context) => {
    const { before, after } = change;
    const { params } = context;
    const { docId } = params;
    const collection = COLLECTIONS.ACCOUNTS_COLLECTION_NAME;

    if (docId === COLLECTIONS.INDEX_ID) {
      return null;
    }

    const docRef = after.exists ? change.after.ref : null;
    const oldData = (before.exists && before.data()) || {};
    const newData = (after.exists && after.data()) || {};

    if (oldData.systemUpdate || newData.systemUpdate) {
      console.log(
        `systemUpdate on > ${
          oldData.systemUpdate ? "oldData" : "newData"
        } > funcCounter` + funcCounter
      );
      return null;
    }

    let accountId = docId;

    let { affiliateId, owner } = { ...oldData, ...newData };

    console.log(`affiliatesCounter > ${docId} > #1`, {
      affiliateId,
      accountId,
      owner,
      collection,
      docId,
      before: before.exists,
      after: after.exists,
    });

    if (!affiliateId) return null;

    const affiliatetDoc = await FirestoreRef.collection(
      COLLECTIONS.AFFILIATES_COLLECTION_NAME
    )
      .doc(affiliateId)
      .get();

    console.log(`affiliatesCounter > ${docId} > #2`, {
      exists: affiliatetDoc.exists,
    });

    if (!affiliatetDoc.exists) return null;

    const promises = [];
    const accountRef = docRef;
    const affiliateRef = affiliatetDoc.ref;

    // index
    const counterDoc = await affiliateRef
      .collection(COLLECTIONS.INDEX_ID)
      .doc(COLLECTIONS.COUNTER_ID)
      .get();
    const counterData = counterDoc.exists ? counterDoc.data() : {};
    const counterRef = counterDoc.exists
      ? counterDoc.ref
      : affiliateRef
          .collection(COLLECTIONS.INDEX_ID)
          .doc(COLLECTIONS.COUNTER_ID);

    console.log(`affiliatesCounter > ${docId} > #3`, {
      counterDoc: counterDoc.exists,
    });

    const accountsCount = await FirestoreRef.collection(collection)
      .where(CONSTANTS.AFFILIATE_FIELD, "==", affiliateId)
      .get();
    const size = accountsCount.size;

    console.log(`affiliatesCounter > ${docId} > #4`, { size, counterData });

    if (counterDoc.exists) {
      promises.push(counterRef.update({ [collection]: size }));
    } else {
      promises.push(counterRef.set({ [collection]: size }));
    }

    const log = {
      affiliateId,
      user_id: affiliateId,
      operator_id: 0,
      id: docId,
      collection,
      date: nowToISO(),
      owner: owner || "",
      accountId,
      context: {},
    };

    if (!oldData.affiliateId) {
      log.trigger = APP_TRIGGERS.APP_TRIGGER_AFFILIATE_CHANGED;
      log.data = newData;
      promises.push(
        affiliateRef.collection(COLLECTIONS.LOGS_COLLECTION_NAME).add(log)
      );
    } else if (!after.exists) {
      log.trigger = APP_TRIGGERS.APP_TRIGGER_AFFILIATE_REMOVED;
      log.data = oldData;
      promises.push(
        affiliateRef.collection(COLLECTIONS.LOGS_COLLECTION_NAME).add(log)
      );
    }

    return Promise.all(promises)
      .then((r) => {
        console.log("affiliatesCounter > r", r.length);
        return null;
      })
      .catch((err) => {
        console.error("affiliatesCounter > err", err);
      });
  });

// ----------------------------------------------------------------------------------------------------
// FIRESTORE
// ----------------------------------------------------------------------------------------------------
exports.getAvailableLimit = functions.https.onRequest(
  async (request, response) => {
    cors(request, response, async () => {
      const postedData = request.body.data;
      console.log("CREATELEAD > POSTEDDATA", postedData);

      if (!postedData) {
        // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
        console.log("failed-precondition No data sent", request.body);
        return null;
      }

      let { accountId, collection } = postedData;

      if (!accountId || !collection) {
        // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(postedData));
        console.log("failed-precondition No data sent", postedData);
        const errorType = ERROR_TYPES.MISSING_REQUIRED_FIELDS;
        const errorMessage = "Ocorreu um erro na requisição. Dados incompletos";
        return response.send({
          data: {
            error: errorType,
            errorMessage,
            errorType,
            debug: postedData,
            available: false,
            limit: 0,
            count: 0,
          },
        });
      }

      try {
        let limit = 0,
          posts = [],
          count = 0;

        const accountDoc = await FirestoreRef.collection(
          COLLECTIONS.ACCOUNTS_COLLECTION_NAME
        )
          .doc(accountId)
          .get();
        const account = accountDoc.exists ? accountDoc.data() : {};

        if ((account || {}).modules) {
          const accountRef = accountDoc.ref;
          const counterDoc = await accountRef
            .collection(COLLECTIONS.INDEX_ID)
            .doc(COLLECTIONS.COUNTER_ID)
            .get();
          const counterData = counterDoc.exists ? counterDoc.data() : {};

          const { modules, config, data } = account;
          console.log("modules", { modules, config, data });

          let { email_option, emails_towards_base, emails_included } = config;
          let { contacts_max } = data;

          if (collection in modules && !modules[collection]) {
            const errorType = ERROR_TYPES.MODULE_NOT_AVAILABLE;
            const errorMessage =
              "Este módulo não está disponível na conta atual";
            return response.send({
              data: {
                error: errorType,
                errorMessage,
                errorType,
                debug: { collection, modules },
              },
            });
          }

          switch (collection) {
            case COLLECTIONS.LEADS_COLLECTION_NAME:
              if (contacts_max) {
                limit = Number(contacts_max);
                if (collection in counterData) {
                  count = counterData[collection];
                } else {
                  posts = await fetchCollection(collection, [
                    [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
                  ]);
                  count = posts.length;
                  console.log("posts", { posts: posts.length, limit });
                }
              }
              break;
            case COLLECTIONS.QIUSERS_COLLECTION_NAME:
              if (
                `${collection}_included` in config &&
                config[`${collection}_included`]
              ) {
                limit = Number(config[`${collection}_included`]);
                if (collection in counterData) {
                  count = counterData[collection];
                } else {
                  posts = await fetchCollection(collection, [
                    [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
                  ]);
                  count = posts.length;
                  console.log("posts", { posts: posts.length, limit });
                }
              }
              break;
            case COLLECTIONS.MAIL_COLLECTION_NAME:
              if (email_option) {
                limit =
                  email_option === "rel"
                    ? Number(emails_towards_base) * Number(contacts_max)
                    : Number(emails_included);
                if (collection in counterData) {
                  count = counterData[collection];
                } else {
                  posts = await fetchCollection(collection, [
                    [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
                  ]);
                  count = posts.length;
                  console.log("posts", {
                    posts: posts.length,
                    limit,
                    email_option,
                    emails_towards_base,
                    emails_included,
                    contacts_max,
                  });
                }
              }
              break;
            case COLLECTIONS.MAILBOXES_COLLECTION_NAME:
              if (
                `${collection}_included` in config &&
                config[`${collection}_included`]
              ) {
                limit = Number(config[`${collection}_included`]);
                posts = await fetchCollection(collection, [
                  [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
                  ["provider", "==", EMAIL_APP_DOMAIN],
                ]);
                count = posts.length;
                console.log("posts", { posts: posts.length, limit });
              }
              break;
            case "posts":
            default:
              if (
                `${collection}_included` in config &&
                config[`${collection}_included`]
              ) {
                limit = Number(config[`${collection}_included`]);
                if (collection in counterData) {
                  count = counterData[collection];
                } else {
                  posts = await fetchCollection(collection, [
                    [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
                  ]);
                  count = posts.length;
                  console.log("posts", { posts: posts.length, limit });
                }
              }
              break;
          }

          return response.send({
            data: { available: count < limit, limit, count },
          });
        } else {
          console.error("getAvailableLimit > No account found");
          const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
          const errorMessage =
            "Ocorreu um erro na requisição. Conta não encontrada.";
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: {},
              available: false,
              limit: 0,
              count: 0,
            },
          });
        }
      } catch (error) {
        console.error(error);
        return response.send({
          data: {
            available: false,
            limit: 0,
            count: 0,
            error: "GENERIC_ERROR",
            debug: error,
          },
        });
      }
    });
    return null;
  }
);

exports.createLead = functions.https.onRequest(async (request, response) => {
  cors(request, response, async () => {
    const newLead = request.body.data;
    console.log("CREATELEAD > NEWLEAD", newLead);

    if (!newLead) {
      // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
      console.log("failed-precondition No data sent", request.body);
      return null;
    }

    const collection = COLLECTIONS.LEADS_COLLECTION_NAME;

    let { accountId } = newLead;

    if (!accountId || !collection) {
      // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(newLead));
      console.log("failed-precondition No data sent", newLead);
      const errorType = ERROR_TYPES.MISSING_REQUIRED_FIELDS;
      const errorMessage = "Ocorreu um erro na requisição. Dados incompletos";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: newLead },
      });
    }

    try {
      const accountDoc = await FirestoreRef.collection(
        COLLECTIONS.ACCOUNTS_COLLECTION_NAME
      )
        .doc(accountId)
        .get();
      const account = accountDoc.exists ? accountDoc.data() : {};

      if ((account || {}).modules) {
        const accountRef = accountDoc.ref;
        const counterDoc = await accountRef
          .collection(COLLECTIONS.INDEX_ID)
          .doc(COLLECTIONS.COUNTER_ID)
          .get();
        const counterData = counterDoc.exists ? counterDoc.data() : {};
        const { modules, config, data } = account;
        console.log("modules", { modules, config, data });

        let { contacts_max } = data;

        if (contacts_max) {
          let limit = Number(contacts_max);
          let count = 0;

          if (collection in counterData) {
            count = counterData[collection];
          } else {
            let posts = await fetchCollection(collection, [
              [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
            ]);
            count = posts.length;
            console.log("posts", { posts: posts.length, limit });
          }

          if (count && limit && count >= limit) {
            let current = count;
            const errorType = ERROR_TYPES.MODULE_LIMIT_REACHED;
            const errorMessage =
              "O limite de [%collection] da conta foi atingido".replace(
                "[%collection]",
                `leads`
              );
            return response.send({
              data: {
                error: errorType,
                errorMessage,
                errorType,
                debug: {},
                limit,
                current,
              },
            });
          }
        }
      } else {
        console.error("createLead > No account found");
        const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
        const errorMessage =
          "Ocorreu um erro na requisição. Conta não encontrada.";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: {} },
        });
      }

      const lead = await addNewPost(collection, newLead);
      let responseData = {
        ...lead,
        id: lead.ID,
        ID: lead.ID,
      };
      return response.send({ data: { ...responseData } });
    } catch (error) {
      console.error(error);
      const errorType = ERROR_TYPES.GENERIC_ERROR;
      const errorMessage =
        typeof error === "string" ? error : error.message ? error.message : "";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: error },
      });
    }
  });
  return null;
});

exports.createPost = functions.https.onRequest(async (request, response) => {
  cors(request, response, async () => {
    const newPost = request.body.data;
    console.log("createPost > newPost", newPost);

    if (!newPost) {
      // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
      console.log("failed-precondition No data sent", request.body);
      return null;
    }

    let { accountId, collection } = newPost;

    if (!accountId || !collection) {
      // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(newPost));
      console.log("failed-precondition No data sent", newPost);
      return response.send({ data: { error: "MISSING_REQUIRED_FIELDS" } });
    }

    try {
      const accountDoc = await FirestoreRef.collection(
        COLLECTIONS.ACCOUNTS_COLLECTION_NAME
      )
        .doc(accountId)
        .get();
      const account = accountDoc.exists ? accountDoc.data() : {};

      if ((account || {}).modules) {
        const accountRef = accountDoc.ref;
        const counterDoc = await accountRef
          .collection(COLLECTIONS.INDEX_ID)
          .doc(COLLECTIONS.COUNTER_ID)
          .get();
        const counterData = counterDoc.exists ? counterDoc.data() : {};

        const { modules, config } = account;
        console.log("modules", { modules, config });
        if (collection in modules && !modules[collection]) {
          const errorType = ERROR_TYPES.MODULE_NOT_AVAILABLE;
          const errorMessage = "Este módulo não está disponível na conta atual";
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: { collection, modules },
            },
          });
        }
        if (
          `${collection}_included` in config &&
          config[`${collection}_included`]
        ) {
          let limit = Number(config[`${collection}_included`]);
          let count = 0;

          if (collection in counterData) {
            count = counterData[collection];
          } else {
            let posts = await fetchCollection(collection, [
              [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
            ]);
            count = posts.length;
            console.log("posts", { posts: posts.length, limit });
          }

          if (count && limit && count >= limit) {
            let current = count;
            const errorType = ERROR_TYPES.MODULE_LIMIT_REACHED;
            const errorMessage =
              "O limite de [%collection] da conta foi atingido".replace(
                "[%collection]",
                `items`
              );
            return response.send({
              data: {
                error: errorType,
                errorMessage,
                errorType,
                debug: {},
                limit,
                current,
              },
            });
          }
        }
      } else {
        console.error("createPost > No account found");
        const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
        const errorMessage =
          "Ocorreu um erro na requisição. Conta não encontrada.";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: {} },
        });
      }
      const post = await addNewPost(collection, newPost);
      let responseData = {
        ...post,
        id: post.ID,
        ID: post.ID,
      };
      return response.send({ data: { ...responseData } });
    } catch (error) {
      console.error(error);
      const errorType = ERROR_TYPES.GENERIC_ERROR;
      const errorMessage =
        typeof error === "string" ? error : error.message ? error.message : "";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: error },
      });
    }
  });
  return null;
});

exports.segmentationFromStage = functions.https.onRequest(
  async (request, response) => {
    cors(request, response, async () => {
      const requestData = request.body.data;
      console.log("segmentationFromStage > requestData", requestData);

      if (!requestData || !requestData.post) {
        // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
        console.log("failed-precondition No data sent", request.body);
        return null;
      }

      const { post, stage, model } = requestData;
      const { accountId, owner } = post;
      const { collection } = model;

      if (!accountId || !collection) {
        // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(requestData));
        console.log("failed-precondition No data sent", requestData);
        return response.send({ data: { error: "MISSING_REQUIRED_FIELDS" } });
      }

      try {
        const accountDoc = await FirestoreRef.collection(
          COLLECTIONS.ACCOUNTS_COLLECTION_NAME
        )
          .doc(accountId)
          .get();
        const account = accountDoc.exists ? accountDoc.data() : {};

        if ((account || {}).modules) {
          const accountRef = accountDoc.ref;
          const counterDoc = await accountRef
            .collection(COLLECTIONS.INDEX_ID)
            .doc(COLLECTIONS.COUNTER_ID)
            .get();
          const counterData = counterDoc.exists ? counterDoc.data() : {};

          const { modules, config } = account;
          console.log("modules", { modules, config });
          if (collection in modules && !modules[collection]) {
            const errorType = ERROR_TYPES.MODULE_NOT_AVAILABLE;
            const errorMessage =
              "Este módulo não está disponível na conta atual";
            return response.send({
              data: {
                error: errorType,
                errorMessage,
                errorType,
                debug: { collection, modules },
              },
            });
          }
          if (
            `${collection}_included` in config &&
            config[`${collection}_included`]
          ) {
            let limit = Number(config[`${collection}_included`]);
            let count = 0;

            if (collection in counterData) {
              count = counterData[collection];
            } else {
              let posts = await fetchCollection(collection, [
                [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
              ]);
              count = posts.length;
              console.log("posts", { posts: posts.length, limit });
            }

            if (count && limit && count >= limit) {
              let current = count;
              const errorType = ERROR_TYPES.MODULE_LIMIT_REACHED;
              const errorMessage =
                "O limite de [%collection] da conta foi atingido".replace(
                  "[%collection]",
                  `items`
                );
              return response.send({
                data: {
                  error: errorType,
                  errorMessage,
                  errorType,
                  debug: {},
                  limit,
                  current,
                },
              });
            }
          }
        } else {
          console.error("segmentationFromStage > No account found");
          const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
          const errorMessage =
            "Ocorreu um erro na requisição. Conta não encontrada.";
          return response.send({
            data: { error: errorType, errorMessage, errorType, debug: {} },
          });
        }

        let where = [];
        where.push([CONSTANTS.FUNNEL_ID_FIELD, "==", post.ID]);
        where.push(["stage", "==", stage]);
        let deals = await fetchCollection(
          COLLECTIONS.DEALS_COLLECTION_NAME,
          where
        );

        let contactIds = [];
        if (deals.length) {
          contactIds = [
            ...new Set(
              deals.filter((d) => d.contactId).map((d) => d.contactId)
            ),
          ];
        }

        if (!contactIds.length) {
          console.log("No contact found in deals");
          const errorType = ERROR_TYPES.ERROR_ADDING_POST;
          const errorMessage =
            "Não há negócios com contatos associados nesta etapa.";
          return response.send({
            data: { error: errorType, errorMessage, errorType, debug: {} },
          });
        }

        const newPostData = {
          ...model,
          config: {
            ...model.config,
            dinamic: false,
          },
          owner,
          accountId,
          leads: contactIds,
        };

        const newPost = await addNewPost(collection, newPostData);
        let responseData = {
          ...newPost,
          id: newPost.ID,
          ID: newPost.ID,
        };
        return response.send({ data: { ...responseData } });
      } catch (error) {
        console.error(error);
        const errorType = ERROR_TYPES.GENERIC_ERROR;
        const errorMessage =
          typeof error === "string"
            ? error
            : error.message
              ? error.message
              : "";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: error },
        });
      }
    });
    return null;
  }
);

exports.createUser = functions.https.onRequest(async (request, response) => {
  cors(request, response, async () => {
    const newUser = request.body.data;
    console.log("CREATE USER > NEW USER", newUser);

    if (!newUser) {
      // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
      console.log("failed-precondition No data sent", request.body);
      return null;
    }

    const collection = COLLECTIONS.QIUSERS_COLLECTION_NAME;
    let { accountId, email } = newUser;

    if (!accountId || !email) {
      // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(newUser));
      console.log("failed-precondition No data sent", newUser);
      return response.send({ data: { error: "MISSING_REQUIRED_FIELDS" } });
    }

    try {
      const authUserQuery = await getAuthUser(newUser);
      if (authUserQuery.email) {
        const errorType = ERROR_TYPES.EMAIL_EXISTS_ERROR;
        const errorMessage = "Já existe um usuário com o email informado";
        return response.send({
          data: {
            error: errorType,
            errorMessage,
            errorType,
            debug: { authUserQuery },
            authUser: authUserQuery,
          },
        });
      }

      const existingUsers = await fetchCollection(collection, [
        ["email", "==", `${email}`],
      ]);
      const userExists = Boolean(existingUsers.length);
      console.log("userExists", { userExists });

      if (userExists) {
        const errorType = ERROR_TYPES.EMAIL_EXISTS_ERROR;
        const errorMessage = "Já existe um usuário com o email informado";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: {} },
        });
      }

      const accountDoc = await FirestoreRef.collection(
        COLLECTIONS.ACCOUNTS_COLLECTION_NAME
      )
        .doc(accountId)
        .get();
      const account = accountDoc.exists ? accountDoc.data() : {};

      if ((account || {}).modules) {
        const accountRef = accountDoc.ref;
        const counterDoc = await accountRef
          .collection(COLLECTIONS.INDEX_ID)
          .doc(COLLECTIONS.COUNTER_ID)
          .get();
        const counterData = counterDoc.exists ? counterDoc.data() : {};

        const { modules, config } = account;
        console.log("modules", { modules, config });
        if (collection in modules && !modules[collection]) {
          const errorType = ERROR_TYPES.MODULE_NOT_AVAILABLE;
          const errorMessage = "Este módulo não está disponível na conta atual";
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: { collection, modules },
            },
          });
        }
        if (
          `${collection}_included` in config &&
          config[`${collection}_included`]
        ) {
          let limit = Number(config[`${collection}_included`]);
          let count = 0;

          if (collection in counterData) {
            count = counterData[collection];
          } else {
            let posts = await fetchCollection(collection, [
              [CONSTANTS.ACCOUNT_FIELD, "==", accountId],
            ]);
            count = posts.length;
            console.log("posts", { posts: posts.length, limit });
          }

          if (count && limit && count >= limit) {
            let current = count;
            const errorType = ERROR_TYPES.MODULE_LIMIT_REACHED;
            const errorMessage =
              "O limite de [%collection] da conta foi atingido".replace(
                "[%collection]",
                `usuários`
              );
            return response.send({
              data: {
                error: errorType,
                errorMessage,
                errorType,
                debug: {},
                limit,
                current,
              },
            });
          }
        }
      } else {
        console.error("createUser > No account found");
        const errorType = ERROR_TYPES.NO_ACCOUNT_FOUND;
        const errorMessage =
          "Ocorreu um erro na requisição. Conta não encontrada.";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: {} },
        });
      }

      const authUser = await createAuthUser(newUser);
      if (authUser.error) {
        let {
          error,
          error: { code },
        } = authUser;
        const errorType = ERROR_TYPES.NEW_AUTH_USER_ERROR;
        const errorMessage =
          typeof error === "string"
            ? error
            : error.message
              ? error.message
              : "";
        return response.send({
          data: {
            error: errorType,
            errorMessage,
            errorType,
            debug: { ...error, code, authUser },
            code,
          },
        });
      }

      const { uid } = authUser;
      console.log("uid", { uid, authUser });

      if (newUser.password) {
        delete newUser.password;
      }

      const qiuser = await addNewPost(collection, { ...newUser, uid });
      let responseData = {
        ...qiuser,
        uid,
        id: qiuser.ID,
        ID: qiuser.ID,
      };

      return response.send({ data: { ...responseData } });
    } catch (error) {
      console.error(error);
      const errorType = ERROR_TYPES.GENERIC_ERROR;
      const errorMessage =
        typeof error === "string" ? error : error.message ? error.message : "";
      return response.send({
        data: { error: errorType, errorMessage, errorType, debug: error },
      });
    }
  });
  return null;
});

exports.createAffiliate = functions.https.onRequest(
  async (request, response) => {
    cors(request, response, async () => {
      const newUser = request.body.data;
      console.log("CREATE AFFILIATE > NEW USER", newUser);

      if (!newUser) {
        // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
        console.log("failed-precondition No data sent", request.body);
        return null;
      }

      const collection = COLLECTIONS.AFFILIATES_COLLECTION_NAME;
      let { email } = newUser;

      if (!email || !newUser.pagarme) {
        // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(newUser));
        console.log("failed-precondition No data sent", newUser);
        return response.send({ data: { error: "MISSING_REQUIRED_FIELDS" } });
      }

      try {
        const authUserQuery = await getAuthUser(newUser);
        // ---------------------------------------
        // Pode usar o mesmo authUser se já existe
        // ---------------------------------------
        // if ( authUserQuery.email ) {
        // 	const errorType = ERROR_TYPES.EMAIL_EXISTS_ERROR;
        // 	const errorMessage = 'Já existe um usuário com o email informado';
        // 	return response.send({ data: { error: errorType, errorMessage, errorType, debug: { authUserQuery }, authUser: authUserQuery }})
        // }

        const existingUsers = await fetchCollection(collection, [
          ["email", "==", `${email}`],
        ]);
        const userExists = Boolean(existingUsers.length);
        console.log("userExists", { userExists });

        if (userExists) {
          const errorType = ERROR_TYPES.EMAIL_EXISTS_ERROR;
          const errorMessage = "Já existe um usuário com o email informado";
          return response.send({
            data: { error: errorType, errorMessage, errorType, debug: {} },
          });
        }

        const client = await pagarmeClient();
        const { pagarme } = newUser;

        let recipient;

        try {
          Object.keys(pagarme).forEach((k) => {
            if (pagarme[k] === "") pagarme[k] = null;
          });

          let nestedKeys = ["bank_account", "register_information", "metadata"];
          nestedKeys.forEach((k) => {
            Object.keys(pagarme[k]).forEach((key) => {
              if (pagarme[k][key] === "") pagarme[k][key] = null;
            });
          });

          Object.keys(pagarme.register_information).forEach((key, i) => {
            const val = pagarme.register_information[key];
            const label = langMessages[`affiliate.data.${key}`] || key;
            switch (key) {
              case "type":
              case "document_number":
              case "company_name":
              case "managing_partners":
              case "name":
              case "email":
                if (!val) {
                  delete pagarme.register_information[key];
                }
                break;
              case "phone_numbers":
                if (
                  !val ||
                  !val.length ||
                  val.find((f) => !f.ddd || !f.number || !f.type)
                ) {
                  delete pagarme.register_information[key];
                }
                break;
              case "site_url":
                if (!val) {
                  delete pagarme.register_information.site_url;
                }
                break;
              default:
                break;
            }
          });

          recipient = await client.recipients.create(pagarme);
          console.log("recipient", recipient);
        } catch (error) {
          console.log("CREATE AFFILIATE > RECIPIENT > ERROR", error);

          const errorType = ERROR_TYPES.PAGARME_CREATE_RECIPIENT_ERROR;
          const errorMessage =
            typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "";
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: { ...error },
            },
          });
        }

        let authUser;
        if (authUserQuery && authUserQuery.email) {
          authUser = authUserQuery;
        } else {
          authUser = await createAuthUser(newUser);
          if (authUser.error) {
            let {
              error,
              error: { code },
            } = authUser;
            const errorType = ERROR_TYPES.NEW_AUTH_USER_ERROR;
            const errorMessage =
              typeof error === "string"
                ? error
                : error.message
                  ? error.message
                  : "";
            return response.send({
              data: {
                error: errorType,
                errorMessage,
                errorType,
                debug: { ...error, code, authUser },
                code,
              },
            });
          }
        }

        const { uid } = authUser;
        console.log("UID/AUTHUSER", { uid, authUser });

        if (newUser.password) {
          delete newUser.password;
        }

        const affiliate = await addNewPost(collection, {
          ...newUser,
          uid,
          pagarme,
          _pagarme: {
            ...pagarme,
            ...recipient,
          },
        });

        let responseData = {
          ...affiliate,
          uid,
          id: affiliate.ID,
          ID: affiliate.ID,
          [CONSTANTS.AFFILIATE_FIELD]: affiliate.ID,
        };

        return client.recipients
          .update({
            id: recipient.id,
            metadata: {
              uid: affiliate.ID,
            },
          })
          .then((recipient) => {
            console.log("CREATE AFFILIATE > RECIPIENT > SUCCESS", recipient);
            return response.send({ data: { ...responseData } });
          })
          .catch((error) => {
            console.log("CREATE AFFILIATE > RECIPIENT > ERROR", error);

            return response.send({ data: { ...responseData } });
          });
      } catch (error) {
        console.error(error);
        const errorType = ERROR_TYPES.GENERIC_ERROR;
        const errorMessage =
          typeof error === "string"
            ? error
            : error.message
              ? error.message
              : "";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: error },
        });
      }
    });
    return null;
  }
);

exports.updateAffiliate = functions.https.onRequest(
  async (request, response) => {
    cors(request, response, async () => {
      const affiliateUser = request.body.data;
      console.log("updateAffiliate > affiliateUser", affiliateUser);

      if (!affiliateUser) {
        // throw new functions.https.HttpsError('failed-precondition', 'No data sent' + JSON.stringify(request.body));
        console.log("failed-precondition No data sent", request.body);
        return null;
      }

      const collection = COLLECTIONS.AFFILIATES_COLLECTION_NAME;
      let { email } = affiliateUser;

      if (!email || !affiliateUser.pagarme) {
        // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(affiliateUser));
        console.log("failed-precondition No data sent", affiliateUser);
        return response.send({ data: { error: "MISSING_REQUIRED_FIELDS" } });
      }

      try {
        const client = await pagarmeClient();
        const {
          ID,
          pagarme,
          _pagarme: { id },
        } = affiliateUser;

        let recipient;

        try {
          Object.keys(pagarme).forEach((k) => {
            if (pagarme[k] === "") pagarme[k] = null;
          });

          let nestedKeys = ["bank_account", "register_information", "metadata"];
          nestedKeys.forEach((k) => {
            Object.keys(pagarme[k]).forEach((key) => {
              if (pagarme[k][key] === "") pagarme[k][key] = null;
            });
          });

          let newData = { ...pagarme };

          // register_information cannot be updated
          if (newData.register_information) {
            delete newData.register_information;
          }

          recipient = await client.recipients.update({
            id,
            ...newData,
          });

          console.log("recipient", recipient);
        } catch (error) {
          console.log("updateAffiliate > recipient > error", error);

          const errorType = ERROR_TYPES.PAGARME_UPDATE_RECIPIENT_ERROR;
          const errorMessage =
            typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "";
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: { ...error },
            },
          });
        }

        if (affiliateUser.password) {
          delete affiliateUser.password;
        }

        let updatedData = {
          ...affiliateUser,
          pagarme,
          _pagarme: {
            ...pagarme,
            ...recipient,
          },
        };

        let responseData = updatedData;

        return updatePost(collection, updatedData, ID)
          .then((recipient) => {
            console.log(recipient);
            return response.send({ data: { ...responseData } });
          })
          .catch((error) => {
            const errorType = ERROR_TYPES.PAGARME_UPDATE_RECIPIENT_ERROR;
            const errorMessage =
              typeof error === "string"
                ? error
                : error.message
                  ? error.message
                  : "";
            return response.send({
              data: {
                error: errorType,
                errorMessage,
                errorType,
                debug: { ...error },
              },
            });
          });
      } catch (error) {
        console.error(error);
        const errorType = ERROR_TYPES.GENERIC_ERROR;
        const errorMessage =
          typeof error === "string"
            ? error
            : error.message
              ? error.message
              : "";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: error },
        });
      }
    });
    return null;
  }
);

exports.createEmail = functions.https.onRequest(createEmail);

exports.prepareEmail = functions.https.onRequest(prepareEmail);

exports.fetchContactsFromTriggers = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .https.onRequest(fetchContactsFromTriggers);

exports.createTransaction = functions.https.onRequest(
  pagarmeApi.createTransaction
);

exports.createSubscription = functions.https.onRequest(
  pagarmeApi.createSubscription
);

exports.getSubscription = functions.https.onRequest(pagarmeApi.getSubscription);

exports.getSubscriptionTransactions = functions.https.onRequest(
  pagarmeApi.getSubscriptionTransactions
);

exports.cancelSubscription = functions.https.onRequest(
  pagarmeApi.cancelSubscription
);

exports.getTransaction = functions.https.onRequest(pagarmeApi.getTransaction);

exports.handleParticipants = functions.firestore
  .document(
    `${COLLECTIONS.EVENTS_COLLECTION_NAME}/{docId}/${COLLECTIONS.PARTICIPANTS_COLLECTION_NAME}/{contactId}`
  )
  .onWrite((change, context) => {
    funcCounter++;
    console.log(`handleParticipants > funcCounter:`, funcCounter);
    if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
      console.error(
        "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
          funcCounter
      );
      return null;
    }

    const { before, after } = change;
    const { params } = context;

    const collection = COLLECTIONS.PARTICIPANTS_COLLECTION_NAME;
    const contactId = params.contactId;
    const docId = params.docId;
    const docRef = after.exists ? change.after.ref : null;

    const contextData = {
      collection: COLLECTIONS.EVENTS_COLLECTION_NAME,
      id: docId,
      contactId,
    };

    const oldData = (before.exists && before.data()) || {};
    const newData = (after.exists && after.data()) || {};
    const diffObj = diffObjects(newData, oldData);

    if (!oldData.systemUpdate && newData.systemUpdate) {
      console.log(
        `handleParticipants > ${collection} > systemUpdate > newData`,
        { newData, diffObj }
      );
      return updateRef(docRef, { systemUpdate: FieldValue.delete() });
    } else if (oldData.systemUpdate && !newData.systemUpdate) {
      console.log(
        `handleParticipants > ${collection} > systemUpdate > oldData`,
        { oldData, diffObj }
      );
      return null;
    }

    let triggerName = "";

    let promises = [];

    promises.push(
      fetchPost(COLLECTIONS.EVENTS_COLLECTION_NAME, docId).then((post) => {
        const event = post;

        // LOGS
        let logData = {};

        if (change.after.exists) {
          logData = newData;
        } else if (before.exists) {
          logData = oldData;
        }

        triggerName = "";
        if (!change.after.exists) {
          triggerName = APP_TRIGGERS.APP_TRIGGER_REMOVED_PARTICIPANT;
        } else if (newData.ID && !oldData.ID) {
          triggerName = APP_TRIGGERS.APP_TRIGGER_ADDED_PARTICIPANT;
        }
        if (triggerName) {
          promises.push(
            addNewLog({
              contactId,
              user_id: contactId,
              operator_id: 0,
              id: docId,
              collection: COLLECTIONS.EVENTS_COLLECTION_NAME,
              trigger: triggerName,
              date: nowToISO(),
              owner: event.owner || "",
              accountId: event.accountId || "",
              data: logData,
            })
          );
        }

        triggerName = "";
        if (change.after.exists && newData.confirmed && !oldData.confirmed) {
          triggerName = APP_TRIGGERS.APP_TRIGGER_CONFIRMED_PARTICIPANT;
        } else if (!newData.confirmed && oldData.confirmed) {
          triggerName = APP_TRIGGERS.APP_TRIGGER_UNCONFIRMED_PARTICIPANT;
        }
        if (triggerName) {
          promises.push(
            addNewLog({
              contactId,
              user_id: contactId,
              operator_id: 0,
              id: docId,
              collection: COLLECTIONS.EVENTS_COLLECTION_NAME,
              trigger: triggerName,
              date: nowToISO(),
              owner: event.owner || "",
              accountId: event.accountId || "",
              data: logData,
            })
          );
        }

        triggerName = "";
        if (change.after.exists && newData.checkin && !oldData.checkin) {
          triggerName = APP_TRIGGERS.APP_TRIGGER_CHECKEDIN;
        } else if (!newData.checkin && oldData.checkin) {
          let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
          let eMatch =
            event.end &&
            moment(event.end).isValid() &&
            new Date(event.end).getTime() < new Date(momentNowISO).getTime();
          triggerName = eMatch ? APP_TRIGGERS.APP_TRIGGER_DIDNTCHECKIN : "";
        }
        if (triggerName) {
          promises.push(
            addNewLog({
              contactId,
              user_id: contactId,
              operator_id: 0,
              id: docId,
              collection: COLLECTIONS.EVENTS_COLLECTION_NAME,
              trigger: triggerName,
              date: nowToISO(),
              owner: event.owner || "",
              accountId: event.accountId || "",
              data: logData,
            })
          );
        }

        return null;
      })
    );

    if (after.exists) {
      let updatedData = {};

      // createdAt
      if (!newData.createdAt) {
        updatedData.createdAt = uniqueTimestamp(after.updateTime.toMillis());
      }

      // updatedAt
      if (!newData.updatedAt || oldData.updatedAt === newData.updatedAt) {
        updatedData.updatedAt = uniqueTimestamp(after.updateTime.toMillis());
      }

      // merge changes
      const mergedData = { ...newData, ...updatedData };

      // sanitize Field Types
      const sanitizedData = sanitizeFieldTypes(mergedData);

      let hasUpdates = false;
      const updateKeys = [];

      if (after.exists) {
        Object.keys(sanitizedData).forEach((k) => {
          const updatedValue = sanitizedData[k];
          const newValue = newData[k];
          if (helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue))
            return;
          if (
            typeof newValue !== typeof updatedValue ||
            (k in updatedData &&
              JSON.stringify(newValue) !== JSON.stringify(updatedValue))
          ) {
            hasUpdates = true;
            updateKeys.push(k);
          }
        });
      }

      if (hasUpdates && change.after.exists) {
        let updateObj = { systemUpdate: true };
        updateKeys.forEach((k) => {
          updateObj[k] = sanitizedData[k];
        });
        promises.push(updateRef(change.after.ref, updateObj));
        console.log("handleParticipants > hasUpdates", {
          updateKeys,
          updateObj,
        });
      }
    }

    return Promise.all(promises);
  });

exports.onDeleteDocs = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .firestore.document(`{collection}/{docId}`)
  .onDelete(async (doc, context) => {
    const { params } = context;
    const docData = doc.data();
    const docRef = doc.ref;

    const collection = params.collection;
    const docId = params.docId;
    const docPath = `${collection}/${docId}`;

    if (docId === COLLECTIONS.INDEX_ID) {
      return null;
    }

    if (COLLECTIONS.coreCollections.includes(collection)) {
      const promises = [];

      const { status } = docData;

      if (status !== CONSTANTS.TRASH_STATUS) {
        docData.deleted = true;
        docData.status = CONSTANTS.DELETED_STATUS;
        docData.deletedAt = doc.updateTime.toMillis();

        $GLOBALS.state.remove(collection, docData);

        const trashSubCollection = FirestoreRef.collection(
          COLLECTIONS.TRASH_COLLECTION_NAME
        )
          .doc("deleted")
          .collection(collection);
        promises.push(trashSubCollection.doc(`${docId}`).set(docData));

        // const newDocPath = `${COLLECTIONS.TRASH_COLLECTION_NAME}/deleted/${collection}/${docId}`;
        // promises.push(migrateSubCollections(docPath, newDocPath))
        promises.push(deleteSubCollections(docPath));
      } else {
        const keywordsCollection =
          KEYWORDS_SUBCOLLECTIONS_MAP[collection] ||
          KEYWORDS_SUBCOLLECTION_NAME;
        const newDocPath = `${COLLECTIONS.TRASH_COLLECTION_NAME}/posts/${collection}/${docId}`;
        promises.push(
          migrateSubCollections(docPath, newDocPath, [keywordsCollection])
        );
      }

      switch (collection) {
        case COLLECTIONS.QIUSERS_COLLECTION_NAME:
          docData.uid && promises.push(deleteAuthUser(docData.uid));
          break;
        default:
          break;
      }

      // Logs
      const logKeys = [
        CONSTANTS.DATE_FIELD,
        CONSTANTS.MODIFIED_FIELD,
        ...CONSTANTS.RELATIONAL_FIELDS,
      ];
      const logData = {};

      logKeys.forEach((key) => {
        if (key in docData) {
          logData[key] = docData[key];
        }
      });

      let triggerName =
        COLLECTION_TRIGGERS[collection].removed ||
        APP_TRIGGERS.APP_TRIGGER_REMOVED;
      let contactId =
        collection === COLLECTIONS.LEADS_COLLECTION_NAME
          ? docId
          : docData.contactId || "";

      const log = {
        contactId,
        user_id: contactId,
        operator_id: docData.operatorId || 0,
        id: docId,
        collection,
        trigger: triggerName,
        date: nowToISO(),
        owner: logData.owner || "",
        accountId: logData.accountId || "",
        data: logData,
        context: docData.context || {},
      };

      promises.push(addNewLog(log));

      return Promise.all(promises);
    }

    return null;
  });

exports.onCreateDocs = functions.firestore
  .document(`{collection}/{docId}`)
  .onCreate((doc, context) => {
    const { params } = context;
    const docData = doc.data();

    const collection = params.collection;
    const docId = params.docId;

    if (docId === COLLECTIONS.INDEX_ID) {
      return null;
    }

    if (!COLLECTIONS.coreCollections.includes(collection)) {
      return updateRef(doc.ref, {
        id: docId,
        ID: docId,
        createdAt: uniqueTimestamp(doc.createTime.toMillis()),
        updatedAt: uniqueTimestamp(doc.createTime.toMillis()),
      });
    }

    return null;
  });

const onCreateDoc = (docData) => {
  let promises = [];

  const docId = docData.ID;
  const { collection } = docData;

  if (COLLECTIONS.coreCollections.includes(collection)) {
    let triggerName =
      COLLECTION_TRIGGERS[collection].added || APP_TRIGGERS.APP_TRIGGER_ADDED;
    let contactId =
      collection === COLLECTIONS.LEADS_COLLECTION_NAME
        ? docId
        : docData.contactId || "";

    const logKeys = [
      ...new Set([
        CONSTANTS.DATE_FIELD,
        CONSTANTS.MODIFIED_FIELD,
        ...COLLECTIONS.coreCollections,
        ...CONSTANTS.RELATIONAL_FIELDS,
      ]),
    ];
    const logData = {};

    logKeys.forEach((key) => {
      if (key in docData) {
        logData[key] = docData[key];
      }
    });

    if ((docData.context || {}).campaignId) {
      logData.campaignId = docData.context.campaignId;
    }

    switch (collection) {
      case COLLECTIONS.TICKETS_COLLECTION_NAME:
        logData["total"] = (docData.data || {}).total || 0;
        logData["value"] = (docData.data || {}).value || 0;
        logData["operator_id"] = (docData.context || {}).operator_id || 0;
        break;
      case COLLECTIONS.DEALS_COLLECTION_NAME:
        logData["value"] = (docData.data || {}).value || 0;
        logData["operator_id"] = (docData.context || {}).operator_id || 0;
        break;
      default:
        break;
    }

    const log = {
      contactId,
      user_id: contactId,
      operator_id:
        docData.operatorId || docData.author || logData.operatorId || 0,
      id: docId,
      collection,
      trigger: triggerName,
      date: nowToISO(),
      owner: logData.owner || "",
      accountId: logData.accountId || "",
      data: logData,
      context: docData.context || {},
    };

    promises.push(addNewLog(log));
  }

  return Promise.all(promises);
};

exports.sanitizeData = functions
  .runWith({ timeoutSeconds: 240, memory: "512MB" })
  .firestore.document("{collection}/{docId}")
  .onWrite(async (change, context) => {
    funcCounter++;
    if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
      console.error(
        "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
          funcCounter
      );
      return null;
    }

    console.log("***-------------------------------------------------------");
    console.log(
      `sanitizeData started @ ${moment().format(CONSTANTS.MOMENT_LOCAL)} `
    );
    console.log(
      `sanitizeData ${context.params.collection} funcCounter:`,
      funcCounter
    );

    const { before, after } = change;
    const { params } = context;

    const collection = params.collection;
    const docId = params.docId;
    const docRef = after.exists ? change.after.ref : null;
    const doc = after.exists ? change.after : null;

    if (docId === COLLECTIONS.INDEX_ID) {
      return null;
    }

    let promises = [];
    let updaterUser = 0;
    let updaterUserName = 0;
    const oldData = (before.exists && before.data()) || {};
    const newData = (after.exists && after.data()) || {};
    const updatedData = {};

    // console.log("SANITIZEDATA > oldata", oldData);
    // console.log("SANITIZEDATA > newData", newData);

    const diffObj = diffObjects(newData, oldData);

    if (!oldData.systemUpdate && newData.systemUpdate) {
      console.log(`sanitizeData > ${collection} > systemUpdate > newData`, {
        newData,
        diffObj,
      });
      return updateRef(docRef, { systemUpdate: FieldValue.delete() });
    } else if (oldData.systemUpdate && !newData.systemUpdate) {
      console.log(`sanitizeData > ${collection} > systemUpdate > oldData`, {
        oldData,
        diffObj,
      });
      return null;
    }

    let { owner, accountId } = { ...oldData, ...newData };

    owner = owner || "";
    accountId = accountId || "";

    try {
      if (after.exists) {
        if (COLLECTIONS.coreCollections.includes(collection)) {
          if (
            owner &&
            (!accountId || accountId === CONSTANTS.ORPHANS_ACCOUNT)
          ) {
            try {
              const accountData = await getAccountByOwner(owner);
              if (
                ![accountId, CONSTANTS.ORPHANS_ACCOUNT].includes(
                  (accountData || {}).ID || ""
                )
              ) {
                accountId = accountData.ID;
                newData.accountId = accountId;
                updatedData.accountId = accountId;
                promises.push(updateRef(docRef, { accountId }));
              }
            } catch (error) {
              console.log(
                `sanitizeData > ${collection} > getAccountByOwner > error`,
                { error }
              );
            }
          }

          // date | createdAt
          if (!newData.date || !newData.createdAt) {
            updatedData.date = nowToISO();
            updatedData.modified = nowToISO();
            updatedData.createdAt = uniqueTimestamp(
              after.updateTime.toMillis()
            );
            updatedData.updatedAt = uniqueTimestamp(
              after.updateTime.toMillis()
            );
          }
          // updatedAt modified
          else if (
            !newData.modified ||
            !newData.updatedAt ||
            oldData.updatedAt === newData.updatedAt
          ) {
            if (!newData.modified) {
              updatedData.modified = nowToISO();
            }
            updatedData.updatedAt = uniqueTimestamp(
              after.updateTime.toMillis()
            );
          }

          // locale
          if (!newData.locale) {
            updatedData.locale = DEFAULT_LOCALE;
          }

          // id
          if (newData.id !== docId) {
            updatedData.id = docId;
          }

          // ID
          if (newData.ID !== docId) {
            updatedData.ID = docId;
          }

          // collection
          if (!newData.collection) {
            updatedData.collection = collection;
          }

          // tags
          if (Array.isArray(newData.tags)) {
            let tags = [...new Set(newData.tags)];
            if (tags.length !== newData.tags.length) {
              updatedData.tags = tags;
            }
          } else if (helpers.isEmptyObject(newData.tags)) {
            updatedData.tags = [];
          }

          // owner
          if (!newData.owner) {
            updatedData.owner = CONSTANTS.ORPHANS_OWNER;
          }

          // accountId
          if (
            !newData.accountId &&
            newData.accountId !== CONSTANTS.ORPHANS_ACCOUNT
          ) {
            if (
              collection !== COLLECTIONS.ACCOUNTS_COLLECTION_NAME &&
              collection !== COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME
            ) {
              updatedData.accountId = CONSTANTS.ORPHANS_ACCOUNT;
            }
          }

          // template models
          if (newData.config && "use_as_template" in newData.config) {
            if (
              Boolean(newData.config.use_as_template) !==
              newData.config.use_as_template
            ) {
              updatedData.config = {
                ...(newData.config || {}),
                ...(updatedData.config || {}),
                use_as_template: Boolean(newData.config.use_as_template),
              };
            }
          }

          // keywords
          let keywordsSource;

          switch (collection) {
            case COLLECTIONS.DEALS_COLLECTION_NAME:
              var contactId = newData.contactId || "";

              var lead = {};
              if (contactId) {
                try {
                  lead = await fetchPost(
                    COLLECTIONS.LEADS_COLLECTION_NAME,
                    contactId
                  );
                } catch (error) {
                  console.log(
                    `sanitizeData > ${collection} > updateKeywordsSubcollection > err`,
                    { error }
                  );
                }
              }

              var mergedContact = {
                ...(lead || {}),
                ...(newData.contact || {}),
                ...(updatedData.contact || {}),
              };

              Object.keys(mergedContact).forEach((field, i) => {
                const val = mergedContact[field];
                if (lead[field] && val === "") {
                  mergedContact[field] = lead[field];
                }
              });

              keywordsSource = {
                ...mergedContact,
                ...newData,
                ...updatedData,
              };

              break;
            default:
              keywordsSource = { ...newData, ...updatedData };
              break;
          }

          const { keywordFields, substrFields } =
            KEYWORDS_FIELDS_MAP[collection];

          const oldKeywords = oldData.keywords || [];
          const hasNewkeywords =
            keywordFields.map((k) => keywordsSource[k] || "").join("") !==
            oldKeywords.join("");

          if (!newData.keywords || hasNewkeywords) {
            const keywordMap = {};
            keywordFields.forEach((field) => {
              keywordMap[field] = keywordsSource[field] || "";
            });
            updatedData.keywords = generateKeywords(
              keywordMap,
              2,
              100,
              substrFields
            );
          }
        }

        switch (collection) {
          case COLLECTIONS.FIELDS_COLLECTION_NAME:
            var fieldName = `${CONSTANTS.CUSTOM_FIELDS_GROUP}:${docId}`;
            var fieldLabel = (newData.data || {}).label || "";
            if (newData.name !== fieldName) {
              updatedData.name = fieldName;
            }
            if (newData.title !== fieldLabel) {
              updatedData.title = fieldLabel;
            }
            if (Boolean(newData.data) && newData.data.name !== fieldName) {
              updatedData.data = {
                ...newData.data,
                ...(updatedData.data || {}),
                name: fieldName,
              };
            }
            break;
          default:
            break;
        }
      }

      // merge changes
      const mergedData = { ...newData, ...updatedData };
      console.log("ADDNEWLOG > MERGEDDATA", mergedData);
      const logs = mergedData.logs;
      // sanitize Field Types
      const sanitizedData = sanitizeFieldTypes(mergedData);
      // console.log("SANITIZEDATA", sanitizedData);
      const newStatus = sanitizedData.status;

      if (!after.exists) {
        promises.push(handleNotifications({}, change, context));
        promises.push(handleDeadlines({}, change, context));
        $GLOBALS.state.remove(collection, docId);
      }

      let sanitizedObj = {},
        onAfterUpdate;

      if (after.exists && newStatus !== CONSTANTS.TRASH_STATUS) {
        $GLOBALS.state.set(collection, sanitizedData);

        promises.push(handleNotifications(sanitizedData, change, context));
        promises.push(handleDeadlines(sanitizedData, change, context));
        promises.push(handleChecklists(sanitizedData, change, context));

        // console.log("SANITEZEDATA > BEFORE LOGS", sanitizedData);
        if (logs) {
          if (logs.hasOwnProperty("updated")) {
            try {
              updaterUser = logs.updated.operator_id;
              const userAuthor = (
                await FirestoreRef.collection(
                  COLLECTIONS.QIUSERS_COLLECTION_NAME
                )
                  .doc(updaterUser)
                  .get()
              ).data();

              updaterUserName = `${userAuthor.firstName || ""} ${
                userAuthor.lastName || ""
              }`;
            } catch (error) {
              updaterUser = 0;
              updaterUserName = "System";
            }
          }

          if (logs.hasOwnProperty("added")) {
            try {
              updaterUser = logs.added.operator_id;
              const userAuthor = (
                await FirestoreRef.collection(
                  COLLECTIONS.QIUSERS_COLLECTION_NAME
                )
                  .doc(updaterUser)
                  .get()
              ).data();
              updaterUserName = `${userAuthor.firstName || ""} ${
                userAuthor.lastName || ""
              }`;
            } catch (error) {
              updaterUser = 0;
              updaterUserName = "System";
            }
          }
        }

        if (
          sanitizedData.hasOwnProperty("logs") &&
          sanitizedData.logs.hasOwnProperty("added") &&
          sanitizedData.logs.added.operator_id === ""
        ) {
          // console.log("SANITIZEDATA > INSIDE TESTE START", sanitizedData);
          let logsAdded = sanitizedData.logs.added;

          logsAdded.operator_id = updaterUser;
          logsAdded.user = updaterUserName;
          logsAdded.date = sanitizedData.date;
          sanitizedData.logs.added = logsAdded;
          // console.log("SANITIZEDATA > INSIDE TESTE FINISH", sanitizedData);
        }

        // console.log("SANITIZEDATA > AFTER TESTE", sanitizedData);

        switch (collection) {
          case COLLECTIONS.ACCOUNTS_COLLECTION_NAME:
            [sanitizedObj, onAfterUpdate] = await sanitizeAccount(
              sanitizedData,
              change,
              context
            );
            break;
          case COLLECTIONS.DEALS_COLLECTION_NAME:
            [sanitizedObj, onAfterUpdate] = await sanitizeDealData(
              sanitizedData,
              change,
              context
            );
            break;
          case COLLECTIONS.QIUSERS_COLLECTION_NAME:
          case COLLECTIONS.AFFILIATES_COLLECTION_NAME:
            [sanitizedObj, onAfterUpdate] = await sanitizeQIUserData(
              sanitizedData,
              change,
              context
            );
            break;
          case COLLECTIONS.LEADS_COLLECTION_NAME:
            [sanitizedObj, onAfterUpdate] = await sanitizeLeadData(
              sanitizedData,
              change,
              context
            );
            break;
          case COLLECTIONS.LANDING_PAGES_COLLECTION_NAME:
            [sanitizedObj, onAfterUpdate] = await sanitizeLandingPageData(
              sanitizedData,
              change,
              context
            );
            break;
          default:
            break;
        }

        if (COLLECTIONS.coreCollections.includes(collection)) {
          promises.push(updateKeywordsSubcollection(doc));
        }

        if (
          COLLECTIONS.coreCollections.includes(collection) &&
          accountId &&
          accountId !== CONSTANTS.ORPHANS_ACCOUNT
        ) {
          let updatedAtValue =
            newData[CONSTANTS.UPDATED_FIELD] ||
            sanitizedData[CONSTANTS.UPDATED_FIELD];

          if (updatedAtValue) {
            let updatedAtSnap = await FirestoreRef.collection(collection)
              .where(CONSTANTS.ACCOUNT_FIELD, "==", accountId)
              .where(CONSTANTS.UPDATED_FIELD, "in", [
                sanitizedData[CONSTANTS.UPDATED_FIELD] || 0,
                newData[CONSTANTS.UPDATED_FIELD] || 0,
              ])
              .get();

            if (updatedAtSnap.size > 1) {
              console.log(
                `sanitizeData > ${collection} > updatedAtSnap`,
                updatedAtSnap.size,
                updatedAtValue
              );
              let incrementedValue = parseFloat(updatedAtValue);
              updatedAtSnap.forEach((d) => {
                incrementedValue++;
                promises.push(
                  updateRef(d.ref, {
                    [CONSTANTS.UPDATED_FIELD]:
                      uniqueTimestamp(incrementedValue),
                    systemUpdate: true,
                  })
                );
              });
            }
          }
        }
      }

      if (after.exists) {
        let updateObj = { ...sanitizedObj };
        const updateKeys = Object.keys(updateObj);

        Object.keys(sanitizedData).forEach((k) => {
          const updatedValue = sanitizedData[k];
          const newValue = newData[k];
          if (
            typeof newValue !== typeof updatedValue ||
            (k in updatedData &&
              !updateKeys.includes(k) &&
              JSON.stringify(newValue) !== JSON.stringify(updatedValue))
          ) {
            updateKeys.push(k);
          }
        });

        if (Boolean(docRef) && updateKeys.length) {
          console.log(`sanitizeData > ${collection} > hasUpdates`, updateKeys);

          updateObj.systemUpdate = true;
          updateKeys.forEach((k) => {
            updateObj[k] =
              k in sanitizedObj ? sanitizedObj[k] : sanitizedData[k];
          });

          await updateRef(docRef, updateObj);
          $GLOBALS.state.set(collection, sanitizedData);
        }
      }

      if (onAfterUpdate) await onAfterUpdate();

      if (
        after.exists &&
        before.exists &&
        COLLECTIONS.coreCollections.includes(collection)
      ) {
        let noLog = [
          CONSTANTS.ID_FIELD,
          CONSTANTS.LOG_RELATED_ID_FIELD,
          CONSTANTS.KEYWORDS_FIELD,
          CONSTANTS.DATE_FIELD,
          CONSTANTS.MODIFIED_FIELD,
          CONSTANTS.UPDATED_FIELD,
          CONSTANTS.CREATED_FIELD,
          "systemUpdate",
          "stats",
          "views",
          "logs",
        ];

        const diffObj = diffObjects(sanitizedData, oldData);
        console.log(`sanitizeData > ${collection} > diffObj`, { diffObj });

        let logData = {};
        Object.keys(diffObj).forEach((k) => {
          if (noLog.includes(k)) return;
          logData[k] = diffObj[k];
          logData[`${k}_old`] = oldData[k] || null;
        });

        logData = { ...logData, operator_name: updaterUserName };

        console.log("SANITIZEDATA > LOG DATA", logData);

        Object.keys(logData).length &&
          promises.push(
            addNewLog({
              contactId: 0,
              user_id: updaterUser,
              operator_id: updaterUser,
              id: docId,
              collection,
              trigger: COLLECTION_TRIGGERS[collection].changed,
              date: nowToISO(),
              owner,
              accountId,
              data: logData,
            })
          );
      }

      if (after.exists && COLLECTIONS.coreCollections.includes(collection)) {
        const contactId =
          collection === COLLECTIONS.LEADS_COLLECTION_NAME
            ? docId
            : sanitizedData.contactId || "";
        const logKeys = [
          CONSTANTS.DATE_FIELD,
          CONSTANTS.MODIFIED_FIELD,
          ...CONSTANTS.RELATIONAL_FIELDS,
        ];
        const logData = {};

        logKeys.forEach((key) => {
          if (change.after.exists && key in sanitizedData) {
            logData[key] = sanitizedData[key];
          } else if (!change.after.exists && key in oldData) {
            logData[key] = oldData[key];
          }
        });

        let oldStores = (oldData && oldData.stores) || [];
        let addedStores = (sanitizedData.stores || []).filter(
          (l) => !oldStores.includes(l)
        );
        let removedStores = oldStores.filter(
          (l) => !(sanitizedData.stores || []).includes(l)
        );

        let storesData = {
          oldStores,
          stores: sanitizedData.stores || [],
        };
        if (addedStores.length) {
          addedStores.forEach((storeId) => {
            promises.push(
              addNewLog({
                contactId,
                user_id: contactId,
                operator_id: sanitizedData.operatorId || updaterUser || 0,
                id: storeId,
                collection: COLLECTIONS.STORES_COLLECTION_NAME,
                trigger: APP_TRIGGERS.APP_TRIGGER_ADDED_TO_STORE,
                date: momentNow().format(CONSTANTS.MOMENT_ISO),
                owner,
                accountId,
                data: { ...logData, ...storesData },
              })
            );
          });
        }
        if (removedStores.length) {
          removedStores.forEach((storeId) => {
            promises.push(
              addNewLog({
                contactId,
                user_id: contactId,
                operator_id: sanitizedData.operatorId || updaterUser || 0,
                id: storeId,
                collection: COLLECTIONS.STORES_COLLECTION_NAME,
                trigger: APP_TRIGGERS.APP_TRIGGER_REMOVED_FROM_STORE,
                date: momentNow().format(CONSTANTS.MOMENT_ISO),
                owner,
                accountId,
                data: { ...logData, ...storesData },
              })
            );
          });
        }

        let oldTags = (oldData && oldData.tags) || [];
        let addedTags = (sanitizedData.tags || []).filter(
          (l) => !oldTags.includes(l)
        );
        let removedTags = oldTags.filter(
          (l) => !(sanitizedData.tags || []).includes(l)
        );

        let tagsData = {
          oldTags: oldTags,
          tags: sanitizedData.tags || [],
        };

        console.log(`sanitizeData > ${collection} > diffTags`, {
          addedTags,
          removedTags,
        });
        if (addedTags.length) {
          addedTags.forEach((tagId) => {
            promises.push(
              addNewLog({
                contactId,
                user_id: contactId,
                operator_id: sanitizedData.operatorId || updaterUser || 0,
                id: docId,
                collection,
                trigger: APP_TRIGGERS.APP_TRIGGER_TAG_ADDED,
                context: {
                  taxId: tagId,
                  taxName: COLLECTIONS.TAGS_TAXONOMY_NAME,
                  taxCollection: collection,
                  operator_id: sanitizedData.operatorId || updaterUser || 0,
                },
                date: momentNow().format(CONSTANTS.MOMENT_ISO),
                owner,
                accountId,
                data: { ...logData, ...tagsData },
              })
            );
          });
        }

        if (removedTags.length) {
          removedTags.forEach((tagId) => {
            promises.push(
              addNewLog({
                contactId,
                user_id: contactId,
                operator_id: sanitizedData.operatorId || updaterUser || 0,
                id: docId,
                collection,
                trigger: APP_TRIGGERS.APP_TRIGGER_TAG_REMOVED,
                context: {
                  taxId: tagId,
                  taxName: COLLECTIONS.TAGS_TAXONOMY_NAME,
                  taxCollection: COLLECTIONS.LEADS_COLLECTION_NAME,
                  operator_id: sanitizedData.operatorId || updaterUser || 0,
                },
                date: momentNow().format(CONSTANTS.MOMENT_ISO),
                owner,
                accountId,
                data: { ...logData, ...tagsData },
              })
            );
          });
        }

        console.log(
          "SANITIZEDATA > IGSIDS",
          sanitizedData.ig_contacts,
          "OLD",
          oldData.ig_contacts
        );

        let oldIgsIds = (oldData && oldData.ig_contacts) || [];
        let addedIgsIdsTags = (sanitizedData.ig_contacts || []).filter(
          (l) => !oldIgsIds.includes(l)
        );
        let removedIgsIds = oldIgsIds.filter(
          (l) => !(sanitizedData.ig_contacts || []).includes(l)
        );

        let igsData = {
          oldIgsIds: oldIgsIds,
          IgsIds: sanitizedData.ig_contacts || [],
        };
        if (addedIgsIdsTags.length) {
          console.log(
            "SANITIZEDATA > IGSIDS > addedIgsIdsTags",
            addedIgsIdsTags
          );

          console.log(
            "SANITIZEDATA > IGSIDS > SANITEZED addedIgsIdsTags",
            sanitizedData
          );
          addedIgsIdsTags.forEach((IgId) => {
            promises.push(
              addNewLog({
                contactId: sanitizedData.id,
                user_id: sanitizedData.contactId,
                operator_id: sanitizedData.operatorId || updaterUser || 0,
                id: docId,
                collection: COLLECTIONS.LEADS_COLLECTION_NAME,
                trigger: APP_TRIGGERS.APP_TRIGGER_INTERATION_ADDED,
                context: {
                  igSid: IgId.ig_sid,
                  igName: IgId.ig_username,
                  collection: COLLECTIONS.SHOTX_COLLECTION_NAME,
                  name: collection,
                  instanceId: IgId.instanceId,
                  operator_id: sanitizedData.operatorId || updaterUser || 0,
                },
                date: momentNow().format(CONSTANTS.MOMENT_ISO),
                owner,
                accountId,
                data: { ...logData, ...igsData },
              })
            );
          });
        }

        if (removedIgsIds.length) {
          console.log("SANITIZEDATA > IGSIDS > removed", removedIgsIds);
          removedIgsIds.forEach((IgId) => {
            promises.push(
              addNewLog({
                contactId: sanitizedData.id,
                user_id: sanitizedData.contactId,
                operator_id: sanitizedData.operatorId || updaterUser || 0,
                id: docId,
                collection: COLLECTIONS.LEADS_COLLECTION_NAME,
                trigger: APP_TRIGGERS.APP_TRIGGER_INTERATION_REMOVED,
                context: {
                  igSid: IgId.ig_sid,
                  igName: IgId.ig_username,
                  collection: collection,
                  name: collection,
                  operator_id: sanitizedData.operatorId || updaterUser || 0,
                },
                date: momentNow().format(CONSTANTS.MOMENT_ISO),
                owner,
                accountId,
                data: { ...logData, ...igsData },
              })
            );
          });
        }
      }
      if (newStatus === CONSTANTS.TRASH_STATUS) {
        promises.push(trashPost(collection, sanitizedData, oldData));
      }

      if (promises.length) {
        console.log(`SANITIZEDATA > ${collection} > promises`, promises.length);
        return Promise.all(promises)
          .then(async (results) => {
            /* results.forEach((result,r)=>{
					console.log('promises > result',result);
					return null;
				}) */

            if (!before.exists && Boolean(docRef)) {
              let updatedDocRef = await docRef.get();
              let updatedDocData = updatedDocRef.exists && updatedDocRef.data();
              return updatedDocData ? onCreateDoc(updatedDocData) : null;
            }
            return null;
          })
          .catch((err) => {
            console.error(err);
            return null;
          });
      }
    } catch (error) {
      const fnName = "sanitizeData";
      const docData = {
        accountId: newData.accountId || oldData.accountId,
        collection,
        docId,
        data: {
          oldData,
          newData,
          updatedData,
        },
      };

      return functionsLog(fnName, error, docData);
    }
    return null;
  });

exports.sanitizeTaxonomy = functions.firestore
  .document(
    `${COLLECTIONS.TAXONOMIES_COLLECTION_NAME}/{collection}/{taxName}/{docId}`
  )
  .onWrite(async (change, context) => {
    funcCounter++;
    console.log("sanitizeTaxonomy funcCounter:", funcCounter);
    if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
      console.error(
        "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
          funcCounter
      );
      return null;
    }

    const { before, after } = change;
    const { params } = context;
    const { collection, taxName, docId } = params;

    const docRef = after.exists ? change.after.ref : null;
    const oldData = (before.exists && before.data()) || {};
    const newData = (after.exists && after.data()) || {};
    const updatedData = {};
    const diffObj = diffObjects(newData, oldData);

    if (!oldData.systemUpdate && newData.systemUpdate) {
      console.log(
        `sanitizeTaxonomy > ${taxName} > ${collection} > systemUpdate > newData`,
        { newData, diffObj }
      );
      return updateRef(docRef, { systemUpdate: FieldValue.delete() });
    } else if (oldData.systemUpdate && !newData.systemUpdate) {
      console.log(
        `sanitizeTaxonomy > ${taxName} > ${collection} > systemUpdate > oldData`,
        { oldData, diffObj }
      );
      return null;
    }

    if (!change.after.exists) {
      return null;
    }

    if (docId === COLLECTIONS.INDEX_ID) {
      return null;
    }

    let promises = [];

    let { owner, accountId } = { ...oldData, ...newData };

    if (owner && (!accountId || accountId === CONSTANTS.ORPHANS_ACCOUNT)) {
      try {
        const accountData = await getAccountByOwner(owner);
        accountId = (accountData || {}).ID || "";
        newData.accountId = accountId;
        updatedData.accountId = accountId;
      } catch (error) {
        console.error(error);
      }
    }

    try {
      // id
      if (newData.id !== docId) {
        updatedData.id = docId;
      }

      // ID
      if (newData.ID !== docId) {
        updatedData.ID = docId;
      }

      // date | createdAt
      if (!newData.date || !newData.createdAt) {
        updatedData.date = nowToISO();
        updatedData.modified = nowToISO();
        updatedData.createdAt = uniqueTimestamp(after.updateTime.toMillis());
        updatedData.updatedAt = uniqueTimestamp(after.updateTime.toMillis());
      }
      // updatedAt | modified
      else if (
        !newData.modified ||
        !newData.updatedAt ||
        oldData.updatedAt === newData.updatedAt
      ) {
        if (!newData.modified) {
          updatedData.modified = nowToISO();
        }
        updatedData.updatedAt = uniqueTimestamp(after.updateTime.toMillis());
      }

      // owner
      if (!newData.owner) {
        updatedData.owner = CONSTANTS.ORPHANS_OWNER;
      }

      // taxName
      if (!newData.taxName) {
        updatedData.taxName = taxName;
      }

      // collection
      if (!newData.collection) {
        updatedData.collection = collection;
      }

      // collection counter
      if (!newData[collection]) {
        updatedData[collection] = 0;
      }

      // keywords
      const keywordsSource = { ...newData, ...updatedData };
      const keywordFields = ["title"];
      const substrFields = ["title"];
      const oldKeywords = oldData.keywords || [];
      const hasNewkeywords =
        keywordFields.map((k) => keywordsSource[k] || "").join("") !==
        oldKeywords.join("");
      if (!newData.keywords || hasNewkeywords) {
        const keywordMap = {};
        keywordFields.forEach((field) => {
          keywordMap[field] = keywordsSource[field] || "";
        });
        updatedData.keywords = generateKeywords(
          keywordMap,
          2,
          100,
          substrFields
        );
      }

      switch (taxName) {
        case COLLECTIONS.TAGS_TAXONOMY_NAME:
          break;
        default:
          break;
      }

      // merge changes
      const mergedData = { ...newData, ...updatedData };

      // sanitize Field Types
      const sanitizedData = sanitizeFieldTypes(mergedData);

      let hasUpdates = false;
      const updateKeys = [];

      if (after.exists) {
        Object.keys(sanitizedData).forEach((k) => {
          const updatedValue = sanitizedData[k];
          const newValue = newData[k];
          if (helpers.isCyclic(newValue) || helpers.isCyclic(updatedValue))
            return;
          if (
            typeof newValue !== typeof updatedValue ||
            (k in updatedData &&
              JSON.stringify(newValue) !== JSON.stringify(updatedValue))
          ) {
            hasUpdates = true;
            updateKeys.push(k);
          }
        });
      }

      if (hasUpdates && change.after.exists) {
        let updateObj = { systemUpdate: true };
        updateKeys.forEach((k) => {
          updateObj[k] = sanitizedData[k];
        });
        promises.push(updateRef(docRef, updateObj));
        console.log("sanitizeTaxonomy > hasUpdates", { updateKeys, updateObj });
      }

      if (promises.length) {
        console.log("sanitizeTaxonomy > promises", promises.length);
        return Promise.all(promises)
          .then((results) => {
            return null;
          })
          .catch((err) => {
            console.error(err);
            return null;
          });
      }
    } catch (error) {
      const fnName = "sanitizeTaxonomy";
      const docData = {
        accountId: newData.accountId || oldData.accountId,
        collection,
        docId,
        data: {
          oldData,
          newData,
          updatedData,
        },
      };

      return functionsLog(fnName, error, docData);
    }
    // console.log('sanitizedData', sanitizedData);
    return null;
  });

exports.ticketsListener = functions.firestore
  .document(`${COLLECTIONS.TICKETS_COLLECTION_NAME}/{docId}`)
  .onWrite(ticketsListener);

exports.handleListeners = functions.firestore
  .document(`{collection}/{docId}`)
  .onWrite(handleListeners);

exports.logsListener = functions
  .runWith({ timeoutSeconds: 540, memory: "4GB" })
  .firestore.document(
    `{collection}/{docId}/${COLLECTIONS.LOGS_COLLECTION_NAME}/{logId}`
  )
  .onCreate(async (doc, context) => {
    funcCounter++;
    console.log(
      `LOGSLISTENER > ${context.params.collection} > FUNCCOUNTER:`,
      funcCounter
    );
    if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
      console.error(
        "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
          funcCounter
      );
      return null;
    }

    const { params } = context;

    const docCollection = params.collection;
    const docId = params.docId;
    const logId = params.logId;
    const newData = doc.data();
    const docRef = doc.ref;

    console.log(
      `LOGSLISTENER > lOG CRIADO EM ${docCollection}/${docId}/logs/${logId}`,
      newData
    );
    const {
      accountId,
      evaluated,
      contactId,
      operator_id,
      id,
      collection,
      trigger,
      date,
      data,
    } = newData;

    let logContext = newData.context || {};
    let listenerCollection = collection;
    let listenerId = id;

    let updates = {
      createdAt: uniqueTimestamp(doc.createTime.toMillis()),
      updatedAt: uniqueTimestamp(doc.createTime.toMillis()),
      fields: ["collection"],
      keywords: [collection],
      collections: [collection],
    };

    if (!newData[CONSTANTS.ACCOUNT_FIELD]) {
      try {
        let docQuery = await (
          id && collection
            ? FirestoreRef.collection(collection).doc(id)
            : FirestoreRef.collection(docCollection).doc(docId)
        ).get();
        let docData = docQuery.data();
        if (docData[CONSTANTS.ACCOUNT_FIELD]) {
          updateRef(docRef, {
            [CONSTANTS.ACCOUNT_FIELD]: docData[CONSTANTS.ACCOUNT_FIELD],
          });
        }
      } catch (error) {
        console.error(error);
      }
    }

    if (evaluated) {
      return updateRef(docRef, { logId });
    }

    if (
      collection !== COLLECTIONS.SESSIONS_COLLECTION_NAME &&
      data.session_id
    ) {
      try {
        await FirestoreRef.collection(COLLECTIONS.SESSIONS_COLLECTION_NAME)
          .doc(data.session_id)
          .collection(COLLECTIONS.LOGS_COLLECTION_NAME)
          .add({
            ...newData,
            evaluated: true,
            originId: logId,
            createdAt: uniqueTimestamp(doc.createTime.toMillis()),
            updatedAt: uniqueTimestamp(doc.createTime.toMillis()),
          });
      } catch (error) {
        console.log("error", error);
      }
    }

    let pushKeywords = (field, val, col, isRelated) => {
      let fieldVal = `[${field}]:[${val}]`;
      let colVal = `[collection]:[${col}]`;

      updates.keywords.push(val);
      updates.keywords.push(`${fieldVal}`);
      updates.keywords.push(`${colVal};${fieldVal}`);

      updates.fields = [...new Set([...updates.fields, field])];
      updates.collections = [...new Set([...updates.collections, col])];

      if (!isRelated) {
        CONSTANTS.RELATIONAL_FIELDS.filter(
          (r) => newData[r] || data[r] || logContext[r]
        ).forEach((related) => {
          let relCol = CONSTANTS.RELATIONAL_COLLECTIONS[related];
          let relVal = newData[related] || data[related] || logContext[related];
          if (relVal) {
            let rVal = `[${related}]:[${relVal}]`;
            let rColVal = `[relatedCol]:[${relCol}]`;

            updates.keywords.push(`${rVal};${fieldVal}`);
            updates.keywords.push(`${rVal};${colVal}`);
            updates.keywords.push(`${rVal};${colVal};${fieldVal}`);
            updates.keywords.push(`${rColVal};${rVal};${colVal};${fieldVal}`);
          }
        });
      }

      updates.keywords = [...new Set(updates.keywords)];
    };

    let keywordFields = ["trigger", "id", "contactId", "date"];

    keywordFields
      .filter((field) => newData[field])
      .forEach((field) => {
        let val = newData[field];
        let col = collection;
        pushKeywords(field, val, col);
      });

    let dateFormats = [CONSTANTS.MOMENT_ISO_DAY];

    dateFormats.forEach((format) => {
      let val = moment(updates.createdAt).format(format);
      let col = collection;
      pushKeywords(format, val, col);
    });

    CONSTANTS.RELATIONAL_FIELDS.filter(
      (field) => newData[field] || data[field] || logContext[field]
    ).forEach((field) => {
      let col = CONSTANTS.RELATIONAL_COLLECTIONS[field];
      let val = newData[field] || data[field] || logContext[field];
      if (val) {
        pushKeywords(field, val, col, true);
      }
    });

    if (logContext["taxName"] && logContext["taxCollection"]) {
      let taxonomiesFields = ["taxId", "taxName", "taxCollection"];
      let { taxId, taxName, taxCollection } = logContext;
      let col = taxCollection;

      listenerCollection = taxName;
      listenerId = taxId;

      pushKeywords("taxName-taxId", `${taxName}-${taxId}`, col);
      pushKeywords(
        "taxCollection-taxName-taxId",
        `${taxCollection}-${taxName}-${taxId}`,
        col
      );

      taxonomiesFields.forEach((field) => {
        let val = logContext[field];
        if (val) {
          pushKeywords(field, val, col);
        }
      });
    }

    let logUpdated = {};
    return updateRef(docRef, {
      ...updates,
      data: { ...logContext, ...data },
      logId,
      evaluated: false,
    })
      .then((docUpdated) => {
        console.log(
          ` LOGSLISTENER > lOG ATUALIZADO ${docCollection}/${docId}/logs/${logId}`,
          { logUpdated, logContext }
        );

        const origin =
          logContext && logContext.origin ? logContext.origin : null; // Flag para identificar de onde veio

        logUpdated = {
          ...docUpdated,
          origin,
        };
        //TODO get listeners update
        return getListeners(
          {
            collection: listenerCollection,
            id: listenerId,
            trigger,
            origin,
            logContext,
          },
          accountId
        );
      })
      .then((listeners) => {
        console.log(`LOGSLISTENER > GETLISTENERS > RESULT: `, {
          params: context.params,
          listeners,
          trigger,
        });
        if (listeners && listeners.length) {
          console.log(`LOGSLISTENER > GETLISTENERS > RESULT > IF: `, {
            params: context.params,
            listeners,
            trigger,
          });
          return evaluateListeners(
            accountId,
            listeners,
            docId,
            docCollection,
            contactId,
            listenerId,
            listenerCollection,
            trigger,
            logUpdated
          );
        }
        return null;
      })
      .then((listenersResults) => {
        console.log(
          `LOGSLISTENER > ${context.params.collection} > LISTENERS RESULT: `,
          { listenersResults }
        );
        return updateRef(docRef, { evaluated: true });
      })
      .then(() => {
        return null;
      })
      .catch((error) => console.error(error));
  });

exports.logsMigration = functions.firestore
  .document(`${COLLECTIONS.LOGS_COLLECTION_NAME}/{collection}/{docId}/{logId}`)
  .onWrite(async (change, context) => {
    funcCounter++;
    console.log(
      `logsMigration > ${context.params.collection} > funcCounter:`,
      funcCounter
    );
    if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
      console.error(
        "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
          funcCounter
      );
      return null;
    }
    const { params } = context;
    const { before, after } = change;

    if (!after.exists) {
      return null;
    }

    const docCollection = params.collection;
    const docId = params.docId;
    const logId = params.logId;

    const newData = (after.exists && after.data()) || {};
    const docRef = after.exists && after.ref;

    const { id, collection } = newData;

    try {
      let newRef, tgtId, tgtCollection;
      if (id && collection) {
        tgtId = id;
        tgtCollection = collection;
      } else {
        tgtId = docId;
        tgtCollection = docCollection;
      }
      if (COLLECTIONS.coreCollections.includes(tgtCollection)) {
        await FirestoreRef.collection(docCollection)
          .doc(docId)
          .collection(COLLECTIONS.LOGS_COLLECTION_NAME)
          .doc(logId)
          .set(newData);
      }
    } catch (error) {
      console.error(error);
    }

    return null;
  });

exports.onReceveMessagesShotx = functions
  .runWith({ timeoutSeconds: 540, memory: "4GB" })
  .firestore.document(
    `${COLLECTIONS.SHOTX_COLLECTION_NAME}/{accountId}/${COLLECTIONS.SHOTX_INSTANCES_COLLECTION_NAME}/{instanceId}/${COLLECTIONS.CONTACTS_SUBCOLLECTION_NAME}/{contactId}/${COLLECTIONS.MESSAGES_SUBCOLLECTION_NAME}/{messageId}`
  )
  .onCreate(async (doc, context) => {
    setTimeout(async () => {
      console.log("ONRECEVEMESSAGESHOTX > START ", { doc, context });

      const messageInfo = doc.data();

      // Verifica se uma mensagem é valida e se existe conteudo
      if (!messageInfo) return null;
      if (messageInfo.receiver === false) return null;
      if (messageInfo.deleted === true) return null;

      const { accountId, instanceId } = context.params;
      let { contactId } = context.params;
      const contactRefNumber = context.params.contactId;
      const messageContext = context;
      messageContext.instanceId = instanceId;

      console.log("ONRECEVEMESSAGESHOTX > INFO", {
        accountId,
        instanceId,
        contactId,
        doc,
        context,
        messageInfo,
      });

      const listenerCollection = COLLECTIONS.SHOTX_COLLECTION_NAME;
      const listenerId = messageInfo.id;
      const trigger = APP_TRIGGERS.APP_TRIGGER_MESSAGE_RECEIVED;
      const name = trigger;
      const origin = DATA_ORIGINS.SHOTX.WHATSAPP;
      const docCollection = COLLECTIONS.SHOTX_COLLECTION_NAME;
      const docId = instanceId;
      const logUpdated = null;

      let contact = [];

      let contactNumber = removeBrazilNineDigit(contactRefNumber);

      let where = [];
      where.push(["accountId", "==", accountId]);
      where.push(["mobile", "==", contactNumber]);

      contact = await fetchCollection(COLLECTIONS.LEADS_COLLECTION_NAME, where);

      console.log(`ONREVEMESSAGESSHOTX > CONTACT > RESULT contact: `, {
        contact,
      });

      if (!contact || !contact.length) {
        contactNumber = insertBrazilNineDigit(contactRefNumber);

        where = [];
        where.push(["accountId", "==", accountId]);
        where.push(["mobile", "==", contactNumber]);

        contact = await fetchCollection(
          COLLECTIONS.LEADS_COLLECTION_NAME,
          where
        );
      }

      contactId = await contact[0].id;

      console.log(`ONREVEMESSAGESSHOTX > CONTACT > RESULT: `, {
        contact,
        contactId,
      });

      return getListeners(
        {
          collection: listenerCollection,
          id: listenerId,
          trigger,
          name,
          origin,
          logContext: messageContext,
        },
        accountId
      )
        .then((listeners) => {
          if (listeners && listeners.length) {
            console.log(`ONREVEMESSAGESSHOTX > GETLISTENERS > RESULT > IF: `, {
              params: context.params,
              listeners,
              trigger,
            });
            return evaluateListeners(
              accountId,
              listeners,
              docId,
              docCollection,
              contactId,
              listenerId,
              listenerCollection,
              trigger,
              logUpdated
            );
          }
          return null;
        })
        .catch((error) => console.error(error));
    }, 30000);
  });

exports.segmentationsLogs = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .firestore.document(`${COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME}/{docId}`)
  .onWrite((change, context) => {
    funcCounter++;
    console.log(`segmentationsLogs funcCounter:`, funcCounter);
    if (funcCounter > CONSTANTS.FUNCTIONS_EXECUTION_LIMIT) {
      console.error(
        "QIPLUS WEBMASTER LIMIT MANAGER > FUNCTIONS_EXECUTION_LIMIT: " +
          funcCounter
      );
      return null;
    }

    const { before, after } = change;
    const { params } = context;

    const collection = COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME;
    const docId = params.docId;
    const docRef = after.exists && after.ref;

    if (!after.exists) {
      return null;
    }

    const oldData = (before.exists && before.data()) || {};
    const newData = (after.exists && after.data()) || {};
    const updatedData = {};

    if (oldData.systemUpdate || newData.systemUpdate) {
      console.log(
        `systemUpdate on > ${
          oldData.systemUpdate ? "oldData" : "newData"
        } > funcCounter` + funcCounter
      );
      return null;
    }

    const config = newData.config || {};

    const promises = [];

    // LOGS
    const logKeys = [
      CONSTANTS.DATE_FIELD,
      CONSTANTS.MODIFIED_FIELD,
      ...CONSTANTS.RELATIONAL_FIELDS,
    ];
    const logData = {};

    logKeys.forEach((key) => {
      if (change.after.exists && key in newData) {
        logData[key] = newData[key];
      } else if (!change.after.exists && key in oldData) {
        logData[key] = oldData[key];
      }
    });

    let oldLeads = Array.isArray(oldData.leads)
      ? [...new Set(oldData.leads)]
      : [];
    let newLeads = Array.isArray(newData.leads)
      ? [...new Set(newData.leads)]
      : [];
    let addedLeads = newLeads.filter((l) => !oldLeads.includes(l));
    let removedLeads = oldLeads.filter((l) => !newLeads.includes(l));

    let triggerName = "";

    if (addedLeads.length) {
      triggerName = APP_TRIGGERS.APP_TRIGGER_ADDED_TO_SEGMENTATION;
      addedLeads.forEach((contactId) => {
        promises.push(
          addNewLog({
            contactId,
            user_id: contactId,
            operator_id: 0,
            id: docId,
            collection: COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME,
            trigger: triggerName,
            date: nowToISO(),
            owner: logData.owner || "",
            accountId: logData.accountId || "",
            data: logData,
          })
        );
      });
    }

    if (removedLeads.length) {
      triggerName = APP_TRIGGERS.APP_TRIGGER_REMOVED_FROM_SEGMENTATION;
      removedLeads.forEach((contactId) => {
        promises.push(
          addNewLog({
            contactId,
            user_id: contactId,
            operator_id: 0,
            id: docId,
            collection: COLLECTIONS.SEGMENTATIONS_COLLECTION_NAME,
            trigger: triggerName,
            date: nowToISO(),
            owner: logData.owner || "",
            accountId: logData.accountId || "",
            data: logData,
          })
        );
      });
    }

    if (
      config.dinamic &&
      !(newData.actions || []).find(
        (a) => a.name === CONSTANTS.APP_ACTION_LOG && a.id === docId
      )
    ) {
      let action = {
        id: docId,
        name: CONSTANTS.APP_ACTION_LOG,
        order: 1,
        stage: CONSTANTS.FIRST_STAGE,
        data: {
          trigger: APP_TRIGGERS.APP_TRIGGER_ADDED_TO_SEGMENTATION,
        },
      };

      promises.push(updateRef(docRef, { actions: [action] }));
    } else if (!config.dinamic && (newData.actions || []).length) {
      promises.push(updateRef(docRef, { actions: [] }));
    }

    if (config.dinamic && (newData.leads || []).length) {
      promises.push(updateRef(docRef, { leads: [] }));
    } else if (
      !config.dinamic &&
      (!Array.isArray(newData.leads) ||
        newData.leads.length !== newLeads.length)
    ) {
      promises.push(updateRef(docRef, { leads: newLeads }));
    }

    if (promises.length) {
      return Promise.all(promises).catch((err) => {
        console.error(err);
        return null;
      });
    }

    return null;
  });

exports.handleChatMessages = functions.firestore
  .document(
    `${COLLECTIONS.CHATS_COLLECTION_NAME}/{chatId}/${COLLECTIONS.MESSAGES_SUBCOLLECTION_NAME}/{messageId}`
  )
  .onCreate((doc, context) => {
    const { params } = context;

    const chatId = params.chatId;
    const messageId = params.messageId;
    const data = doc.data();

    const { scheduled_date } = data;

    let momentNowInstance = momentNow();
    let scheduledMoment =
      scheduled_date && moment(scheduled_date).isValid()
        ? moment(scheduled_date)
        : momentNowInstance;
    let scheduledISO = scheduledMoment.format(CONSTANTS.MOMENT_ISO);
    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);

    return updateRef(doc.ref, {
      createdAt: uniqueTimestamp(doc.createTime.toMillis()),
      chatId,
      messageId,
      id: messageId,
      scheduled_date: scheduledISO,
      sent: true,
      sending: false,
      error: false,
    });
  });

exports.handleEmails = functions.firestore
  .document(`${COLLECTIONS.MAIL_COLLECTION_NAME}/{mailId}`)
  .onCreate((doc, context) => {
    const { params } = context;

    const mailId = params.mailId;
    const data = doc.data();

    let { to, scheduled_date, html, sent, sending, error } = data;

    if (!error && !sending && !sent && html) {
      let momentNowInstance = momentNow();
      let scheduledMoment =
        scheduled_date && moment(scheduled_date).isValid()
          ? moment(scheduled_date)
          : momentNowInstance;
      let scheduledISO = scheduledMoment.format(CONSTANTS.MOMENT_ISO);
      let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);

      return updateRef(doc.ref, {
        createdAt: uniqueTimestamp(doc.createTime.toMillis()),
        to: to || "",
        mailId,
        date: nowToISO(),
        scheduled_date: scheduledISO,
        sent: false,
        sending: false,
        error: false,
      });
    }

    return null;
  });

exports.handleMailboxesUpdate = functions.firestore
  .document(`${COLLECTIONS.MAILBOXES_COLLECTION_NAME}/{mailboxId}`)
  .onUpdate(mailboxes.handleMailboxesUpdate);

exports.handleMailboxesCreate = functions.firestore
  .document(`${COLLECTIONS.MAILBOXES_COLLECTION_NAME}/{mailboxId}`)
  .onCreate(mailboxes.handleMailboxesCreate);

exports.handleMailboxesEmails = functions.firestore
  .document(
    `${COLLECTIONS.MAILBOXES_COLLECTION_NAME}/{mailboxId}/{folder}/{mailId}`
  )
  .onCreate(mailboxes.handleMailboxesEmails);

exports.checkMailboxAvailability = functions.https.onRequest(
  mailboxes.checkMailboxAvailability
);

exports.getCloudMailboxes = functions.https.onRequest(
  mailboxes.getCloudMailboxes
);

exports.getIMAPFolders = functions
  .runWith({ timeoutSeconds: 540, memory: "1GB" })
  .https.onRequest(mailboxes.getIMAPFolders);

exports.moveIMAPMail = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .https.onRequest(mailboxes.moveIMAPMail);

exports.saveIMAPMail = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .https.onRequest(mailboxes.saveIMAPMail);

exports.getIMAPMail = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .https.onRequest(mailboxes.getIMAPMail);

exports.sendSMTPMail = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .https.onRequest(mailboxes.sendSMTPMail);

exports.httpMailStats = functions
  .runWith({ timeoutSeconds: 300, memory: "1GB" })
  .https.onRequest(async (request, response) => {
    cors(request, response, async () => {
      const query = request.body.data;
      console.log(
        "httpMailStats > query ----------------------------------------",
        { query }
      );

      if (!query) {
        // throw new functions.https.HttpsError('failed-precondition', 'No query sent' + JSON.stringify(request.body));
        console.log(
          "failed-precondition No query sent",
          JSON.stringify(request.body)
        );
        return null;
      }

      let {
        id,
        collection,
        queries,
        limit,
        lastPos,
        orderBy,
        order,
        filters,
        dateFormat,
      } = query;

      if (!id || !collection) {
        // throw new functions.https.HttpsError('failed-precondition', 'Missing arguments.' + JSON.stringify(query));
        console.log(
          "failed-precondition missing id OR collection",
          JSON.stringify(query)
        );
        return null;
      }

      filters = filters || {};
      queries = (Array.isArray(queries) && queries) || [];
      let dateFormats = ["YYYY-MM-DD HH:mm", "YYYY-MM-DD HH", "YYYY-MM-DD"];

      /*
       * Begin Query
       */
      let QueryRef = FirestoreRef.collection(collection)
        .doc(id)
        .collection(COLLECTIONS.LOGS_COLLECTION_NAME);

      queries.forEach(
        (queryArgs) =>
          (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2]))
      );

      if (orderBy) {
        QueryRef = QueryRef.orderBy(orderBy, order || "asc");
        Boolean(lastPos) && (QueryRef = QueryRef.startAfter(lastPos));
      }

      Boolean(limit) &&
        !isNaN(limit) &&
        limit &&
        (QueryRef = QueryRef.limit(limit));

      return QueryRef.get()
        .then((snapshot) => {
          // let { size } = snapshot;

          let emailIds = [];
          let uniqueRecipients = [];
          let logsByEvent = {};
          let bounces = { soft: 0, hard: 0 };
          let axis = {};
          let aData = {};

          let logs = snapshot.docs.map((doc) => doc.data());

          let filteredLogs = logs
            .sort((a, b) => sortByUpdateDate(a, b, "asc", "createdAt"))
            .filter((log, i) => {
              let { trigger, contactId, date, data } = log;
              let eventData = data["event-data"] || {};

              if (
                (filters.start &&
                  moment(date).valueOf() < moment(filters.start).valueOf()) ||
                (filters.end &&
                  moment(date).valueOf() > moment(filters.end).valueOf()) ||
                (filters.event && trigger !== filters.event)
              ) {
                return false;
              }

              if (trigger === "failed") {
                if (
                  logs.find(
                    (l) =>
                      l.trigger === "delivered" &&
                      l.contactId === contactId &&
                      (l.data || {}).messageId === data.messageId
                  )
                ) {
                  return false;
                }
              } else if (trigger === APP_TRIGGERS.APP_TRIGGER_VIEWED) {
                if (
                  logs.find(
                    (l, k) =>
                      k > i &&
                      l.trigger === APP_TRIGGERS.APP_TRIGGER_OPENED &&
                      l.contactId === contactId &&
                      (data.triggerId || data.mailId) &&
                      ((l.data || {}).triggerId === data.triggerId ||
                        (l.data || {}).mailId === data.mailId)
                  )
                ) {
                  return false;
                }
              } else if (
                logs.find(
                  (l, k) =>
                    k > i &&
                    l.trigger === trigger &&
                    l.contactId === contactId &&
                    data.messageId &&
                    (l.data || {}).messageId === data.messageId
                )
              ) {
                return false;
              }

              return true;
            });

          filteredLogs.forEach((log, i) => {
            let { trigger, contactId, updatedAt, date, data } = log;
            let eventData = data["event-data"] || {};

            contactId &&
              (uniqueRecipients = [
                ...new Set([...uniqueRecipients, contactId]),
              ]);

            if (
              trigger === APP_TRIGGERS.APP_TRIGGER_SENT &&
              (data.triggerId || data.mailId)
            ) {
              emailIds = [
                ...new Set([...emailIds, `${data.triggerId || data.mailId}`]),
              ];
            }

            if (trigger === "failed" && eventData.severity) {
              bounces.soft += eventData.severity === "temporary" ? 1 : 0;
              bounces.hard += eventData.severity !== "temporary" ? 1 : 0;
            }

            // axisData
            dateFormats.forEach((f) => {
              let ddd = moment(date).format(f);
              !aData[trigger] && (aData[trigger] = {});
              !aData[trigger][f] && (aData[trigger][f] = {});
              !aData[trigger][f][ddd] && (aData[trigger][f][ddd] = 0);
              aData[trigger][f][ddd]++;

              !axis[f] && (axis[f] = []);
              axis[f] = [...new Set([...axis[f], ddd])];
            });

            // logsByEvent
            !logsByEvent[trigger] && (logsByEvent[trigger] = []);
            logsByEvent[trigger].push(log);
          });

          let size = filteredLogs.length;
          let axisData = { axis, aData, dateFormat };
          let data = {
            size,
            axisData,
            emailIds,
            uniqueRecipients,
            bounces /* logsByEvent */,
          };

          return response.send({ data });
        })
        .catch((error) => {
          console.log("httpMailStats > query", { error });
          const errorType = ERROR_TYPES.EMAIL_ANALYTICS_GENERIC_ERROR;
          const errorMessage =
            typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "";
          return response.send({
            data: { error: errorType, errorMessage, errorType, debug: error },
          });
        });
    });
    return null;
  });

exports.postsStats = functions
  .runWith({ timeoutSeconds: 540, memory: "2GB" })
  .https.onRequest(async (request, response) => {
    cors(request, response, async () => {
      const query = request.body.data;
      console.log(
        "postsStats > query ----------------------------------------",
        { query }
      );

      if (!query) {
        // throw new functions.https.HttpsError('failed-precondition', 'No query sent' + JSON.stringify(request.body));
        console.log(
          "failed-precondition No query sent",
          JSON.stringify(request.body)
        );
        return null;
      }

      let arrayKeys = [
        "queries",
        "keywords",
        "groupKeywords",
        "counters",
        "accumulators",
        "postsIds",
        "groupQueries",
        "postsQueries",
      ];

      arrayKeys.forEach((k) => !Array.isArray(query[k]) && (query[k] = []));

      let {
        accountId,
        collection,
        id,
        postsIds,
        queries,
        keywords,
        groupKeywords,
        groupQueries,
        postsQueries,
        limit,
        lastPos,
        orderBy,
        order,
        filters,
        counters,
        accumulators,
        dateFormats,
      } = query;

      if (!collection || (!id && !accountId)) {
        console.log(
          "failed-precondition missing collection OR (id AND accountId)",
          JSON.stringify(query)
        );
        return null;
      }

      dateFormats = dateFormats || [
        CONSTANTS.MOMENT_ISO_DAY,
        CONSTANTS.MOMENT_ISO_MONTH,
      ];
      filters = filters || {};

      let promises = [];
      let posts = [];
      let stats = {};

      if (groupKeywords.length || groupQueries.length) {
        let logs = [];
        let pages = paginateArray(groupKeywords);

        console.log("prepareRequests > pages", { pages, groupKeywords });

        if (pages.length) {
          let keywordsPromises = pages.map((keywordsPage) => {
            let pageRef = FirestoreRef.collectionGroup(
              COLLECTIONS.LOGS_COLLECTION_NAME
            );

            groupQueries.forEach(
              (queryArgs) =>
                (pageRef = pageRef.where(
                  queryArgs[0],
                  queryArgs[1],
                  queryArgs[2]
                ))
            );

            if (filters.start)
              pageRef = pageRef.where(
                CONSTANTS.CREATED_FIELD,
                ">=",
                moment(filters.start).valueOf()
              );
            if (filters.end)
              pageRef = pageRef.where(
                CONSTANTS.CREATED_FIELD,
                "<=",
                moment(filters.end).valueOf()
              );

            if (accountId !== 1)
              pageRef = pageRef.where(CONSTANTS.ACCOUNT_FIELD, "==", accountId);

            pageRef = pageRef.where(
              CONSTANTS.KEYWORDS_FIELD,
              "array-contains-any",
              keywordsPage
            );
            pageRef = pageRef.orderBy(CONSTANTS.CREATED_FIELD, "desc");

            return pageRef.get();
          });

          try {
            let results = await Promise.all(keywordsPromises);
            logs = results.reduce(
              (all, snapshot) => [
                ...all,
                ...snapshot.docs.map((doc) => doc.data()),
              ],
              []
            );
          } catch (error) {
            console.error({ fn: "postsStats", error });
          }
        } else {
          let QueryRef = FirestoreRef.collectionGroup(
            COLLECTIONS.LOGS_COLLECTION_NAME
          );

          groupQueries.forEach(
            (queryArgs) =>
              (QueryRef = QueryRef.where(
                queryArgs[0],
                queryArgs[1],
                queryArgs[2]
              ))
          );

          if (filters.start)
            QueryRef = QueryRef.where(
              CONSTANTS.CREATED_FIELD,
              ">=",
              moment(filters.start).valueOf()
            );
          if (filters.end)
            QueryRef = QueryRef.where(
              CONSTANTS.CREATED_FIELD,
              "<=",
              moment(filters.end).valueOf()
            );

          if (accountId !== 1)
            QueryRef = QueryRef.where(CONSTANTS.ACCOUNT_FIELD, "==", accountId);

          QueryRef = QueryRef.orderBy(CONSTANTS.CREATED_FIELD, "desc");

          Boolean(lastPos) && (QueryRef = QueryRef.startAfter(lastPos));
          Boolean(limit) &&
            !isNaN(limit) &&
            limit &&
            (QueryRef = QueryRef.limit(limit));

          let snapshot = await QueryRef.get();
          logs = snapshot.docs.map((doc) => doc.data());
        }

        logs.forEach((log) => {
          let postId = log[CONSTANTS.LOG_RELATED_ID_FIELD];
          postsIds = [...new Set([...postsIds, postId])];
        });

        promises = postsIds.map((postId) => ({
          postId,
          logs: logs.filter(
            (l) => l[CONSTANTS.LOG_RELATED_ID_FIELD] === postId
          ),
        }));
      } else {
        if (id) {
          let postRef = await FirestoreRef.collection(collection).doc(id).get();
          posts = postRef.exists ? [postRef.data()] : {};
          postsIds = [id];
        } else if (!postsIds.length) {
          if (
            postsQueries.length ||
            !COLLECTIONS.coreCollections.includes(collection)
          ) {
            let postsQuery = FirestoreRef.collection(collection).where(
              CONSTANTS.ACCOUNT_FIELD,
              "==",
              accountId
            );
            postsQueries.forEach(
              (queryArgs) =>
                (postsQuery = postsQuery.where(
                  queryArgs[0],
                  queryArgs[1],
                  queryArgs[2]
                ))
            );

            let postsRef = await postsQuery.get();
            posts = postsRef.docs.map((doc) => doc.data());
            postsIds = postsRef.docs.map((doc) => doc.id);
          } else {
            const isCustomKeywordsCollection =
              keywordsSubcollections.includes(collection);
            const keywordsCollection =
              KEYWORDS_SUBCOLLECTIONS_MAP[collection] ||
              KEYWORDS_SUBCOLLECTION_NAME;
            const keywordsOrderBy = CONSTANTS.CREATED_FIELD;
            const keywordsOrder = "desc";
            const where = [];

            where.push([
              CONSTANTS.KEYWORDS_FIELDKEY_FIELD,
              "==",
              CONSTANTS.ACCOUNT_FIELD,
            ]);
            where.push([CONSTANTS.KEYWORDS_FIELD, "array-contains", accountId]);
            where.push([CONSTANTS.ACCOUNT_FIELD, "==", accountId]);
            if (!isCustomKeywordsCollection)
              where.push([CONSTANTS.COLLECTION_FIELD, "==", collection]);

            console.log(`postsStats > ${collection} > keywordsQuery > where`, {
              where,
            });

            let keywordsQuery =
              FirestoreRef.collectionGroup(keywordsCollection);

            where.forEach(
              (queryArgs) =>
                (keywordsQuery = keywordsQuery.where(
                  queryArgs[0],
                  queryArgs[1],
                  queryArgs[2]
                ))
            );

            // if (filters.start) keywordsQuery=keywordsQuery.startAt(moment(filters.start).valueOf())
            // if (filters.start) keywordsQuery=keywordsQuery.where(CONSTANTS.CREATED_FIELD, ">=", moment(filters.start).valueOf())
            if (filters.end)
              keywordsQuery = keywordsQuery.where(
                CONSTANTS.CREATED_FIELD,
                "<=",
                moment(filters.end).valueOf()
              );

            keywordsQuery = keywordsQuery.orderBy(
              keywordsOrderBy,
              keywordsOrder
            );

            let keywordsRef = await keywordsQuery.get();
            let keywordsDocs = keywordsRef.docs.map((doc) => doc.data());
            postsIds = [...new Set(keywordsDocs.map((data) => data.docId))];

            console.log(
              `postsStats > ${collection} > keywordsQuery > postsIds`,
              { postsIds }
            );
            // console.log(`postsStats > ${collection} > keywordsQuery > keywordsDocs`, { keywordsDocs })
          }
        }

        /*
         * Begin Query
         */

        promises = postsIds.map((postId) => {
          let QueryRef = FirestoreRef.collection(collection)
            .doc(postId)
            .collection(COLLECTIONS.LOGS_COLLECTION_NAME);

          queries.forEach(
            (queryArgs) =>
              (QueryRef = QueryRef.where(
                queryArgs[0],
                queryArgs[1],
                queryArgs[2]
              ))
          );

          if (!queries.length && filters.start)
            QueryRef = QueryRef.where(
              CONSTANTS.CREATED_FIELD,
              ">=",
              moment(filters.start).valueOf()
            );
          if (!queries.length && filters.end)
            QueryRef = QueryRef.where(
              CONSTANTS.CREATED_FIELD,
              "<=",
              moment(filters.end).valueOf()
            );

          if (!queries.length && (orderBy || lastPos)) {
            orderBy = orderBy || CONSTANTS.CREATED_FIELD;
            QueryRef = QueryRef.orderBy(orderBy, order || "desc");
            Boolean(lastPos) && (QueryRef = QueryRef.startAfter(lastPos));
          }

          Boolean(limit) &&
            !isNaN(limit) &&
            limit &&
            (QueryRef = QueryRef.limit(limit));

          return QueryRef.get().then((snapshot) => ({
            postId,
            logs: snapshot.docs.map((doc) => doc.data()),
          }));
        });
      }

      return Promise.all(promises)
        .then((results) => {
          results.forEach(({ postId, logs }) => {
            if (!logs.length) return;

            let filteredLogs = logs
              .sort((a, b) => sortByUpdateDate(a, b, "asc", "createdAt"))
              .filter((log, i) => {
                let { trigger, contactId, date, data } = log;
                if (
                  (filters.start &&
                    moment(date).valueOf() < moment(filters.start).valueOf()) ||
                  (filters.end &&
                    moment(date).valueOf() > moment(filters.end).valueOf()) ||
                  (filters.event && trigger !== filters.event) ||
                  (filters.collection &&
                    log.collection !== filters.collection) ||
                  (filters.collections &&
                    !filters.collections.includes(log.collection))
                ) {
                  return false;
                }

                return true;
              });

            stats[postId] = compileLogsStats(
              filteredLogs,
              collection,
              dateFormats,
              counters,
              accumulators
            );
          });

          return response.send({ data: stats });
        })
        .catch((error) => {
          console.log("postsStats > query", { error });
          const errorType = ERROR_TYPES.POST_STATS_GENERIC_ERROR;
          const errorMessage =
            typeof error === "string"
              ? error
              : error.message
                ? error.message
                : "";
          return response.send({
            data: {
              error: errorType,
              errorMessage,
              errorType,
              debug: { error, query },
            },
          });
        });
    });
    return null;
  });

exports.getGeoipData = functions.https.onRequest(async (request, response) => {
  cors(request, response, async () => {
    const query = request.body.data;
    if (!query || !Array.isArray(query.data)) {
      console.log("getGeoipData > request.body", request.body);
      return null;
    }

    const geoip = require("fast-geoip");
    const uaParser = require("ua-parser-js");

    const promises = query.data.map(
      ({ ip, ua }) =>
        new Promise(async (res) => {
          let data = {
            ip,
            ua,
            geo: null,
            agent: null,
            error: undefined,
            geoError: undefined,
            agentError: undefined,
          };
          if (ua) {
            try {
              data.agent = uaParser(ua);
            } catch (error) {
              console.log("uaParser > error", error);
              data.error = error;
              data.agentError = error;
            }
          }
          if (ip) {
            try {
              data.geo = await geoip.lookup(ip);
            } catch (error) {
              console.log("getGeoipData > error", error);
              data.error = error;
              data.geoError = error;
            }
          }
          return res(data);
        })
    );

    return Promise.all(promises)
      .then((responses) => {
        return response.send({ data: responses });
      })
      .catch((error) => {
        console.log("httpMailStats > query", { error });
        const errorType = ERROR_TYPES.ERROR_GETTING_GEOIP_DATA;
        const errorMessage =
          typeof error === "string"
            ? error
            : error.message
              ? error.message
              : "";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: error },
        });
      });
  });
  return null;
});

exports.getPages = functions.https.onRequest(async (request, response) => {
  cors(request, response, async () => {
    const query = request.body.data;
    if (!query || !query.db) {
      // console.log('onRequest > request.body', request.body);
      throw new functions.https.HttpsError(
        "failed-precondition",
        "The function needs arguments." + JSON.stringify(request.body)
      );
    }
    return buildPages(query, (results) => {
      response.send({ data: results });
    });
  });
  return null;
});

exports.facebookApiHandler = functions.https.onRequest(
  async (request, response) => {
    cors(request, response, async () => {
      const eventData = request.body.data;
      console.log("facebookApiHandler > eventData", eventData);

      try {
        if ((request.headers || {})["user-agent"]) {
          request.headers["user-agent"];
        }

        if ((request.socket || {}).remoteAddress) {
          request.socket.remoteAddress;
        } else if ((request.connection || {}).remoteAddress) {
          request.connection.remoteAddress;
        }
      } catch (error) {
        const errorType = ERROR_TYPES.GENERIC_ERROR;
        const errorMessage =
          typeof error === "string"
            ? error
            : error.message
              ? error.message
              : "";
        return response.send({
          data: { error: errorType, errorMessage, errorType, debug: error },
        });
      }

      return null;
    });
    return null;
  }
);

exports.doNotificationCron = functions.https.onRequest(
  async (request, response) => {
    cors(request, response, async () => {
      response.send({ data: { ok: true } });
      return notificationCron();
    });
    return null;
  }
);

exports.doAutomationCron = functions.https.onRequest(
  async (request, response) => {
    cors(request, response, async () => {
      try {
        let results = await automationCron();
        return response.send({ data: { ok: true, results } });
      } catch (error) {
        console.error("doAutomationCron > error", { error });
        return response.send({ data: { status: "error", ok: false, error } });
      }
    });
    return null;
  }
);

exports.doBillingCron = functions.https.onRequest(async (request, response) => {
  cors(request, response, async () => {
    try {
      let results = await billingCron();
      return response.send({ data: { status: "ok", ok: true, results } });
    } catch (error) {
      console.error("doBillingCron > error", { error });
      return response.send({ data: { status: "error", ok: false, error } });
    }
  });
  return null;
});

exports.cron1Minutes = functions
  .runWith({ memory: "4GB", timeoutSeconds: 540 })
  .pubsub.schedule("every 1 minutes")
  .onRun((context) => {
    try {
      let promises = [];
      promises.push(emailCron());
      promises.push(notificationCron());
      promises.push(shotxCron());
      promises.push(shotxSendMessages());
      return Promise.all(promises);
    } catch (error) {
      const fnName = "cron1Minutes";
      const docData = {
        errorMsg: "QIPlus | Webmaster || Error while executing cron1Minutes",
      };
      return functionsLog(fnName, error, docData);
    }
  });

exports.cron5Minutes = functions
  .runWith({ memory: "4GB", timeoutSeconds: 540 })
  .pubsub.schedule("every 5 minutes")
  .onRun((context) => {
    try {
      let promises = [];
      promises.push(automationCron());
      return Promise.all(promises);
    } catch (error) {
      const fnName = "cron5Minutes";
      const docData = {
        errorMsg: "QIPlus | Webmaster || Error while executing cron5Minutes",
      };
      return functionsLog(fnName, error, docData);
    }
  });

exports.cron24Hours = functions
  .runWith({ memory: "2GB", timeoutSeconds: 540 })
  .pubsub.schedule("every 24 hours")
  .onRun((context) => {
    try {
      let promises = [];
      promises.push(billingCron());
      promises.push(updateFunnelStats());
      return Promise.all(promises);
    } catch (error) {
      const fnName = "cron24Hours";
      const docData = {
        errorMsg: "QIPlus | Webmaster || Error while executing cron24Hours",
      };
      return functionsLog(fnName, error, docData);
    }
  });

// Schedule the automated backup
exports.backup24Hours = functions
  .runWith({ memory: "2GB", timeoutSeconds: 540 })
  .pubsub.schedule("every 24 hours")
  .onRun((context) => {
    const collectionIds = [
      COLLECTIONS.ACCOUNTS_COLLECTION_NAME,
      COLLECTIONS.QIUSERS_COLLECTION_NAME,
      COLLECTIONS.AFFILIATES_COLLECTION_NAME,
      COLLECTIONS.QIPLUS_PLANS_COLLECTION_NAME,
    ];
    const timeStampPath = `${momentNow().format("YYYY-MM-DD")}`;
    return backupFirestore(collectionIds, timeStampPath);
  });

// ----------------------------------------------------------------------------------------------------
// END FIRESTORE
// ----------------------------------------------------------------------------------------------------
