const COLLECTIONS = require('../constants/collections');
const checklistModel = require('./childnodes/checklist');
const contextModel = require('./childnodes/context');
const contactModel = require('./childnodes/contact');
const { jsonClone } = require('../helpers');

const calendar = {
    id: "",
    ID: "",
    title: "",
    content: "",
    thumbnail: "",
    
    date: "",
    modified: "",
    start: "",
    end: "",
    allDay: false,
    
    collection: COLLECTIONS.CALENDAR_COLLECTION_NAME,
    post_type: "agenda",

    contact: json<PERSON><PERSON>(contactModel),
    
    config: {
        color: "",
        notification: false,
        resourceEditable: false,
        removable: true,
        editable: true,
        allDay: false,
        stick: false,
    },

    deal: "",
    owner: "",
    contactId: "",
    stores: [],
    
    notifications: [],
    context: json<PERSON>lone(contextModel),
    checklist: json<PERSON>lone(checklistModel),
    tasks: {},
    tasklists: [],
}

module.exports = calendar